import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter } from 'expo-router';
import { Reservation, Produce } from '@/types';
import Card from '@/components/ui/Card';
import Badge from '@/components/ui/Badge';
import Colors from '@/constants/colors';
import { mockProduce } from '@/mocks/produce';
import { mockUsers } from '@/mocks/users';

interface ReservationCardProps {
  reservation: Reservation;
  showFarmer?: boolean;
  showBuyer?: boolean;
}

const ReservationCard: React.FC<ReservationCardProps> = ({
  reservation,
  showFarmer = false,
  showBuyer = false,
}) => {
  const router = useRouter();
  const produce = mockProduce.find(p => p.id === reservation.produceId) as Produce;
  const farmer = mockUsers.find(u => u.id === reservation.farmerId);
  const buyer = mockUsers.find(u => u.id === reservation.buyerId);
  
  if (!produce) return null;
  
  const getStatusBadge = () => {
    switch (reservation.status) {
      case 'pending':
        return <Badge label="Pending" variant="warning" />;
      case 'accepted':
        return <Badge label="Accepted" variant="info" />;
      case 'completed':
        return <Badge label="Completed" variant="success" />;
      case 'rejected':
        return <Badge label="Rejected" variant="error" />;
      case 'cancelled':
        return <Badge label="Cancelled" variant="error" />;
      default:
        return null;
    }
  };
  
  const handlePress = () => {
    router.push(`/reservations/${reservation.id}`);
  };

  return (
    <TouchableOpacity onPress={handlePress} activeOpacity={0.8}>
      <Card style={styles.container} variant="elevated">
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={1}>{produce.title}</Text>
          {getStatusBadge()}
        </View>
        
        <View style={styles.details}>
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Quantity:</Text>
            <Text style={styles.detailValue}>{reservation.quantity} {produce.unit}</Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Price:</Text>
            <Text style={styles.detailValue}>
              ${(reservation.proposedPrice || produce.price).toFixed(2)} / {produce.unit}
            </Text>
          </View>
          
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Total:</Text>
            <Text style={styles.detailValueHighlight}>
              ${((reservation.proposedPrice || produce.price) * reservation.quantity).toFixed(2)}
            </Text>
          </View>
          
          {reservation.deliveryPreference && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Delivery:</Text>
              <Text style={styles.detailValue}>
                {reservation.deliveryPreference.charAt(0).toUpperCase() + 
                 reservation.deliveryPreference.slice(1)}
              </Text>
            </View>
          )}
          
          {showFarmer && farmer && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Farmer:</Text>
              <Text style={styles.detailValue}>{farmer.name}</Text>
            </View>
          )}
          
          {showBuyer && buyer && (
            <View style={styles.detailRow}>
              <Text style={styles.detailLabel}>Buyer:</Text>
              <Text style={styles.detailValue}>{buyer.name}</Text>
            </View>
          )}
        </View>
        
        <View style={styles.footer}>
          <Text style={styles.date}>
            Created: {new Date(reservation.createdAt).toLocaleDateString()}
          </Text>
          {reservation.status === 'accepted' && (
            <Text style={styles.harvestDate}>
              Harvest: {new Date(produce.harvestDate).toLocaleDateString()}
            </Text>
          )}
        </View>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
    marginRight: 8,
  },
  details: {
    marginBottom: 12,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 8,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textLight,
  },
  detailValue: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '500',
  },
  detailValueHighlight: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '700',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 12,
  },
  date: {
    fontSize: 12,
    color: Colors.textLight,
  },
  harvestDate: {
    fontSize: 12,
    color: Colors.success,
    fontWeight: '500',
  },
});

export default ReservationCard;