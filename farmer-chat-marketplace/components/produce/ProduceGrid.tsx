import React from 'react';
import { View, StyleSheet, FlatList, Text } from 'react-native';
import { Produce } from '@/types';
import ProduceCard from './ProduceCard';
import Colors from '@/constants/colors';

interface ProduceGridProps {
  produces: Produce[];
  loading?: boolean;
  onRefresh?: () => void;
  refreshing?: boolean;
}

const ProduceGrid: React.FC<ProduceGridProps> = ({
  produces,
  loading = false,
  onRefresh,
  refreshing = false,
}) => {
  if (loading && produces.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>Loading produce...</Text>
      </View>
    );
  }

  if (produces.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>No produce found</Text>
        <Text style={styles.emptySubtext}>Try changing your search or filters</Text>
      </View>
    );
  }

  return (
    <FlatList
      data={produces}
      renderItem={({ item }) => <ProduceCard produce={item} />}
      keyExtractor={(item) => item.id}
      contentContainerStyle={styles.container}
      numColumns={1}
      showsVerticalScrollIndicator={false}
      onRefresh={onRefresh}
      refreshing={refreshing}
    />
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    textAlign: 'center',
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: 'center',
    marginTop: 8,
  },
});

export default ProduceGrid;