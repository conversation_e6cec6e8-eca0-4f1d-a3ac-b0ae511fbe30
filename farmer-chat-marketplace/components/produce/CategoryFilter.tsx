import React from 'react';
import { ScrollView, TouchableOpacity, Text, StyleSheet } from 'react-native';
import Colors from '@/constants/colors';
import { ProduceCategory } from '@/types';

interface CategoryFilterProps {
  selectedCategory: ProduceCategory | 'all';
  onSelectCategory: (category: ProduceCategory | 'all') => void;
}

const categories: { label: string; value: ProduceCategory | 'all' }[] = [
  { label: 'All', value: 'all' },
  { label: 'Vegetables', value: 'vegetables' },
  { label: 'Fruits', value: 'fruits' },
  { label: 'Grains', value: 'grains' },
  { label: 'Dairy', value: 'dairy' },
  { label: 'Meat', value: 'meat' },
  { label: 'Poultry', value: 'poultry' },
  { label: 'Other', value: 'other' },
];

const CategoryFilter: React.FC<CategoryFilterProps> = ({
  selectedCategory,
  onSelectCategory,
}) => {
  return (
    <ScrollView
      horizontal
      showsHorizontalScrollIndicator={false}
      contentContainerStyle={styles.container}
      style={styles.scrollView}
    >
      {categories.map((category) => (
        <TouchableOpacity
          key={category.label}
          style={[
            styles.categoryButton,
            selectedCategory === category.value && styles.selectedCategory,
          ]}
          onPress={() => onSelectCategory(category.value)}
          activeOpacity={0.7}
        >
          <Text
            style={[
              styles.categoryText,
              selectedCategory === category.value && styles.selectedCategoryText,
            ]}
          >
            {category.label}
          </Text>
        </TouchableOpacity>
      ))}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  scrollView: {
    flexGrow: 0,
  },
  container: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    gap: 8,
  },
  categoryButton: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 24,
    backgroundColor: Colors.inputBackground,
    marginRight: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  categoryText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
  },
  selectedCategory: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  selectedCategoryText: {
    color: Colors.white,
  },
});

export default CategoryFilter;