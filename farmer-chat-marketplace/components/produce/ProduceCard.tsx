import React from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Dimensions, Platform } from 'react-native';
import { Image } from 'expo-image';
import { useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';
import { Produce } from '@/types';
import Colors from '@/constants/colors';
import Badge from '@/components/ui/Badge';
import Avatar from '@/components/ui/Avatar';
import { mockUsers } from '@/mocks/users';
import { Star, Clock } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface ProduceCardProps {
  produce: Produce;
  compact?: boolean;
}

const ProduceCard: React.FC<ProduceCardProps> = ({ produce, compact = false }) => {
  const router = useRouter();
  const farmer = mockUsers.find(user => user.id === produce.farmerId);
  
  const handlePress = () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    router.push(`/marketplace/${produce.id}`);
  };
  
  // Calculate days until harvest
  const daysUntilHarvest = () => {
    const today = new Date();
    const harvestDate = new Date(produce.harvestDate);
    const diffTime = harvestDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };
  
  const days = daysUntilHarvest();
  const isHarvestSoon = days <= 7 && days > 0;
  const isHarvestReady = days <= 0;

  if (compact) {
    return (
      <TouchableOpacity 
        style={styles.compactContainer} 
        onPress={handlePress}
        activeOpacity={0.8}
      >
        <Image
          source={{ uri: produce.images[0] }}
          style={styles.compactImage}
          contentFit="cover"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.7)']}
          style={styles.compactGradient}
        />
        <View style={styles.compactBadgeContainer}>
          <Badge 
            label={produce.growingPractice === 'organic' ? 'Organic' : 'Conventional'} 
            variant={produce.growingPractice === 'organic' ? 'success' : 'default'}
          />
        </View>
        <View style={styles.compactContent}>
          <Text style={styles.compactTitle} numberOfLines={1}>{produce.title}</Text>
          <Text style={styles.compactPrice}>${produce.price.toFixed(2)} / {produce.unit}</Text>
        </View>
      </TouchableOpacity>
    );
  }

  return (
    <TouchableOpacity 
      style={styles.container} 
      onPress={handlePress}
      activeOpacity={0.8}
    >
      <View style={styles.imageWrapper}>
        <Image
          source={{ uri: produce.images[0] }}
          style={styles.image}
          contentFit="cover"
        />
        <LinearGradient
          colors={['transparent', 'rgba(0,0,0,0.5)']}
          style={styles.imageGradient}
        />
        <View style={styles.badgeContainer}>
          <Badge 
            label={produce.category.charAt(0).toUpperCase() + produce.category.slice(1)} 
            variant="primary"
          />
        </View>
        {isHarvestSoon && (
          <View style={styles.harvestBadge}>
            <Clock size={12} color={Colors.warning} />
            <Text style={styles.harvestBadgeText}>Harvest in {days} days</Text>
          </View>
        )}
        {isHarvestReady && (
          <View style={[styles.harvestBadge, styles.harvestReadyBadge]}>
            <Clock size={12} color={Colors.success} />
            <Text style={[styles.harvestBadgeText, styles.harvestReadyText]}>Ready to harvest</Text>
          </View>
        )}
      </View>
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.title} numberOfLines={2}>{produce.title}</Text>
          <Badge 
            label={produce.growingPractice === 'organic' ? 'Organic' : 'Conventional'} 
            variant={produce.growingPractice === 'organic' ? 'success' : 'default'}
          />
        </View>
        <Text style={styles.price}>${produce.price.toFixed(2)} / {produce.unit}</Text>
        <Text style={styles.quantity}>Available: {produce.quantity} {produce.unit}</Text>
        <View style={styles.footer}>
          {farmer && (
            <View style={styles.farmer}>
              <Avatar source={farmer.avatar} name={farmer.name} size="sm" verified={farmer.verified} />
              <View style={styles.farmerInfo}>
                <Text style={styles.farmerName} numberOfLines={1}>{farmer.name}</Text>
                <View style={styles.ratingContainer}>
                  <Star size={12} color={Colors.warning} fill={Colors.warning} />
                  <Text style={styles.rating}>{farmer.rating.toFixed(1)}</Text>
                </View>
              </View>
            </View>
          )}
          <Text style={styles.harvest}>Harvest: {new Date(produce.harvestDate).toLocaleDateString()}</Text>
        </View>
      </View>
    </TouchableOpacity>
  );
};

const { width } = Dimensions.get('window');
const cardWidth = width / 2 - 24;

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 3,
    marginBottom: 16,
  },
  imageWrapper: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 160,
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  badgeContainer: {
    position: 'absolute',
    top: 12,
    left: 12,
  },
  harvestBadge: {
    position: 'absolute',
    bottom: 12,
    right: 12,
    backgroundColor: Colors.warning + '20',
    borderRadius: 16,
    paddingHorizontal: 8,
    paddingVertical: 4,
    flexDirection: 'row',
    alignItems: 'center',
  },
  harvestReadyBadge: {
    backgroundColor: Colors.success + '20',
  },
  harvestBadgeText: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.warning,
    marginLeft: 4,
  },
  harvestReadyText: {
    color: Colors.success,
  },
  content: {
    padding: 12,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  title: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
    flex: 1,
    marginRight: 8,
  },
  price: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.primary,
    marginBottom: 4,
  },
  quantity: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginTop: 4,
  },
  farmer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  farmerInfo: {
    marginLeft: 8,
    flex: 1,
  },
  farmerName: {
    fontSize: 12,
    color: Colors.text,
    fontWeight: '500',
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 2,
  },
  rating: {
    fontSize: 12,
    color: Colors.warning,
    fontWeight: '600',
    marginLeft: 2,
  },
  harvest: {
    fontSize: 12,
    color: Colors.textLight,
  },
  // Compact styles
  compactContainer: {
    width: cardWidth,
    backgroundColor: Colors.white,
    borderRadius: 12,
    overflow: 'hidden',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: 12,
    position: 'relative',
  },
  compactImage: {
    width: '100%',
    height: 120,
  },
  compactGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 60,
  },
  compactBadgeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
  },
  compactContent: {
    padding: 8,
  },
  compactTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  compactPrice: {
    fontSize: 14,
    fontWeight: '700',
    color: Colors.primary,
  },
});

export default ProduceCard;