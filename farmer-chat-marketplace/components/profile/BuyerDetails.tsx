import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { BuyerProfile } from '@/types';
import Card from '@/components/ui/Card';
import Colors from '@/constants/colors';
import { MapPin, Building2, CreditCard } from 'lucide-react-native';

interface BuyerDetailsProps {
  buyer: BuyerProfile;
}

const BuyerDetails: React.FC<BuyerDetailsProps> = ({ buyer }) => {
  return (
    <Card style={styles.container}>
      <Text style={styles.sectionTitle}>Business Details</Text>
      
      {buyer.location && (
        <View style={styles.detailItem}>
          <MapPin size={20} color={Colors.secondary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Location</Text>
            <Text style={styles.detailValue}>{buyer.location.address}</Text>
          </View>
        </View>
      )}
      
      {buyer.businessType && (
        <View style={styles.detailItem}>
          <Building2 size={20} color={Colors.secondary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Business Type</Text>
            <Text style={styles.detailValue}>{buyer.businessType}</Text>
          </View>
        </View>
      )}
      
      {buyer.businessScale && (
        <View style={styles.detailItem}>
          <Building2 size={20} color={Colors.secondary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Business Scale</Text>
            <Text style={styles.detailValue}>{buyer.businessScale}</Text>
          </View>
        </View>
      )}
      
      {buyer.preferredPaymentMethods && buyer.preferredPaymentMethods.length > 0 && (
        <View style={styles.detailItem}>
          <CreditCard size={20} color={Colors.secondary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Preferred Payment Methods</Text>
            <Text style={styles.detailValue}>
              {buyer.preferredPaymentMethods.map(method => 
                method.charAt(0).toUpperCase() + method.slice(1)
              ).join(', ')}
            </Text>
          </View>
        </View>
      )}
      
      {buyer.purchasePreferences && buyer.purchasePreferences.length > 0 && (
        <View style={styles.preferencesSection}>
          <Text style={styles.detailLabel}>Purchase Preferences</Text>
          <View style={styles.preferencesList}>
            {buyer.purchasePreferences.map((preference, index) => (
              <View key={index} style={styles.preferenceItem}>
                <Text style={styles.preferenceText}>
                  {preference.charAt(0).toUpperCase() + preference.slice(1)}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  icon: {
    marginRight: 12,
    marginTop: 2,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.text,
  },
  preferencesSection: {
    marginTop: 8,
  },
  preferencesList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  preferenceItem: {
    backgroundColor: Colors.secondary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  preferenceText: {
    color: Colors.secondary,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default BuyerDetails;