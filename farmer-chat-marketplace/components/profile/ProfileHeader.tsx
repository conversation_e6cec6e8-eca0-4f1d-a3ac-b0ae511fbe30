import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { User } from '@/types';
import Avatar from '@/components/ui/Avatar';
import Badge from '@/components/ui/Badge';
import Colors from '@/constants/colors';

interface ProfileHeaderProps {
  user: User;
}

const ProfileHeader: React.FC<ProfileHeaderProps> = ({ user }) => {
  return (
    <View style={styles.container}>
      <View style={styles.avatarContainer}>
        <Avatar 
          source={user.avatar} 
          name={user.name} 
          size="xl" 
          verified={user.verified} 
        />
      </View>
      <Text style={styles.name}>{user.name}</Text>
      <View style={styles.badgeContainer}>
        <Badge 
          label={user.type === 'farmer' ? 'Farmer' : 'Buyer'} 
          variant={user.type === 'farmer' ? 'primary' : 'info'} 
        />
        {user.verified && (
          <Badge label="Verified" variant="success" style={styles.badge} />
        )}
      </View>
      <View style={styles.statsContainer}>
        <View style={styles.statItem}>
          <Text style={styles.statValue}>{user.rating.toFixed(1)}</Text>
          <Text style={styles.statLabel}>Rating</Text>
        </View>
        <View style={styles.divider} />
        <View style={styles.statItem}>
          <Text style={styles.statValue}>
            {new Date(user.createdAt).toLocaleDateString([], { year: 'numeric', month: 'short' })}
          </Text>
          <Text style={styles.statLabel}>Joined</Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  avatarContainer: {
    marginBottom: 16,
  },
  name: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
  },
  badgeContainer: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  badge: {
    marginLeft: 8,
  },
  statsContainer: {
    flexDirection: 'row',
    width: '100%',
    justifyContent: 'space-around',
    paddingHorizontal: 16,
  },
  statItem: {
    alignItems: 'center',
  },
  statValue: {
    fontSize: 18,
    fontWeight: '700',
    color: Colors.text,
  },
  statLabel: {
    fontSize: 14,
    color: Colors.textLight,
    marginTop: 4,
  },
  divider: {
    width: 1,
    height: '100%',
    backgroundColor: Colors.border,
  },
});

export default ProfileHeader;