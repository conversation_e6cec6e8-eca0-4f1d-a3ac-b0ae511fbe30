import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { FarmerProfile } from '@/types';
import Card from '@/components/ui/Card';
import Colors from '@/constants/colors';
import { MapPin, Wheat, Ruler } from 'lucide-react-native';

interface FarmerDetailsProps {
  farmer: FarmerProfile;
}

const FarmerDetails: React.FC<FarmerDetailsProps> = ({ farmer }) => {
  return (
    <Card style={styles.container}>
      <Text style={styles.sectionTitle}>Farm Details</Text>
      
      {farmer.location && (
        <View style={styles.detailItem}>
          <MapPin size={20} color={Colors.primary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Location</Text>
            <Text style={styles.detailValue}>{farmer.location.address}</Text>
          </View>
        </View>
      )}
      
      {farmer.farmType && (
        <View style={styles.detailItem}>
          <Wheat size={20} color={Colors.primary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Farm Type</Text>
            <Text style={styles.detailValue}>{farmer.farmType}</Text>
          </View>
        </View>
      )}
      
      {farmer.farmSize && (
        <View style={styles.detailItem}>
          <Ruler size={20} color={Colors.primary} style={styles.icon} />
          <View style={styles.detailContent}>
            <Text style={styles.detailLabel}>Farm Size</Text>
            <Text style={styles.detailValue}>{farmer.farmSize}</Text>
          </View>
        </View>
      )}
      
      {farmer.crops && farmer.crops.length > 0 && (
        <View style={styles.cropsSection}>
          <Text style={styles.detailLabel}>Crops Grown</Text>
          <View style={styles.cropsList}>
            {farmer.crops.map((crop, index) => (
              <View key={index} style={styles.cropItem}>
                <Text style={styles.cropText}>
                  {crop.charAt(0).toUpperCase() + crop.slice(1)}
                </Text>
              </View>
            ))}
          </View>
        </View>
      )}
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 16,
  },
  icon: {
    marginRight: 12,
    marginTop: 2,
  },
  detailContent: {
    flex: 1,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.text,
  },
  cropsSection: {
    marginTop: 8,
  },
  cropsList: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 8,
  },
  cropItem: {
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 8,
    marginBottom: 8,
  },
  cropText: {
    color: Colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
});

export default FarmerDetails;