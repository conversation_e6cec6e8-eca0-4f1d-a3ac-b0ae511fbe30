import React from 'react';
import { View, Text, StyleSheet, StyleProp, ViewStyle, TextStyle } from 'react-native';
import Colors from '@/constants/colors';

type BadgeVariant = 'default' | 'primary' | 'success' | 'warning' | 'error' | 'info';

interface BadgeProps {
  label: string;
  variant?: BadgeVariant;
  style?: StyleProp<ViewStyle>;
  textStyle?: StyleProp<TextStyle>;
}

export const Badge: React.FC<BadgeProps> = ({
  label,
  variant = 'default',
  style,
  textStyle,
}) => {
  return (
    <View style={[styles.badge, styles[variant], style]}>
      <Text style={[styles.text, styles[`${variant}Text`], textStyle]}>{label}</Text>
    </View>
  );
};

const styles = StyleSheet.create({
  badge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
    alignSelf: 'flex-start',
  },
  text: {
    fontSize: 12,
    fontWeight: '600',
  },
  // Variants
  default: {
    backgroundColor: Colors.border,
  },
  defaultText: {
    color: Colors.text,
  },
  primary: {
    backgroundColor: Colors.primary + '20', // 20% opacity
  },
  primaryText: {
    color: Colors.primary,
  },
  success: {
    backgroundColor: Colors.success + '20',
  },
  successText: {
    color: Colors.success,
  },
  warning: {
    backgroundColor: Colors.warning + '20',
  },
  warningText: {
    color: Colors.warning,
  },
  error: {
    backgroundColor: Colors.error + '20',
  },
  errorText: {
    color: Colors.error,
  },
  info: {
    backgroundColor: Colors.info + '20',
  },
  infoText: {
    color: Colors.info,
  },
});

export default Badge;