import React, { useState } from 'react';
import { View, StyleSheet, TouchableOpacity, ViewStyle } from 'react-native';
import { Star } from 'lucide-react-native';
import Colors from '@/constants/colors';

interface RatingProps {
  initialValue?: number;
  maxRating?: number;
  size?: number;
  readonly?: boolean;
  onRatingChange?: (rating: number) => void;
  style?: ViewStyle;
}

const Rating: React.FC<RatingProps> = ({
  initialValue = 0,
  maxRating = 5,
  size = 24,
  readonly = false,
  onRatingChange,
  style,
}) => {
  const [rating, setRating] = useState(initialValue);

  const handlePress = (selectedRating: number) => {
    if (readonly) return;
    
    setRating(selectedRating);
    if (onRatingChange) {
      onRatingChange(selectedRating);
    }
  };

  return (
    <View style={[styles.container, style]}>
      {[...Array(maxRating)].map((_, index) => {
        const starValue = index + 1;
        const filled = starValue <= rating;
        
        return (
          <TouchableOpacity
            key={index}
            onPress={() => handlePress(starValue)}
            disabled={readonly}
            style={styles.starContainer}
          >
            <Star
              size={size}
              color={Colors.warning}
              fill={filled ? Colors.warning : 'transparent'}
            />
          </TouchableOpacity>
        );
      })}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
  },
  starContainer: {
    padding: 2,
  },
});

export default Rating;