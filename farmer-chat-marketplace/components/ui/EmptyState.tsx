import React from 'react';
import { View, Text, StyleSheet, ViewStyle, TextStyle } from 'react-native';
import Colors from '@/constants/colors';
import Button from './Button';

interface EmptyStateProps {
  title: string;
  description?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  buttonTitle?: string;
  actionTitle?: string;
  onButtonPress?: () => void;
  onAction?: () => void;
  containerStyle?: ViewStyle;
  titleStyle?: TextStyle;
  descriptionStyle?: TextStyle;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  subtitle,
  icon,
  buttonTitle,
  actionTitle,
  onButtonPress,
  onAction,
  containerStyle,
  titleStyle,
  descriptionStyle,
}) => {
  const displayDescription = subtitle || description;
  const displayButtonTitle = actionTitle || buttonTitle;
  const handleButtonPress = onAction || onButtonPress;

  return (
    <View style={[styles.container, containerStyle]}>
      {icon && <View style={styles.iconContainer}>{icon}</View>}
      <Text style={[styles.title, titleStyle]}>{title}</Text>
      {displayDescription && (
        <Text style={[styles.description, descriptionStyle]}>{displayDescription}</Text>
      )}
      {displayButtonTitle && handleButtonPress && (
        <Button
          title={displayButtonTitle}
          onPress={handleButtonPress}
          style={styles.button}
        />
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  iconContainer: {
    marginBottom: 16,
  },
  title: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    textAlign: 'center',
    marginBottom: 8,
  },
  description: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
    lineHeight: 22,
  },
  button: {
    minWidth: 200,
  },
});

export default EmptyState;