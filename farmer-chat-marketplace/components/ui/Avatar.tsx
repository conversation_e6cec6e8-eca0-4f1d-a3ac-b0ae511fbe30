import React from 'react';
import { View, Text, StyleSheet, StyleProp, ViewStyle } from 'react-native';
import { Image } from 'expo-image';
import Colors from '@/constants/colors';

type AvatarSize = 'sm' | 'md' | 'lg' | 'xl';

interface AvatarProps {
  source?: string;
  name?: string;
  size?: AvatarSize;
  style?: StyleProp<ViewStyle>;
  verified?: boolean;
}

export const Avatar: React.FC<AvatarProps> = ({
  source,
  name,
  size = 'md',
  style,
  verified = false,
}) => {
  const getInitials = (name?: string) => {
    if (!name) return '?';
    return name
      .split(' ')
      .map(part => part[0])
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  return (
    <View style={[styles.container, style]}>
      {source ? (
        <Image
          source={{ uri: source }}
          style={[styles.avatar, styles[size]]}
          contentFit="cover"
        />
      ) : (
        <View style={[styles.avatar, styles[size], styles.placeholder]}>
          <Text style={[styles.initials, styles[`${size}Text`]]}>
            {getInitials(name)}
          </Text>
        </View>
      )}
      {verified && (
        <View style={[styles.badge, styles[`${size}Badge`]]}>
          <Text style={styles.badgeText}>✓</Text>
        </View>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    position: 'relative',
  },
  avatar: {
    borderRadius: 100,
    overflow: 'hidden',
  },
  placeholder: {
    backgroundColor: Colors.primary + '30',
    alignItems: 'center',
    justifyContent: 'center',
  },
  initials: {
    color: Colors.primary,
    fontWeight: 'bold',
  },
  badge: {
    position: 'absolute',
    backgroundColor: Colors.success,
    borderRadius: 100,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    borderColor: Colors.white,
  },
  badgeText: {
    color: Colors.white,
    fontWeight: 'bold',
  },
  // Sizes
  sm: {
    width: 32,
    height: 32,
  },
  md: {
    width: 40,
    height: 40,
  },
  lg: {
    width: 56,
    height: 56,
  },
  xl: {
    width: 80,
    height: 80,
  },
  smText: {
    fontSize: 12,
  },
  mdText: {
    fontSize: 16,
  },
  lgText: {
    fontSize: 20,
  },
  xlText: {
    fontSize: 28,
  },
  smBadge: {
    width: 12,
    height: 12,
    right: 0,
    bottom: 0,
    borderWidth: 1,
  },
  mdBadge: {
    width: 14,
    height: 14,
    right: 0,
    bottom: 0,
    borderWidth: 1.5,
  },
  lgBadge: {
    width: 18,
    height: 18,
    right: 0,
    bottom: 0,
    borderWidth: 2,
  },
  xlBadge: {
    width: 24,
    height: 24,
    right: 0,
    bottom: 0,
    borderWidth: 2.5,
  },
});

export default Avatar;