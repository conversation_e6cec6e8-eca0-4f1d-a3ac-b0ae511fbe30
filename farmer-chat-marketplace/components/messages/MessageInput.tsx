import React, { useState } from 'react';
import { View, TextInput, StyleSheet, TouchableOpacity, Keyboard, Platform } from 'react-native';
import { Send, Paperclip, Camera, Mic } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import Colors from '@/constants/colors';

interface MessageInputProps {
  onSend: (message: string) => void;
  placeholder?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({
  onSend,
  placeholder = 'Type a message...',
}) => {
  const [message, setMessage] = useState('');
  const [isRecording, setIsRecording] = useState(false);

  const handleSend = () => {
    if (message.trim()) {
      if (Platform.OS !== 'web') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
      onSend(message.trim());
      setMessage('');
      Keyboard.dismiss();
    }
  };
  
  const handleAttachment = () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    // Attachment functionality would go here
  };
  
  const handleCamera = () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    // Camera functionality would go here
  };
  
  const toggleRecording = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
    setIsRecording(!isRecording);
    // Voice recording functionality would go here
  };

  return (
    <View style={styles.container}>
      <View style={styles.inputContainer}>
        <TouchableOpacity style={styles.attachButton} onPress={handleAttachment}>
          <Paperclip size={20} color={Colors.textLight} />
        </TouchableOpacity>
        
        <TextInput
          style={styles.input}
          placeholder={placeholder}
          placeholderTextColor={Colors.textLight}
          value={message}
          onChangeText={setMessage}
          multiline
          maxLength={500}
        />
        
        {!message.trim() ? (
          <View style={styles.mediaButtons}>
            <TouchableOpacity style={styles.mediaButton} onPress={handleCamera}>
              <Camera size={20} color={Colors.textLight} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={[styles.mediaButton, isRecording && styles.recordingButton]} 
              onPress={toggleRecording}
            >
              <Mic size={20} color={isRecording ? Colors.white : Colors.textLight} />
            </TouchableOpacity>
          </View>
        ) : (
          <TouchableOpacity
            style={styles.sendButton}
            onPress={handleSend}
          >
            <Send size={20} color={Colors.white} />
          </TouchableOpacity>
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    padding: 12,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    backgroundColor: Colors.white,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  attachButton: {
    padding: 8,
  },
  input: {
    flex: 1,
    backgroundColor: Colors.card,
    borderRadius: 20,
    paddingHorizontal: 16,
    paddingVertical: 10,
    maxHeight: 100,
    fontSize: 16,
    color: Colors.text,
  },
  mediaButtons: {
    flexDirection: 'row',
    marginLeft: 8,
  },
  mediaButton: {
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  recordingButton: {
    backgroundColor: Colors.error,
  },
  sendButton: {
    backgroundColor: Colors.primary,
    borderRadius: 20,
    width: 40,
    height: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
});

export default MessageInput;