import { create } from 'zustand';
import { Reservation } from '@/types';
import { mockReservations } from '@/mocks/reservations';

interface ReservationState {
  reservations: Reservation[];
  isLoading: boolean;
  error: string | null;
  
  fetchReservations: () => Promise<void>;
  getReservationById: (id: string) => Reservation | undefined;
  getReservationsByBuyerId: (buyerId: string) => Reservation[];
  getReservationsByFarmerId: (farmerId: string) => Reservation[];
  getReservationsByProduceId: (produceId: string) => Reservation[];
  createReservation: (reservation: Omit<Reservation, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateReservationStatus: (id: string, status: Reservation['status']) => Promise<void>;
}

export const useReservationStore = create<ReservationState>((set, get) => ({
  reservations: [],
  isLoading: false,
  error: null,
  
  fetchReservations: async () => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set({ reservations: mockReservations, isLoading: false });
    } catch (error) {
      set({ error: "Failed to fetch reservations", isLoading: false });
    }
  },
  
  getReservationById: (id: string) => {
    return get().reservations.find(reservation => reservation.id === id);
  },
  
  getReservationsByBuyerId: (buyerId: string) => {
    return get().reservations.filter(reservation => reservation.buyerId === buyerId);
  },
  
  getReservationsByFarmerId: (farmerId: string) => {
    return get().reservations.filter(reservation => reservation.farmerId === farmerId);
  },
  
  getReservationsByProduceId: (produceId: string) => {
    return get().reservations.filter(reservation => reservation.produceId === produceId);
  },
  
  createReservation: async (reservationData) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newReservation: Reservation = {
        id: `reservation-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...reservationData
      };
      
      set(state => ({
        reservations: [...state.reservations, newReservation],
        isLoading: false
      }));
    } catch (error) {
      set({ error: "Failed to create reservation", isLoading: false });
    }
  },
  
  updateReservationStatus: async (id: string, status: Reservation['status']) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => ({
        reservations: state.reservations.map(reservation => 
          reservation.id === id 
            ? { ...reservation, status, updatedAt: new Date().toISOString() } 
            : reservation
        ),
        isLoading: false
      }));
    } catch (error) {
      set({ error: "Failed to update reservation status", isLoading: false });
    }
  }
}));