import { create } from 'zustand';
import { Produce } from '@/types';
import { mockProduce } from '@/mocks/produce';

interface ProduceState {
  produces: Produce[];
  filteredProduces: Produce[];
  selectedCategory: string | null;
  searchQuery: string;
  isLoading: boolean;
  error: string | null;
  
  fetchProduces: () => Promise<void>;
  getProduceById: (id: string) => Produce | undefined;
  getProducesByFarmerId: (farmerId: string) => Produce[];
  filterByCategory: (category: string | null) => void;
  searchProduces: (query: string) => void;
  addProduce: (produce: Omit<Produce, 'id' | 'createdAt' | 'updatedAt'>) => Promise<void>;
  updateProduce: (id: string, updates: Partial<Produce>) => Promise<void>;
  deleteProduce: (id: string) => Promise<void>;
}

export const useProduceStore = create<ProduceState>((set, get) => ({
  produces: [],
  filteredProduces: [],
  selectedCategory: null,
  searchQuery: '',
  isLoading: false,
  error: null,
  
  fetchProduces: async () => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set({ 
        produces: mockProduce, 
        filteredProduces: mockProduce,
        isLoading: false 
      });
    } catch (error) {
      set({ error: "Failed to fetch produces", isLoading: false });
    }
  },
  
  getProduceById: (id: string) => {
    return get().produces.find(produce => produce.id === id);
  },
  
  getProducesByFarmerId: (farmerId: string) => {
    return get().produces.filter(produce => produce.farmerId === farmerId);
  },
  
  filterByCategory: (category: string | null) => {
    const { produces, searchQuery } = get();
    set({ selectedCategory: category });
    
    let filtered = produces;
    
    if (category) {
      filtered = filtered.filter(produce => produce.category === category);
    }
    
    if (searchQuery) {
      filtered = filtered.filter(produce => 
        produce.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        produce.description.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }
    
    set({ filteredProduces: filtered });
  },
  
  searchProduces: (query: string) => {
    const { produces, selectedCategory } = get();
    set({ searchQuery: query });
    
    let filtered = produces;
    
    if (selectedCategory) {
      filtered = filtered.filter(produce => produce.category === selectedCategory);
    }
    
    if (query) {
      filtered = filtered.filter(produce => 
        produce.title.toLowerCase().includes(query.toLowerCase()) ||
        produce.description.toLowerCase().includes(query.toLowerCase())
      );
    }
    
    set({ filteredProduces: filtered });
  },
  
  addProduce: async (produce) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newProduce: Produce = {
        id: `produce-${Date.now()}`,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        ...produce
      };
      
      set(state => ({
        produces: [...state.produces, newProduce],
        filteredProduces: [...state.filteredProduces, newProduce],
        isLoading: false
      }));
    } catch (error) {
      set({ error: "Failed to add produce", isLoading: false });
    }
  },
  
  updateProduce: async (id, updates) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => {
        const updatedProduces = state.produces.map(produce => 
          produce.id === id 
            ? { ...produce, ...updates, updatedAt: new Date().toISOString() } 
            : produce
        );
        
        const updatedFilteredProduces = state.filteredProduces.map(produce => 
          produce.id === id 
            ? { ...produce, ...updates, updatedAt: new Date().toISOString() } 
            : produce
        );
        
        return {
          produces: updatedProduces,
          filteredProduces: updatedFilteredProduces,
          isLoading: false
        };
      });
    } catch (error) {
      set({ error: "Failed to update produce", isLoading: false });
    }
  },
  
  deleteProduce: async (id) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => ({
        produces: state.produces.filter(produce => produce.id !== id),
        filteredProduces: state.filteredProduces.filter(produce => produce.id !== id),
        isLoading: false
      }));
    } catch (error) {
      set({ error: "Failed to delete produce", isLoading: false });
    }
  }
}));