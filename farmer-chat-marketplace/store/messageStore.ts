import { create } from 'zustand';
import { Message, Conversation } from '@/types';
import { mockMessages, mockConversations } from '@/mocks/messages';

interface MessageState {
  conversations: Conversation[];
  currentConversation: Conversation | null;
  messages: Record<string, Message[]>; // conversationId -> messages
  isLoading: boolean;
  error: string | null;
  
  fetchConversations: () => Promise<void>;
  fetchMessages: (conversationId: string) => Promise<void>;
  sendMessage: (conversationId: string, content: string, senderId: string, receiverId: string) => Promise<void>;
  markAsRead: (conversationId: string) => Promise<void>;
  getUnreadCount: () => number;
  startNewConversation: (participants: string[]) => Promise<string>;
}

export const useMessageStore = create<MessageState>((set, get) => ({
  conversations: [],
  currentConversation: null,
  messages: {},
  isLoading: false,
  error: null,
  
  fetchConversations: async () => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set({ conversations: mockConversations, isLoading: false });
    } catch (error) {
      set({ error: "Failed to fetch conversations", isLoading: false });
    }
  },
  
  fetchMessages: async (conversationId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Find the conversation
      const conversation = get().conversations.find(c => c.id === conversationId);
      if (!conversation) {
        throw new Error("Conversation not found");
      }
      
      // Get messages for this conversation
      const conversationMessages = mockMessages.filter(
        m => conversation.participants.includes(m.senderId) && 
             conversation.participants.includes(m.receiverId)
      ).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      
      set(state => ({
        currentConversation: conversation,
        messages: { ...state.messages, [conversationId]: conversationMessages },
        isLoading: false
      }));
    } catch (error) {
      set({ error: "Failed to fetch messages", isLoading: false });
    }
  },
  
  sendMessage: async (conversationId: string, content: string, senderId: string, receiverId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const newMessage: Message = {
        id: `msg-${Date.now()}`,
        senderId,
        receiverId,
        content,
        timestamp: new Date().toISOString(),
        read: false
      };
      
      set(state => {
        // Update messages
        const conversationMessages = state.messages[conversationId] || [];
        const updatedMessages = {
          ...state.messages,
          [conversationId]: [...conversationMessages, newMessage]
        };
        
        // Update conversation
        const updatedConversations = state.conversations.map(conv => {
          if (conv.id === conversationId) {
            return {
              ...conv,
              lastMessage: newMessage,
              unreadCount: conv.unreadCount + (senderId !== receiverId ? 1 : 0),
              updatedAt: newMessage.timestamp
            };
          }
          return conv;
        });
        
        return {
          messages: updatedMessages,
          conversations: updatedConversations,
          isLoading: false
        };
      });
    } catch (error) {
      set({ error: "Failed to send message", isLoading: false });
    }
  },
  
  markAsRead: async (conversationId: string) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      set(state => {
        // Update conversation unread count
        const updatedConversations = state.conversations.map(conv => {
          if (conv.id === conversationId) {
            return { ...conv, unreadCount: 0 };
          }
          return conv;
        });
        
        // Mark messages as read
        const conversationMessages = state.messages[conversationId] || [];
        const updatedMessages = conversationMessages.map(msg => ({ ...msg, read: true }));
        
        return {
          conversations: updatedConversations,
          messages: { ...state.messages, [conversationId]: updatedMessages },
          isLoading: false
        };
      });
    } catch (error) {
      set({ error: "Failed to mark messages as read", isLoading: false });
    }
  },
  
  getUnreadCount: () => {
    return get().conversations.reduce((total, conv) => total + conv.unreadCount, 0);
  },
  
  startNewConversation: async (participants: string[]) => {
    set({ isLoading: true, error: null });
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Check if conversation already exists
      const existingConversation = get().conversations.find(conv => {
        return participants.every(p => conv.participants.includes(p)) && 
               conv.participants.length === participants.length;
      });
      
      if (existingConversation) {
        set({ isLoading: false });
        return existingConversation.id;
      }
      
      // Create new conversation
      const newConversation: Conversation = {
        id: `conv-${Date.now()}`,
        participants,
        unreadCount: 0,
        updatedAt: new Date().toISOString()
      };
      
      set(state => ({
        conversations: [...state.conversations, newConversation],
        isLoading: false
      }));
      
      return newConversation.id;
    } catch (error) {
      set({ error: "Failed to start new conversation", isLoading: false });
      return '';
    }
  }
}));