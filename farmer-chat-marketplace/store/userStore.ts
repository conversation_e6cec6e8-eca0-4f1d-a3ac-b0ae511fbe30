import { create } from 'zustand';
import { persist, createJSONStorage } from 'zustand/middleware';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { User, FarmerProfile, BuyerProfile } from '@/types';
import { mockUsers } from '@/mocks/users';

interface UserState {
  currentUser: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  register: (userData: Partial<User>, password: string) => Promise<void>;
  updateProfile: (userData: Partial<User>) => Promise<void>;
}

export const useUserStore = create<UserState>()(
  persist(
    (set, get) => ({
      currentUser: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
      
      login: async (email: string, password: string) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Find user in mock data
          const user = mockUsers.find(u => u.email === email);
          
          if (user) {
            set({ currentUser: user, isAuthenticated: true, isLoading: false });
          } else {
            set({ error: "Invalid email or password", isLoading: false });
          }
        } catch (error) {
          set({ error: "Login failed. Please try again.", isLoading: false });
        }
      },
      
      logout: () => {
        set({ currentUser: null, isAuthenticated: false });
      },
      
      register: async (userData: Partial<User>, password: string) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          // Create new user (in a real app, this would be done on the server)
          const newUser: User = {
            id: `user-${Date.now()}`,
            name: userData.name || '',
            email: userData.email || '',
            phone: userData.phone || '',
            type: userData.type || 'buyer',
            verified: false,
            rating: 0,
            createdAt: new Date().toISOString(),
            ...userData
          };
          
          set({ currentUser: newUser, isAuthenticated: true, isLoading: false });
        } catch (error) {
          set({ error: "Registration failed. Please try again.", isLoading: false });
        }
      },
      
      updateProfile: async (userData: Partial<User>) => {
        set({ isLoading: true, error: null });
        try {
          // Simulate API call
          await new Promise(resolve => setTimeout(resolve, 1000));
          
          const currentUser = get().currentUser;
          if (currentUser) {
            const updatedUser = { ...currentUser, ...userData };
            set({ currentUser: updatedUser, isLoading: false });
          } else {
            set({ error: "User not found", isLoading: false });
          }
        } catch (error) {
          set({ error: "Profile update failed. Please try again.", isLoading: false });
        }
      }
    }),
    {
      name: 'user-storage',
      storage: createJSONStorage(() => AsyncStorage)
    }
  )
);