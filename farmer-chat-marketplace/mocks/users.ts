import { FarmerProfile, BuyerProfile } from '@/types';

export const mockFarmers: FarmerProfile[] = [
  {
    id: '1',
    name: '<PERSON>',
    email: "<EMAIL>",
    phone: "+1234567890",
    type: 'farmer',
    location: {
      latitude: 37.7749,
      longitude: -122.4194,
      address: "123 Farm Road, Farmville, CA"
    },
    avatar: "https://images.unsplash.com/photo-1560343776-97e7d202ff0e?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    verified: true,
    rating: 4.8,
    createdAt: "2023-01-15T08:30:00Z",
    farmSize: "25 acres",
    farmType: "Mixed crops",
    crops: ["tomatoes", "lettuce", "carrots"],
  },
  {
    id: '2',
    name: '<PERSON>',
    email: "<EMAIL>",
    phone: "+1987654321",
    type: 'farmer',
    location: {
      latitude: 34.0522,
      longitude: -118.2437,
      address: "456 Orchard Lane, Cropsville, CA"
    },
    avatar: "https://images.unsplash.com/photo-1594608661623-aa0bd3a69799?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    verified: true,
    rating: 4.6,
    createdAt: "2023-02-20T10:15:00Z",
    farmSize: "15 acres",
    farmType: "Organic fruits",
    crops: ["apples", "peaches", "berries"],
  },
  {
    id: '3',
    name: 'Robert Johnson',
    email: "<EMAIL>",
    phone: "+1122334455",
    type: 'farmer',
    location: {
      latitude: 39.7392,
      longitude: -104.9903,
      address: "789 Grain Road, Wheatville, CO"
    },
    avatar: "https://images.unsplash.com/photo-1570295999919-56ceb5ecca61?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    verified: false,
    rating: 4.2,
    createdAt: "2023-03-10T14:45:00Z",
    farmSize: "100 acres",
    farmType: "Grain production",
    crops: ["wheat", "corn", "barley"],
  }
];

export const mockBuyers: BuyerProfile[] = [
  {
    id: '4',
    name: 'Emily Wilson',
    email: "<EMAIL>",
    phone: "+**********",
    type: 'buyer',
    location: {
      latitude: 40.7128,
      longitude: -74.0060,
      address: "321 Market Street, New York, NY"
    },
    avatar: "https://images.unsplash.com/photo-*************-be9c29b29330?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    verified: true,
    rating: 4.9,
    createdAt: "2023-01-25T09:20:00Z",
    businessType: "Restaurant",
    businessScale: "Medium",
    preferredPaymentMethods: ["bank transfer", "credit card"],
    purchasePreferences: ["organic", "locally grown"]
  },
  {
    id: '5',
    name: 'David Chen',
    email: "<EMAIL>",
    phone: "+**********",
    type: 'buyer',
    location: {
      latitude: 37.3382,
      longitude: -121.8863,
      address: "654 Grocery Avenue, San Jose, CA"
    },
    avatar: "https://images.unsplash.com/photo-*************-0a1dd7228f2d?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60",
    verified: true,
    rating: 4.7,
    createdAt: "2023-02-05T11:30:00Z",
    businessType: "Grocery Store",
    businessScale: "Large",
    preferredPaymentMethods: ["bank transfer"],
    purchasePreferences: ["conventional", "bulk quantities"]
  }
];

export const mockUsers = [...mockFarmers, ...mockBuyers];