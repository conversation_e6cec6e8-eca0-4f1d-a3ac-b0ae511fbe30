import { Conversation, Message } from '@/types';

export const mockMessages: Message[] = [
  {
    id: '1',
    senderId: '4',
    receiverId: '1',
    content: "Hello, I'm interested in your tomatoes. Are they still available?",
    timestamp: '2025-06-05T13:30:00Z',
    read: true
  },
  {
    id: '2',
    senderId: '1',
    receiverId: '4',
    content: "Yes, they are still available. How many kg would you like to order?",
    timestamp: '2025-06-05T13:45:00Z',
    read: true
  },
  {
    id: '3',
    senderId: '4',
    receiverId: '1',
    content: "I'd like to order 100kg. Can you deliver to my restaurant in the city?",
    timestamp: '2025-06-05T14:00:00Z',
    read: true
  },
  {
    id: '4',
    senderId: '1',
    receiverId: '4',
    content: "Yes, we can deliver. The delivery fee is $20. Is that okay?",
    timestamp: '2025-06-05T14:15:00Z',
    read: true
  },
  {
    id: '5',
    senderId: '4',
    receiverId: '1',
    content: "That works for me. Can we schedule delivery for next Tuesday?",
    timestamp: '2025-06-05T14:30:00Z',
    read: false
  },
  {
    id: '6',
    senderId: '5',
    receiverId: '2',
    content: "Hi Maria, I'm interested in your peaches. What's the minimum order?",
    timestamp: '2025-06-07T10:30:00Z',
    read: true
  },
  {
    id: '7',
    senderId: '2',
    receiverId: '5',
    content: "Hello David, the minimum order is 40kg. Would you like to place an order?",
    timestamp: '2025-06-07T10:40:00Z',
    read: true
  },
  {
    id: '8',
    senderId: '5',
    receiverId: '2',
    content: "Yes, I'd like to order 200kg. Can we negotiate on the price a bit?",
    timestamp: '2025-06-07T10:45:00Z',
    read: false
  }
];

export const mockConversations: Conversation[] = [
  {
    id: '1',
    participants: ['1', '4'],
    lastMessage: mockMessages[4],
    unreadCount: 1,
    updatedAt: '2025-06-05T14:30:00Z'
  },
  {
    id: '2',
    participants: ['2', '5'],
    lastMessage: mockMessages[7],
    unreadCount: 1,
    updatedAt: '2025-06-07T10:45:00Z'
  }
];