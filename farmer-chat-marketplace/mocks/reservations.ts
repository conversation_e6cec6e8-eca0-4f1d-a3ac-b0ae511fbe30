import { Reservation } from '@/types';

export const mockReservations: Reservation[] = [
  {
    id: '1',
    produceId: '1',
    buyerId: '4',
    farmerId: '1',
    quantity: 100,
    proposedPrice: 2.3,
    status: 'accepted',
    deliveryPreference: 'local delivery',
    paymentTerms: 'payment on delivery',
    createdAt: '2025-06-05T14:30:00Z',
    updatedAt: '2025-06-06T09:15:00Z'
  },
  {
    id: '2',
    produceId: '3',
    buyerId: '5',
    farmerId: '2',
    quantity: 200,
    proposedPrice: 3.5,
    status: 'pending',
    deliveryPreference: 'shipping',
    paymentTerms: 'advance payment',
    createdAt: '2025-06-07T10:45:00Z',
    updatedAt: '2025-06-07T10:45:00Z'
  },
  {
    id: '3',
    produceId: '2',
    buyerId: '4',
    farmerId: '1',
    quantity: 50,
    proposedPrice: 1.1,
    status: 'completed',
    deliveryPreference: 'farm pickup',
    paymentTerms: 'payment on pickup',
    createdAt: '2025-06-02T16:20:00Z',
    updatedAt: '2025-06-08T11:30:00Z'
  },
  {
    id: '4',
    produceId: '4',
    buyerId: '5',
    farmerId: '3',
    quantity: 1000,
    proposedPrice: 0.32,
    status: 'rejected',
    deliveryPreference: 'bulk shipping',
    paymentTerms: 'advance payment',
    createdAt: '2025-06-06T09:10:00Z',
    updatedAt: '2025-06-07T14:25:00Z'
  }
];