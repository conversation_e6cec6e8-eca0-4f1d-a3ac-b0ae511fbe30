// Mobile-first color palette for the Farmer Chat app
export default {
  primary: '#2E7D32', // Deeper green for better mobile contrast
  secondary: '#1976D2', // Stronger blue for mobile
  accent: '#F57C00', // Warmer orange for mobile highlights
  background: '#F8F9FA', // Softer background for mobile
  card: '#FFFFFF',
  surface: '#FFFFFF',
  text: '#1A1A1A', // Darker text for mobile readability
  textLight: '#6B7280', // Better contrast for secondary text
  textMuted: '#9CA3AF',
  border: '#E5E7EB',
  divider: '#F3F4F6',
  success: '#059669',
  error: '#DC2626',
  warning: '#D97706',
  info: '#0284C7',
  white: '#FFFFFF',
  black: '#000000',
  transparent: 'transparent',
  overlay: 'rgba(0, 0, 0, 0.6)',
  
  // Mobile-specific colors
  tabBarBackground: '#FFFFFF',
  headerBackground: '#FFFFFF',
  inputBackground: '#F9FAFB',
  pressedState: 'rgba(46, 125, 50, 0.1)',
  
  // Semantic colors for mobile
  online: '#10B981',
  offline: '#6B7280',
  pending: '#F59E0B',
  confirmed: '#059669',
  cancelled: '#EF4444',
};