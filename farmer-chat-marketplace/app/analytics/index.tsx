import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useProduceStore } from '@/store/produceStore';
import { useReservationStore } from '@/store/reservationStore';
import Colors from '@/constants/colors';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { TrendingUp, DollarSign, ShoppingBag, Users, ArrowLeft } from 'lucide-react-native';

export default function DetailedAnalyticsScreen() {
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { produces, getProducesByFarmerId } = useProduceStore();
  const { reservations, getReservationsByFarmerId, getReservationsByBuyerId } = useReservationStore();
  
  if (!currentUser) {
    return (
      <View style={styles.emptyContainer}>
        <Stack.Screen options={{ headerShown: false }} />
        <Text style={styles.emptyTitle}>Sign in to view analytics</Text>
        <Button 
          title="Sign In" 
          onPress={() => router.push('/auth/login')} 
          style={styles.signInButton}
        />
      </View>
    );
  }
  
  const userProduces = currentUser.type === 'farmer' 
    ? getProducesByFarmerId(currentUser.id)
    : [];
  
  const userReservations = currentUser.type === 'farmer'
    ? getReservationsByFarmerId(currentUser.id)
    : getReservationsByBuyerId(currentUser.id);
  
  // Calculate total revenue/spending
  const totalAmount = userReservations.reduce((sum, reservation) => {
    const produce = produces.find(p => p.id === reservation.produceId);
    if (!produce) return sum;
    
    const price = reservation.proposedPrice || produce.price;
    return sum + (price * reservation.quantity);
  }, 0);
  
  // Calculate completed reservations
  const completedReservations = userReservations.filter(r => r.status === 'completed');
  const completionRate = userReservations.length > 0 
    ? (completedReservations.length / userReservations.length) * 100 
    : 0;
  
  // Mock data for demonstration
  const monthlyData = [
    { month: 'Jan', amount: 1200 },
    { month: 'Feb', amount: 1800 },
    { month: 'Mar', amount: 1500 },
    { month: 'Apr', amount: 2200 },
    { month: 'May', amount: 2800 },
    { month: 'Jun', amount: 2400 },
  ];
  
  const categoryData = [
    { name: 'Vegetables', percentage: 45, color: Colors.primary },
    { name: 'Fruits', percentage: 28, color: Colors.secondary },
    { name: 'Grains', percentage: 17, color: Colors.accent },
    { name: 'Other', percentage: 10, color: Colors.warning },
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Detailed Analytics</Text>
        <View style={styles.headerSpacer} />
      </View>
      
      <View style={styles.statsContainer}>
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <DollarSign size={24} color={Colors.primary} />
          </View>
          <Text style={styles.statTitle}>
            {currentUser.type === 'farmer' ? 'Revenue' : 'Spending'}
          </Text>
          <Text style={styles.statValue}>${totalAmount.toFixed(2)}</Text>
        </Card>
        
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <ShoppingBag size={24} color={Colors.secondary} />
          </View>
          <Text style={styles.statTitle}>Reservations</Text>
          <Text style={styles.statValue}>{userReservations.length}</Text>
        </Card>
        
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <TrendingUp size={24} color={Colors.success} />
          </View>
          <Text style={styles.statTitle}>Completion</Text>
          <Text style={styles.statValue}>{completionRate.toFixed(0)}%</Text>
        </Card>
        
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <Users size={24} color={Colors.warning} />
          </View>
          <Text style={styles.statTitle}>
            {currentUser.type === 'farmer' ? 'Buyers' : 'Farmers'}
          </Text>
          <Text style={styles.statValue}>
            {new Set(userReservations.map(r => 
              currentUser.type === 'farmer' ? r.buyerId : r.farmerId
            )).size}
          </Text>
        </Card>
      </View>
      
      {/* Monthly Trends */}
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>
          {currentUser.type === 'farmer' ? 'Sales by Month' : 'Purchases by Month'}
        </Text>
        <Card style={styles.chartCard} variant="elevated">
          <View style={styles.barChart}>
            {monthlyData.map((item, index) => (
              <View key={item.month} style={styles.barContainer}>
                <View 
                  style={[
                    styles.bar, 
                    { 
                      height: (item.amount / 3000) * 120,
                      backgroundColor: Colors.primary 
                    }
                  ]} 
                />
                <Text style={styles.barLabel}>{item.month}</Text>
                <Text style={styles.barValue}>${item.amount}</Text>
              </View>
            ))}
          </View>
        </Card>
      </View>
      
      {/* Category Breakdown */}
      <View style={styles.chartContainer}>
        <Text style={styles.chartTitle}>
          {currentUser.type === 'farmer' ? 'Sales by Category' : 'Purchases by Category'}
        </Text>
        <Card style={styles.chartCard} variant="elevated">
          <View style={styles.categoryChart}>
            {categoryData.map((item, index) => (
              <View key={item.name} style={styles.categoryItem}>
                <View style={styles.categoryInfo}>
                  <View style={[styles.categoryColor, { backgroundColor: item.color }]} />
                  <Text style={styles.categoryName}>{item.name}</Text>
                </View>
                <Text style={styles.categoryPercentage}>{item.percentage}%</Text>
              </View>
            ))}
          </View>
        </Card>
      </View>
      
      <View style={styles.insightsContainer}>
        <Text style={styles.insightsTitle}>Insights</Text>
        <Card style={styles.insightCard} variant="outlined">
          <Text style={styles.insightText}>
            {currentUser.type === 'farmer' 
              ? 'Your tomatoes are selling 20% higher than market average'
              : 'You could save 15% by buying directly from local farmers'}
          </Text>
        </Card>
        <Card style={styles.insightCard} variant="outlined">
          <Text style={styles.insightText}>
            {currentUser.type === 'farmer' 
              ? 'Demand for organic produce has increased by 35% this month'
              : 'Prices for seasonal fruits are expected to drop next month'}
          </Text>
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  signInButton: {
    minWidth: 200,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    padding: 16,
    marginBottom: 16,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statTitle: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  chartContainer: {
    marginBottom: 24,
    paddingHorizontal: 16,
  },
  chartTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  chartCard: {
    padding: 16,
  },
  barChart: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    height: 160,
  },
  barContainer: {
    alignItems: 'center',
    flex: 1,
  },
  bar: {
    width: 24,
    backgroundColor: Colors.primary,
    borderRadius: 4,
    marginBottom: 8,
  },
  barLabel: {
    fontSize: 12,
    color: Colors.textLight,
    marginBottom: 4,
  },
  barValue: {
    fontSize: 10,
    color: Colors.textLight,
    fontWeight: '600',
  },
  categoryChart: {
    gap: 16,
  },
  categoryItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  categoryInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  categoryColor: {
    width: 16,
    height: 16,
    borderRadius: 8,
    marginRight: 12,
  },
  categoryName: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '500',
  },
  categoryPercentage: {
    fontSize: 14,
    color: Colors.textLight,
    fontWeight: '600',
  },
  insightsContainer: {
    padding: 16,
    marginBottom: 24,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  insightCard: {
    marginBottom: 12,
  },
  insightText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
});