import React, { useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useMessageStore } from '@/store/messageStore';
import { useUserStore } from '@/store/userStore';
import Colors from '@/constants/colors';
import ConversationItem from '@/components/messages/ConversationItem';
import EmptyState from '@/components/ui/EmptyState';
import { MessageCircle, Plus } from '@/components/ui/Icon';

export default function MessagesScreen() {
  const router = useRouter();
  const { currentUser, isAuthenticated } = useUserStore();
  const { conversations, fetchConversations, isLoading } = useMessageStore();
  
  useEffect(() => {
    if (isAuthenticated) {
      fetchConversations();
    }
  }, [isAuthenticated]);
  
  if (!isAuthenticated) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ 
          title: 'Messages',
          headerShown: true,
          headerStyle: { backgroundColor: Colors.white },
          headerTitleStyle: { color: Colors.text }
        }} />
        <EmptyState
          icon={<MessageCircle size={48} color={Colors.textLight} />}
          title="Sign in to view messages"
          description="Connect with farmers and buyers"
          actionTitle="Sign In"
          onAction={() => router.push('/auth/login')}
        />
      </View>
    );
  }
  
  const userConversations = conversations.filter(conv => 
    conv.participants.includes(currentUser?.id || '')
  );

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ 
        title: 'Messages',
        headerShown: true,
        headerStyle: { backgroundColor: Colors.white },
        headerTitleStyle: { color: Colors.text },
        headerRight: () => (
          <TouchableOpacity 
            style={styles.newMessageButton}
            onPress={() => router.push('/marketplace')}
          >
            <Plus size={20} color={Colors.white} />
          </TouchableOpacity>
        )
      }} />
      
      {userConversations.length === 0 ? (
        <EmptyState
          icon={<MessageCircle size={48} color={Colors.textLight} />}
          title="No conversations yet"
          description="Start chatting with farmers and buyers"
          actionTitle="Browse Market"
          onAction={() => router.push('/marketplace')}
        />
      ) : (
        <FlatList
          data={userConversations}
          renderItem={({ item }) => (
            <ConversationItem 
              conversation={item} 
              onPress={() => router.push(`/messages/${item.id}`)}
            />
          )}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  newMessageButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 16,
  },
  listContainer: {
    padding: 16,
  },
});