import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity, Platform } from 'react-native';
import { useLocalSearchParams, useRouter, Stack } from 'expo-router';
import { Image } from 'expo-image';
import { Calendar, MapPin, Package, Truck, DollarSign, Star, ChevronLeft, ChevronRight } from 'lucide-react-native';
import * as Haptics from 'expo-haptics';
import { useProduceStore } from '@/store/produceStore';
import { useUserStore } from '@/store/userStore';
import { useMessageStore } from '@/store/messageStore';
import { useReservationStore } from '@/store/reservationStore';
import Colors from '@/constants/colors';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import Avatar from '@/components/ui/Avatar';
import { mockUsers } from '@/mocks/users';
import { LinearGradient } from 'expo-linear-gradient';

export default function ProduceDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getProduceById } = useProduceStore();
  const { currentUser } = useUserStore();
  const { startNewConversation } = useMessageStore();
  const { createReservation } = useReservationStore();
  
  const [quantity, setQuantity] = useState(0);
  const [loading, setLoading] = useState(false);
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showImageGallery, setShowImageGallery] = useState(false);
  
  const produce = getProduceById(id);
  const farmer = mockUsers.find(user => user.id === produce?.farmerId);
  
  useEffect(() => {
    if (produce) {
      setQuantity(produce.minOrderQuantity);
    }
  }, [produce]);
  
  if (!produce || !farmer) {
    return (
      <View style={styles.container}>
        <Text>Produce not found</Text>
      </View>
    );
  }
  
  const handleIncreaseQuantity = () => {
    if (Platform.OS !== 'web') {
      Haptics.selectionAsync();
    }
    setQuantity(prev => prev + 1);
  };
  
  const handleDecreaseQuantity = () => {
    if (quantity > produce.minOrderQuantity) {
      if (Platform.OS !== 'web') {
        Haptics.selectionAsync();
      }
      setQuantity(prev => prev - 1);
    }
  };
  
  const handleContactFarmer = async () => {
    if (!currentUser) {
      router.push('/auth/login');
      return;
    }
    
    setLoading(true);
    try {
      const conversationId = await startNewConversation([currentUser.id, farmer.id]);
      router.push(`/messages/${conversationId}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to start conversation');
    } finally {
      setLoading(false);
    }
  };
  
  const handleReserve = async () => {
    if (!currentUser) {
      router.push('/auth/login');
      return;
    }
    
    if (currentUser.type === 'farmer') {
      Alert.alert('Error', 'Farmers cannot make reservations');
      return;
    }
    
    setLoading(true);
    try {
      await createReservation({
        produceId: produce.id,
        buyerId: currentUser.id,
        farmerId: farmer.id,
        quantity,
        status: 'pending',
        deliveryPreference: produce.deliveryOptions[0],
      });
      
      Alert.alert(
        'Reservation Created',
        'Your reservation has been sent to the farmer',
        [{ text: 'OK', onPress: () => router.push('/reservations') }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to create reservation');
    } finally {
      setLoading(false);
    }
  };
  
  const nextImage = () => {
    if (currentImageIndex < produce.images.length - 1) {
      setCurrentImageIndex(currentImageIndex + 1);
    }
  };
  
  const prevImage = () => {
    if (currentImageIndex > 0) {
      setCurrentImageIndex(currentImageIndex - 1);
    }
  };
  
  const toggleImageGallery = () => {
    setShowImageGallery(!showImageGallery);
  };
  
  const isFarmer = currentUser?.id === farmer.id;
  const isBuyer = currentUser?.type === 'buyer';

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen 
        options={{ 
          headerShown: false
        }} 
      />
      
      {/* Product Images */}
      <View style={styles.imageContainer}>
        <TouchableOpacity activeOpacity={0.9} onPress={toggleImageGallery}>
          <Image
            source={{ uri: produce.images[currentImageIndex] }}
            style={styles.image}
            contentFit="cover"
          />
          
          {produce.images.length > 1 && (
            <View style={styles.imageControls}>
              <TouchableOpacity 
                style={styles.imageControlButton} 
                onPress={prevImage}
                disabled={currentImageIndex === 0}
              >
                <ChevronLeft size={24} color={Colors.white} />
              </TouchableOpacity>
              
              <Text style={styles.imageCounter}>
                {currentImageIndex + 1}/{produce.images.length}
              </Text>
              
              <TouchableOpacity 
                style={styles.imageControlButton} 
                onPress={nextImage}
                disabled={currentImageIndex === produce.images.length - 1}
              >
                <ChevronRight size={24} color={Colors.white} />
              </TouchableOpacity>
            </View>
          )}
          
          <LinearGradient
            colors={['transparent', 'rgba(0,0,0,0.7)']}
            style={styles.imageGradient}
          />
        </TouchableOpacity>
        
        {showImageGallery && (
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.thumbnailContainer}
          >
            {produce.images.map((img, index) => (
              <TouchableOpacity 
                key={index}
                onPress={() => setCurrentImageIndex(index)}
                style={[
                  styles.thumbnail,
                  currentImageIndex === index && styles.thumbnailActive
                ]}
              >
                <Image
                  source={{ uri: img }}
                  style={styles.thumbnailImage}
                  contentFit="cover"
                />
              </TouchableOpacity>
            ))}
          </ScrollView>
        )}
      </View>
      
      {/* Product Info */}
      <View style={styles.infoContainer}>
        <View style={styles.header}>
          <Text style={styles.title}>{produce.title}</Text>
          <Badge 
            label={produce.category.charAt(0).toUpperCase() + produce.category.slice(1)} 
            variant="primary"
          />
        </View>
        
        <View style={styles.priceRow}>
          <Text style={styles.price}>${produce.price.toFixed(2)} / {produce.unit}</Text>
          <View style={styles.ratingContainer}>
            <Star size={16} color={Colors.warning} fill={Colors.warning} />
            <Text style={styles.ratingText}>{farmer.rating.toFixed(1)}</Text>
          </View>
        </View>
        
        <View style={styles.detailsContainer}>
          <View style={styles.detailItem}>
            <Calendar size={20} color={Colors.primary} style={styles.detailIcon} />
            <View>
              <Text style={styles.detailLabel}>Harvest Date</Text>
              <Text style={styles.detailValue}>
                {new Date(produce.harvestDate).toLocaleDateString()}
              </Text>
            </View>
          </View>
          
          <View style={styles.detailItem}>
            <Package size={20} color={Colors.primary} style={styles.detailIcon} />
            <View>
              <Text style={styles.detailLabel}>Available Quantity</Text>
              <Text style={styles.detailValue}>{produce.quantity} {produce.unit}</Text>
            </View>
          </View>
          
          <View style={styles.detailItem}>
            <Package size={20} color={Colors.primary} style={styles.detailIcon} />
            <View>
              <Text style={styles.detailLabel}>Minimum Order</Text>
              <Text style={styles.detailValue}>{produce.minOrderQuantity} {produce.unit}</Text>
            </View>
          </View>
          
          <View style={styles.detailItem}>
            <Truck size={20} color={Colors.primary} style={styles.detailIcon} />
            <View>
              <Text style={styles.detailLabel}>Delivery Options</Text>
              <Text style={styles.detailValue}>
                {produce.deliveryOptions.map(option => 
                  option.charAt(0).toUpperCase() + option.slice(1)
                ).join(', ')}
              </Text>
            </View>
          </View>
        </View>
        
        <View style={styles.descriptionContainer}>
          <Text style={styles.sectionTitle}>Description</Text>
          <Text style={styles.description}>{produce.description}</Text>
        </View>
        
        <View style={styles.farmerContainer}>
          <Text style={styles.sectionTitle}>Farmer</Text>
          <TouchableOpacity 
            style={styles.farmerCard}
            onPress={() => router.push(`/profile/${farmer.id}`)}
          >
            <Avatar 
              source={farmer.avatar} 
              name={farmer.name} 
              size="md" 
              verified={farmer.verified}
            />
            <View style={styles.farmerInfo}>
              <Text style={styles.farmerName}>{farmer.name}</Text>
              <View style={styles.farmerLocation}>
                <MapPin size={14} color={Colors.textLight} style={styles.locationIcon} />
                <Text style={styles.locationText}>
                  {farmer.location?.address || 'Location not provided'}
                </Text>
              </View>
            </View>
          </TouchableOpacity>
        </View>
        
        {isBuyer && !isFarmer && (
          <View style={styles.actionContainer}>
            <View style={styles.quantityContainer}>
              <Text style={styles.quantityLabel}>Quantity</Text>
              <View style={styles.quantityControls}>
                <TouchableOpacity 
                  style={[
                    styles.quantityButton,
                    quantity <= produce.minOrderQuantity && styles.quantityButtonDisabled
                  ]} 
                  onPress={handleDecreaseQuantity}
                  disabled={quantity <= produce.minOrderQuantity}
                >
                  <Text style={[
                    styles.quantityButtonText,
                    quantity <= produce.minOrderQuantity && styles.quantityButtonTextDisabled
                  ]}>-</Text>
                </TouchableOpacity>
                <Text style={styles.quantityValue}>{quantity} {produce.unit}</Text>
                <TouchableOpacity 
                  style={[
                    styles.quantityButton,
                    quantity >= produce.quantity && styles.quantityButtonDisabled
                  ]} 
                  onPress={handleIncreaseQuantity}
                  disabled={quantity >= produce.quantity}
                >
                  <Text style={[
                    styles.quantityButtonText,
                    quantity >= produce.quantity && styles.quantityButtonTextDisabled
                  ]}>+</Text>
                </TouchableOpacity>
              </View>
            </View>
            
            <View style={styles.totalContainer}>
              <Text style={styles.totalLabel}>Total Price</Text>
              <Text style={styles.totalValue}>
                ${(produce.price * quantity).toFixed(2)}
              </Text>
            </View>
            
            <View style={styles.buttonContainer}>
              <Button 
                title="Contact Farmer" 
                onPress={handleContactFarmer} 
                variant="outline"
                style={styles.contactButton}
                loading={loading}
              />
              <Button 
                title="Reserve Now" 
                onPress={handleReserve} 
                style={styles.reserveButton}
                loading={loading}
              />
            </View>
          </View>
        )}
        
        {isFarmer && (
          <View style={styles.actionContainer}>
            <Button 
              title="Edit Listing" 
              onPress={() => router.push(`/marketplace/edit/${produce.id}`)} 
              style={styles.editButton}
            />
          </View>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  imageContainer: {
    width: '100%',
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 300,
  },
  imageControls: {
    position: 'absolute',
    bottom: 16,
    left: 0,
    right: 0,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    zIndex: 10,
  },
  imageControlButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  imageCounter: {
    color: Colors.white,
    fontSize: 14,
    fontWeight: '600',
  },
  imageGradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 80,
  },
  thumbnailContainer: {
    padding: 8,
    backgroundColor: Colors.white,
  },
  thumbnail: {
    width: 60,
    height: 60,
    borderRadius: 8,
    marginRight: 8,
    borderWidth: 2,
    borderColor: 'transparent',
    overflow: 'hidden',
  },
  thumbnailActive: {
    borderColor: Colors.primary,
  },
  thumbnailImage: {
    width: '100%',
    height: '100%',
  },
  infoContainer: {
    padding: 16,
    backgroundColor: Colors.white,
    borderTopLeftRadius: 24,
    borderTopRightRadius: 24,
    marginTop: -20,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  title: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    flex: 1,
    marginRight: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  price: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.primary,
  },
  ratingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.warning + '20',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 16,
  },
  ratingText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.warning,
    marginLeft: 4,
  },
  detailsContainer: {
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
    marginBottom: 24,
  },
  detailItem: {
    flexDirection: 'row',
    marginBottom: 12,
  },
  detailIcon: {
    marginRight: 12,
    marginTop: 2,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 2,
  },
  detailValue: {
    fontSize: 16,
    color: Colors.text,
    fontWeight: '500',
  },
  descriptionContainer: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: Colors.text,
    lineHeight: 24,
  },
  farmerContainer: {
    marginBottom: 24,
  },
  farmerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.card,
    borderRadius: 12,
    padding: 16,
  },
  farmerInfo: {
    marginLeft: 12,
    flex: 1,
  },
  farmerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  farmerLocation: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationIcon: {
    marginRight: 4,
  },
  locationText: {
    fontSize: 14,
    color: Colors.textLight,
  },
  actionContainer: {
    borderTopWidth: 1,
    borderTopColor: Colors.border,
    paddingTop: 16,
  },
  quantityContainer: {
    marginBottom: 16,
  },
  quantityLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  quantityControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  quantityButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
  },
  quantityButtonDisabled: {
    backgroundColor: Colors.border,
  },
  quantityButtonText: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  quantityButtonTextDisabled: {
    color: Colors.textLight,
  },
  quantityValue: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginHorizontal: 16,
    minWidth: 80,
    textAlign: 'center',
  },
  totalContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  totalLabel: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  totalValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.primary,
  },
  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  contactButton: {
    flex: 1,
    marginRight: 8,
  },
  reserveButton: {
    flex: 1,
    marginLeft: 8,
  },
  editButton: {
    width: '100%',
  },
});