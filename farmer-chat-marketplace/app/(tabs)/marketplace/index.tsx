import React, { useEffect, useState } from 'react';
import { View, StyleSheet, TouchableOpacity } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useProduceStore } from '@/store/produceStore';
import { useUserStore } from '@/store/userStore';
import Colors from '@/constants/colors';
import SearchBar from '@/components/produce/SearchBar';
import CategoryFilter from '@/components/produce/CategoryFilter';
import ProduceGrid from '@/components/produce/ProduceGrid';
import { Plus, Search, Filter } from 'lucide-react-native';
import { ProduceCategory } from '@/types';

export default function MarketplaceScreen() {
  const router = useRouter();
  const { produces, fetchProduces, isLoading } = useProduceStore();
  const { currentUser } = useUserStore();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<ProduceCategory | 'all'>('all');
  const [showFilters, setShowFilters] = useState(false);
  
  useEffect(() => {
    fetchProduces();
  }, []);
  
  const filteredProduces = produces.filter(produce => {
    const matchesSearch = produce.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         produce.variety.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || produce.category === selectedCategory;
    return matchesSearch && matchesCategory && produce.available;
  });
  
  const handleCreateListing = () => {
    if (!currentUser) {
      router.push('/auth/login');
    } else if (currentUser.type === 'farmer') {
      router.push('/marketplace/create');
    }
  };

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ 
        title: 'Marketplace',
        headerShown: true,
        headerStyle: { backgroundColor: Colors.white },
        headerTitleStyle: { color: Colors.text },
        headerRight: () => (
          <View style={styles.headerActions}>
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => setShowFilters(!showFilters)}
            >
              <Filter size={20} color={Colors.primary} />
            </TouchableOpacity>
            
            <TouchableOpacity 
              style={styles.headerButton}
              onPress={() => router.push('/marketplace/search')}
            >
              <Search size={20} color={Colors.primary} />
            </TouchableOpacity>
            
            {currentUser?.type === 'farmer' && (
              <TouchableOpacity 
                style={styles.addButton}
                onPress={handleCreateListing}
              >
                <Plus size={20} color={Colors.white} />
              </TouchableOpacity>
            )}
          </View>
        )
      }} />
      
      {/* Search Bar */}
      <View style={styles.searchContainer}>
        <SearchBar 
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholder="Search produce..."
          style={styles.searchBar}
        />
      </View>
      
      {/* Filters */}
      {showFilters && (
        <View style={styles.filtersContainer}>
          <CategoryFilter
            selectedCategory={selectedCategory}
            onSelectCategory={setSelectedCategory}
          />
        </View>
      )}
      
      {/* Produce Grid */}
      <ProduceGrid 
        produces={filteredProduces}
        loading={isLoading}
        onRefresh={fetchProduces}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  searchContainer: {
    paddingHorizontal: 16,
    paddingVertical: 12,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  searchBar: {
    flex: 1,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    marginRight: 16,
  },
  headerButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  addButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.primary,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  filtersContainer: {
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    paddingVertical: 8,
  },
});