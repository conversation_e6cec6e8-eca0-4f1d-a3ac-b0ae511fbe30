import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useProduceStore } from '@/store/produceStore';
import { useReservationStore } from '@/store/reservationStore';
import Colors from '@/constants/colors';
import ProfileHeader from '@/components/profile/ProfileHeader';
import FarmerDetails from '@/components/profile/FarmerDetails';
import BuyerDetails from '@/components/profile/BuyerDetails';
import Button from '@/components/ui/Button';
import ProduceCard from '@/components/produce/ProduceCard';
import ReservationCard from '@/components/reservation/ReservationCard';
import { FarmerProfile, BuyerProfile } from '@/types';

export default function ProfileScreen() {
  const router = useRouter();
  const { currentUser, isAuthenticated, logout } = useUserStore();
  const { produces, fetchProduces, getProducesByFarmerId } = useProduceStore();
  const { 
    reservations, 
    fetchReservations, 
    getReservationsByFarmerId, 
    getReservationsByBuyerId 
  } = useReservationStore();
  
  useEffect(() => {
    if (isAuthenticated) {
      fetchProduces();
      fetchReservations();
    }
  }, [isAuthenticated]);
  
  const handleLogin = () => {
    router.push('/auth/login');
  };
  
  const handleLogout = () => {
    logout();
    router.replace('/');
  };
  
  const handleEditProfile = () => {
    router.push('/profile/edit');
  };
  
  if (!isAuthenticated || !currentUser) {
    return (
      <View style={styles.emptyContainer}>
        <Stack.Screen options={{ 
          title: 'Profile',
          headerShown: true,
          headerStyle: { backgroundColor: Colors.white },
          headerTitleStyle: { color: Colors.text }
        }} />
        <Text style={styles.emptyTitle}>Sign in to view your profile</Text>
        <Text style={styles.emptySubtitle}>
          Create an account to connect with farmers and buyers
        </Text>
        <Button 
          title="Sign In" 
          onPress={handleLogin} 
          style={styles.signInButton}
        />
      </View>
    );
  }
  
  const userProduces = currentUser.type === 'farmer' 
    ? getProducesByFarmerId(currentUser.id).slice(0, 2)
    : [];
  
  const userReservations = currentUser.type === 'farmer'
    ? getReservationsByFarmerId(currentUser.id).slice(0, 2)
    : getReservationsByBuyerId(currentUser.id).slice(0, 2);

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen options={{ 
        title: 'Profile',
        headerShown: true,
        headerStyle: { backgroundColor: Colors.white },
        headerTitleStyle: { color: Colors.text }
      }} />
      
      <ProfileHeader user={currentUser} />
      
      <View style={styles.actionsContainer}>
        <Button 
          title="Edit Profile" 
          onPress={handleEditProfile} 
          variant="outline"
          style={styles.actionButton}
        />
        <Button 
          title="Log Out" 
          onPress={handleLogout} 
          variant="ghost"
          style={styles.actionButton}
        />
      </View>
      
      {currentUser.type === 'farmer' && (
        <FarmerDetails farmer={currentUser as FarmerProfile} />
      )}
      
      {currentUser.type === 'buyer' && (
        <BuyerDetails buyer={currentUser as BuyerProfile} />
      )}
      
      {currentUser.type === 'farmer' && userProduces.length > 0 && (
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Your Listings</Text>
            <Text 
              style={styles.seeAllText}
              onPress={() => router.push('/profile/listings')}
            >
              See All
            </Text>
          </View>
          
          {userProduces.map(produce => (
            <ProduceCard key={produce.id} produce={produce} />
          ))}
        </View>
      )}
      
      {userReservations.length > 0 && (
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>
              {currentUser.type === 'farmer' ? 'Incoming Reservations' : 'Your Reservations'}
            </Text>
            <Text 
              style={styles.seeAllText}
              onPress={() => router.push('/reservations')}
            >
              See All
            </Text>
          </View>
          
          {userReservations.map(reservation => (
            <ReservationCard 
              key={reservation.id} 
              reservation={reservation} 
              showFarmer={currentUser.type === 'buyer'}
              showBuyer={currentUser.type === 'farmer'}
            />
          ))}
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: Colors.background,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  signInButton: {
    minWidth: 200,
  },
  actionsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  actionButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  sectionContainer: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  seeAllText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
});