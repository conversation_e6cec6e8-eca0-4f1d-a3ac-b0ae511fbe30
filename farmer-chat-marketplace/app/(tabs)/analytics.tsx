import React from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useProduceStore } from '@/store/produceStore';
import { useReservationStore } from '@/store/reservationStore';
import Colors from '@/constants/colors';
import Button from '@/components/ui/Button';
import Card from '@/components/ui/Card';
import { TrendingUp, ShoppingBag, User } from '@/components/ui/Icon';
import { Ionicons } from '@expo/vector-icons';
import EmptyState from '@/components/ui/EmptyState';

export default function AnalyticsScreen() {
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { produces, getProducesByFarmerId } = useProduceStore();
  const { reservations, getReservationsByFarmerId, getReservationsByBuyerId } = useReservationStore();
  
  if (!currentUser) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ 
          title: 'Analytics',
          headerShown: true,
          headerStyle: { backgroundColor: Colors.white },
          headerTitleStyle: { color: Colors.text }
        }} />
        <EmptyState
          icon={<TrendingUp size={48} color={Colors.textLight} />}
          title="Sign in to view analytics"
          description="Track your farming business performance"
          actionTitle="Sign In"
          onAction={() => router.push('/auth/login')}
        />
      </View>
    );
  }
  
  const userProduces = currentUser.type === 'farmer' 
    ? getProducesByFarmerId(currentUser.id)
    : [];
  
  const userReservations = currentUser.type === 'farmer'
    ? getReservationsByFarmerId(currentUser.id)
    : getReservationsByBuyerId(currentUser.id);
  
  // Calculate total revenue/spending
  const totalAmount = userReservations.reduce((sum, reservation) => {
    const produce = produces.find(p => p.id === reservation.produceId);
    if (!produce) return sum;
    
    const price = reservation.proposedPrice || produce.price;
    return sum + (price * reservation.quantity);
  }, 0);
  
  // Calculate completed reservations
  const completedReservations = userReservations.filter(r => r.status === 'completed');
  const completionRate = userReservations.length > 0 
    ? (completedReservations.length / userReservations.length) * 100 
    : 0;

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen options={{ 
        title: 'Analytics',
        headerShown: true,
        headerStyle: { backgroundColor: Colors.white },
        headerTitleStyle: { color: Colors.text }
      }} />
      
      <View style={styles.statsContainer}>
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <Ionicons name="cash" size={24} color={Colors.primary} />
          </View>
          <Text style={styles.statTitle}>
            {currentUser.type === 'farmer' ? 'Revenue' : 'Spending'}
          </Text>
          <Text style={styles.statValue}>${totalAmount.toFixed(2)}</Text>
        </Card>
        
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <ShoppingBag size={24} color={Colors.secondary} />
          </View>
          <Text style={styles.statTitle}>Reservations</Text>
          <Text style={styles.statValue}>{userReservations.length}</Text>
        </Card>
        
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <TrendingUp size={24} color={Colors.success} />
          </View>
          <Text style={styles.statTitle}>Completion</Text>
          <Text style={styles.statValue}>{completionRate.toFixed(0)}%</Text>
        </Card>
        
        <Card style={styles.statCard} variant="elevated">
          <View style={styles.statIconContainer}>
            <Ionicons name="people" size={24} color={Colors.warning} />
          </View>
          <Text style={styles.statTitle}>
            {currentUser.type === 'farmer' ? 'Buyers' : 'Farmers'}
          </Text>
          <Text style={styles.statValue}>
            {new Set(userReservations.map(r => 
              currentUser.type === 'farmer' ? r.buyerId : r.farmerId
            )).size}
          </Text>
        </Card>
      </View>
      
      <View style={styles.insightsContainer}>
        <Text style={styles.insightsTitle}>Insights</Text>
        <Card style={styles.insightCard} variant="outlined">
          <Text style={styles.insightText}>
            {currentUser.type === 'farmer' 
              ? 'Your tomatoes are selling 20% higher than market average'
              : 'You could save 15% by buying directly from local farmers'}
          </Text>
        </Card>
        <Card style={styles.insightCard} variant="outlined">
          <Text style={styles.insightText}>
            {currentUser.type === 'farmer' 
              ? 'Demand for organic produce has increased by 35% this month'
              : 'Prices for seasonal fruits are expected to drop next month'}
          </Text>
        </Card>
      </View>
      
      <View style={styles.comingSoonContainer}>
        <Card style={styles.comingSoonCard} variant="elevated">
          <Text style={styles.comingSoonTitle}>Charts Coming Soon</Text>
          <Text style={styles.comingSoonText}>
            Advanced analytics with interactive charts and detailed reports will be available in the next update.
          </Text>
          <Button
            title="View Detailed Analytics"
            onPress={() => router.push('/analytics')}
            style={styles.detailsButtonFull}
          />
        </Card>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  statsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    padding: 16,
    justifyContent: 'space-between',
  },
  statCard: {
    width: '48%',
    padding: 16,
    marginBottom: 16,
  },
  statIconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 12,
  },
  statTitle: {
    fontSize: 14,
    color: Colors.textLight,
    marginBottom: 4,
  },
  statValue: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  insightsContainer: {
    padding: 16,
    marginBottom: 24,
  },
  insightsTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
  },
  insightCard: {
    marginBottom: 12,
  },
  insightText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  comingSoonContainer: {
    padding: 16,
    marginBottom: 24,
  },
  comingSoonCard: {
    padding: 24,
    alignItems: 'center',
  },
  comingSoonTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  comingSoonText: {
    fontSize: 14,
    color: Colors.textLight,
    textAlign: 'center',
    lineHeight: 20,
  },
  detailsButtonFull: {
    marginTop: 16,
  },
});