import React, { useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity } from 'react-native';
import { useRouter, Stack } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useProduceStore } from '@/store/produceStore';
import { useReservationStore } from '@/store/reservationStore';
import Colors from '@/constants/colors';
import Button from '@/components/ui/Button';
import ProduceCard from '@/components/produce/ProduceCard';
import ReservationCard from '@/components/reservation/ReservationCard';
import { Plus, TrendingUp, ShoppingBag, MessageCircle, BarChart } from 'lucide-react-native';

export default function HomeScreen() {
  const router = useRouter();
  const { currentUser, isAuthenticated } = useUserStore();
  const { produces, fetchProduces } = useProduceStore();
  const { reservations, fetchReservations } = useReservationStore();
  
  useEffect(() => {
    fetchProduces();
    fetchReservations();
  }, []);
  
  const recentProduces = produces.slice(0, 3);
  const recentReservations = reservations.slice(0, 2);
  
  const handleQuickAction = (action: string) => {
    switch (action) {
      case 'browse':
        router.push('/marketplace');
        break;
      case 'create':
        if (!isAuthenticated) {
          router.push('/auth/login');
        } else if (currentUser?.type === 'farmer') {
          router.push('/marketplace/create');
        } else {
          router.push('/marketplace');
        }
        break;
      case 'messages':
        router.push('/messages');
        break;
      case 'analytics':
        router.push('/analytics');
        break;
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen options={{ 
        title: 'Home',
        headerShown: true,
        headerStyle: { backgroundColor: Colors.white },
        headerTitleStyle: { color: Colors.text },
        headerRight: () => (
          isAuthenticated && currentUser?.type === 'farmer' ? (
            <TouchableOpacity 
              style={styles.headerAddButton}
              onPress={() => handleQuickAction('create')}
            >
              <Plus size={24} color={Colors.primary} />
            </TouchableOpacity>
          ) : null
        )
      }} />
      
      {/* Welcome Section */}
      <View style={styles.welcomeSection}>
        <Text style={styles.greeting}>
          {isAuthenticated ? `Hello, ${currentUser?.name?.split(' ')[0]}!` : 'Welcome!'}
        </Text>
        <Text style={styles.subtitle}>
          {isAuthenticated 
            ? currentUser?.type === 'farmer' 
              ? 'Manage your farm listings'
              : 'Find fresh produce nearby'
            : 'Connect with local farmers'
          }
        </Text>
      </View>
      
      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => handleQuickAction('browse')}
        >
          <View style={styles.actionIcon}>
            <ShoppingBag size={24} color={Colors.primary} />
          </View>
          <Text style={styles.actionTitle}>Browse Market</Text>
          <Text style={styles.actionSubtitle}>Find fresh produce</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => handleQuickAction('messages')}
        >
          <View style={styles.actionIcon}>
            <MessageCircle size={24} color={Colors.secondary} />
          </View>
          <Text style={styles.actionTitle}>Messages</Text>
          <Text style={styles.actionSubtitle}>Chat with farmers</Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={styles.actionCard}
          onPress={() => handleQuickAction('analytics')}
        >
          <View style={styles.actionIcon}>
            <BarChart size={24} color={Colors.accent} />
          </View>
          <Text style={styles.actionTitle}>Analytics</Text>
          <Text style={styles.actionSubtitle}>View insights</Text>
        </TouchableOpacity>
      </View>
      
      {/* Recent Produce */}
      <View style={styles.section}>
        <View style={styles.sectionHeader}>
          <Text style={styles.sectionTitle}>Fresh Produce</Text>
          <TouchableOpacity onPress={() => router.push('/marketplace')}>
            <Text style={styles.seeAllText}>See All</Text>
          </TouchableOpacity>
        </View>
        
        {recentProduces.map(produce => (
          <ProduceCard key={produce.id} produce={produce} />
        ))}
        
        {recentProduces.length === 0 && (
          <View style={styles.emptyState}>
            <Text style={styles.emptyText}>No produce available</Text>
            <Text style={styles.emptySubtext}>Check back later for fresh listings</Text>
          </View>
        )}
      </View>
      
      {/* Recent Activity */}
      {isAuthenticated && recentReservations.length > 0 && (
        <View style={styles.section}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Recent Activity</Text>
            <TouchableOpacity onPress={() => router.push('/reservations')}>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>
          
          {recentReservations.map(reservation => (
            <ReservationCard 
              key={reservation.id} 
              reservation={reservation}
              showFarmer={currentUser?.type === 'buyer'}
              showBuyer={currentUser?.type === 'farmer'}
            />
          ))}
        </View>
      )}
      
      {/* Call to Action */}
      {!isAuthenticated && (
        <View style={styles.ctaSection}>
          <Text style={styles.ctaTitle}>Join Our Community</Text>
          <Text style={styles.ctaSubtitle}>
            Connect directly with local farmers and buyers
          </Text>
          <Button 
            title="Get Started" 
            onPress={() => router.push('/auth/register')} 
            style={styles.ctaButton}
            size="lg"
          />
        </View>
      )}
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  headerAddButton: {
    marginRight: 16,
  },
  welcomeSection: {
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  greeting: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textLight,
  },
  quickActions: {
    flexDirection: 'row',
    padding: 16,
    justifyContent: 'space-between',
  },
  actionCard: {
    flex: 1,
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginHorizontal: 4,
    alignItems: 'center',
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  actionIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  actionTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 2,
  },
  actionSubtitle: {
    fontSize: 12,
    color: Colors.textLight,
    textAlign: 'center',
  },
  section: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
  },
  seeAllText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    padding: 24,
    backgroundColor: Colors.white,
    borderRadius: 12,
  },
  emptyText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    color: Colors.textLight,
  },
  ctaSection: {
    margin: 16,
    padding: 24,
    backgroundColor: Colors.white,
    borderRadius: 16,
    alignItems: 'center',
  },
  ctaTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  ctaSubtitle: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 16,
  },
  ctaButton: {
    minWidth: 200,
  },
});