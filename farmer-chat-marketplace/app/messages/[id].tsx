import React, { useEffect, useRef, useState } from 'react';
import { 
  View, 
  StyleSheet, 
  FlatList, 
  KeyboardAvoidingView, 
  Platform, 
  ActivityIndicator,
  TouchableOpacity,
  Text,
  ScrollView
} from 'react-native';
import { useLocalSearchParams, Stack, useRouter } from 'expo-router';
import { useMessageStore } from '@/store/messageStore';
import { useUserStore } from '@/store/userStore';
import { useReservationStore } from '@/store/reservationStore';
import { useProduceStore } from '@/store/produceStore';
import Colors from '@/constants/colors';
import MessageBubble from '@/components/messages/MessageBubble';
import MessageInput from '@/components/messages/MessageInput';
import Avatar from '@/components/ui/Avatar';
import { mockUsers } from '@/mocks/users';
import { Image } from 'expo-image';
import { Info, Phone, Video, ArrowLeft } from 'lucide-react-native';

export default function ConversationScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { 
    messages, 
    currentConversation, 
    fetchMessages, 
    sendMessage, 
    markAsRead,
    isLoading 
  } = useMessageStore();
  const { reservations } = useReservationStore();
  const { produces } = useProduceStore();
  
  const [sending, setSending] = useState(false);
  const [showInfo, setShowInfo] = useState(false);
  const flatListRef = useRef<FlatList>(null);
  
  useEffect(() => {
    if (id && currentUser) {
      fetchMessages(id);
    }
  }, [id, currentUser]);
  
  useEffect(() => {
    if (id && currentConversation && currentConversation.unreadCount && currentConversation.unreadCount > 0) {
      markAsRead(id);
    }
  }, [id, currentConversation]);
  
  const conversationMessages = messages[id] || [];
  
  const handleSend = async (content: string) => {
    if (!currentUser || !currentConversation) return;
    
    const otherParticipantId = currentConversation.participants.find(
      participantId => participantId !== currentUser.id
    );
    
    if (!otherParticipantId) return;
    
    setSending(true);
    await sendMessage(id, content, currentUser.id, otherParticipantId);
    setSending(false);
    
    // Scroll to bottom
    setTimeout(() => {
      flatListRef.current?.scrollToEnd({ animated: true });
    }, 100);
  };
  
  if (!currentUser) {
    router.replace('/auth/login');
    return null;
  }
  
  if (isLoading && conversationMessages.length === 0) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }
  
  // Get the other participant for the header title
  const otherParticipantId = currentConversation?.participants.find(
    participantId => participantId !== currentUser.id
  );
  const otherParticipant = mockUsers.find(user => user.id === otherParticipantId);
  
  // Find related reservations between these users
  const relatedReservations = reservations.filter(
    r => (r.buyerId === currentUser.id && r.farmerId === otherParticipantId) ||
         (r.farmerId === currentUser.id && r.buyerId === otherParticipantId)
  );
  
  // Get produce items for these reservations
  const relatedProduceIds = relatedReservations.map(r => r.produceId);
  const relatedProduces = produces.filter(p => relatedProduceIds.includes(p.id));
  
  const toggleInfo = () => {
    setShowInfo(!showInfo);
  };

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : undefined}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 90 : 0}
    >
      <Stack.Screen 
        options={{ 
          headerShown: false
        }} 
      />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={Colors.text} />
        </TouchableOpacity>
        
        {otherParticipant && (
          <TouchableOpacity 
            style={styles.userInfo}
            onPress={() => router.push(`/profile/${otherParticipant.id}`)}
          >
            <Avatar 
              source={otherParticipant.avatar} 
              name={otherParticipant.name} 
              size="sm" 
              verified={otherParticipant.verified}
            />
            <View style={styles.userTextInfo}>
              <Text style={styles.userName}>{otherParticipant.name}</Text>
              <Text style={styles.userType}>
                {otherParticipant.type.charAt(0).toUpperCase() + otherParticipant.type.slice(1)}
              </Text>
            </View>
          </TouchableOpacity>
        )}
        
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.headerButton}>
            <Phone size={20} color={Colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton}>
            <Video size={20} color={Colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.headerButton} onPress={toggleInfo}>
            <Info size={20} color={Colors.primary} />
          </TouchableOpacity>
        </View>
      </View>
      
      {showInfo && relatedProduces.length > 0 && (
        <View style={styles.infoPanel}>
          <Text style={styles.infoPanelTitle}>Related Produce</Text>
          <ScrollView 
            horizontal 
            showsHorizontalScrollIndicator={false}
            contentContainerStyle={styles.relatedProduceContainer}
          >
            {relatedProduces.map(produce => (
              <TouchableOpacity 
                key={produce.id}
                style={styles.relatedProduceItem}
                onPress={() => router.push(`/marketplace/${produce.id}`)}
              >
                <Image
                  source={{ uri: produce.images[0] }}
                  style={styles.relatedProduceImage}
                  contentFit="cover"
                />
                <Text style={styles.relatedProduceTitle} numberOfLines={1}>
                  {produce.title}
                </Text>
                <Text style={styles.relatedProducePrice}>
                  ${produce.price.toFixed(2)}/{produce.unit}
                </Text>
              </TouchableOpacity>
            ))}
          </ScrollView>
        </View>
      )}
      
      <FlatList
        ref={flatListRef}
        data={conversationMessages}
        renderItem={({ item }) => (
          <MessageBubble 
            message={item} 
            isCurrentUser={item.senderId === currentUser.id} 
          />
        )}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.messagesContainer}
        onLayout={() => flatListRef.current?.scrollToEnd({ animated: false })}
      />
      
      <MessageInput onSend={handleSend} />
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 8,
  },
  userInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  userTextInfo: {
    marginLeft: 12,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  userType: {
    fontSize: 12,
    color: Colors.textLight,
  },
  headerActions: {
    flexDirection: 'row',
  },
  headerButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: Colors.card,
    alignItems: 'center',
    justifyContent: 'center',
    marginLeft: 8,
  },
  messagesContainer: {
    padding: 16,
    paddingBottom: 8,
  },
  infoPanel: {
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
    padding: 12,
  },
  infoPanelTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 8,
  },
  relatedProduceContainer: {
    paddingBottom: 8,
  },
  relatedProduceItem: {
    width: 120,
    marginRight: 12,
    borderRadius: 8,
    overflow: 'hidden',
    backgroundColor: Colors.card,
  },
  relatedProduceImage: {
    width: '100%',
    height: 80,
  },
  relatedProduceTitle: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.text,
    padding: 8,
    paddingBottom: 4,
  },
  relatedProducePrice: {
    fontSize: 12,
    fontWeight: '600',
    color: Colors.primary,
    paddingHorizontal: 8,
    paddingBottom: 8,
  },
});