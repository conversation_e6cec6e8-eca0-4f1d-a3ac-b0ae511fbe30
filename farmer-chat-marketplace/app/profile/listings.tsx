import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Plus, Filter } from 'lucide-react-native';
import { useProduceStore } from '@/store/produceStore';
import { useUserStore } from '@/store/userStore';
import Colors from '@/constants/colors';
import ProduceCard from '@/components/produce/ProduceCard';
import Button from '@/components/ui/Button';

export default function FarmerListingsScreen() {
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { produces, fetchProduces, getProducesByFarmerId } = useProduceStore();
  
  const [filter, setFilter] = useState<'all' | 'available' | 'unavailable'>('all');
  const [refreshing, setRefreshing] = useState(false);
  
  useEffect(() => {
    fetchProduces();
  }, []);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchProduces();
    setRefreshing(false);
  };
  
  if (!currentUser || currentUser.type !== 'farmer') {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>Only farmers can view listings</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.backButton}
        />
      </View>
    );
  }
  
  const farmerProduces = getProducesByFarmerId(currentUser.id);
  
  const filteredProduces = farmerProduces.filter(produce => {
    if (filter === 'all') return true;
    if (filter === 'available') return produce.available;
    if (filter === 'unavailable') return !produce.available;
    return true;
  });
  
  const handleAddProduce = () => {
    router.push('/marketplace/create');
  };

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: 'My Listings',
          headerBackTitle: 'Profile'
        }} 
      />
      
      <View style={styles.filterContainer}>
        <TouchableOpacity 
          style={[styles.filterButton, filter === 'all' && styles.filterButtonActive]}
          onPress={() => setFilter('all')}
        >
          <Text style={[styles.filterText, filter === 'all' && styles.filterTextActive]}>
            All ({farmerProduces.length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.filterButton, filter === 'available' && styles.filterButtonActive]}
          onPress={() => setFilter('available')}
        >
          <Text style={[styles.filterText, filter === 'available' && styles.filterTextActive]}>
            Available ({farmerProduces.filter(p => p.available).length})
          </Text>
        </TouchableOpacity>
        
        <TouchableOpacity 
          style={[styles.filterButton, filter === 'unavailable' && styles.filterButtonActive]}
          onPress={() => setFilter('unavailable')}
        >
          <Text style={[styles.filterText, filter === 'unavailable' && styles.filterTextActive]}>
            Unavailable ({farmerProduces.filter(p => !p.available).length})
          </Text>
        </TouchableOpacity>
      </View>
      
      {filteredProduces.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>No listings found</Text>
          <Text style={styles.emptySubtitle}>
            {filter === 'all' 
              ? "You haven't created any produce listings yet" 
              : `You don't have any ${filter} listings`}
          </Text>
          <Button 
            title="Add New Listing" 
            onPress={handleAddProduce} 
            style={styles.addButton}
          />
        </View>
      ) : (
        <FlatList
          data={filteredProduces}
          renderItem={({ item }) => <ProduceCard produce={item} />}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContent}
          onRefresh={handleRefresh}
          refreshing={refreshing}
        />
      )}
      
      <View style={styles.fabContainer}>
        <Button
          title="Add Listing"
          onPress={handleAddProduce}
          style={styles.fab}
          textStyle={styles.fabText}
          variant="primary"
        />
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  filterContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  filterButton: {
    flex: 1,
    paddingVertical: 8,
    alignItems: 'center',
    borderBottomWidth: 2,
    borderBottomColor: 'transparent',
  },
  filterButtonActive: {
    borderBottomColor: Colors.primary,
  },
  filterText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.textLight,
  },
  filterTextActive: {
    color: Colors.primary,
    fontWeight: '600',
  },
  listContent: {
    padding: 16,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  addButton: {
    minWidth: 200,
  },
  backButton: {
    marginTop: 16,
  },
  fabContainer: {
    position: 'absolute',
    bottom: 24,
    right: 24,
  },
  fab: {
    borderRadius: 28,
    paddingHorizontal: 20,
    paddingVertical: 12,
    flexDirection: 'row',
    alignItems: 'center',
  },
  fabText: {
    marginLeft: 8,
  },
});