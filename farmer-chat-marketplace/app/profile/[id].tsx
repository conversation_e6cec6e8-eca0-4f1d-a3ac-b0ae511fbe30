import React, { useEffect, useState } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert } from 'react-native';
import { useLocalSearchParams, Stack, useRouter } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useProduceStore } from '@/store/produceStore';
import { useMessageStore } from '@/store/messageStore';
import Colors from '@/constants/colors';
import ProfileHeader from '@/components/profile/ProfileHeader';
import FarmerDetails from '@/components/profile/FarmerDetails';
import BuyerDetails from '@/components/profile/BuyerDetails';
import ProduceCard from '@/components/produce/ProduceCard';
import Button from '@/components/ui/Button';
import { mockUsers } from '@/mocks/users';
import { FarmerProfile, BuyerProfile, User } from '@/types';
import { Star } from 'lucide-react-native';

export default function UserProfileScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { getProducesByFarmerId } = useProduceStore();
  const { startNewConversation } = useMessageStore();
  
  const [loading, setLoading] = useState(false);
  const [showAllProduces, setShowAllProduces] = useState(false);
  
  // Find the user from mock data
  const user = mockUsers.find(u => u.id === id);
  
  if (!user) {
    return (
      <View style={styles.container}>
        <Text style={styles.errorText}>User not found</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.backButton}
        />
      </View>
    );
  }
  
  const userProduces = user.type === 'farmer' 
    ? getProducesByFarmerId(user.id)
    : [];
  
  const displayedProduces = showAllProduces ? userProduces : userProduces.slice(0, 3);
  
  const handleContact = async () => {
    if (!currentUser) {
      router.push('/auth/login');
      return;
    }
    
    if (currentUser.id === user.id) {
      Alert.alert('Error', 'You cannot message yourself');
      return;
    }
    
    setLoading(true);
    try {
      const conversationId = await startNewConversation([currentUser.id, user.id]);
      router.push(`/messages/${conversationId}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to start conversation');
    } finally {
      setLoading(false);
    }
  };
  
  const toggleShowAllProduces = () => {
    setShowAllProduces(!showAllProduces);
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen 
        options={{ 
          title: user.name,
          headerBackTitle: 'Back'
        }} 
      />
      
      <ProfileHeader user={user} />
      
      {currentUser && currentUser.id !== user.id && (
        <View style={styles.actionsContainer}>
          <Button 
            title="Contact" 
            onPress={handleContact} 
            style={styles.contactButton}
            loading={loading}
          />
          
          <TouchableOpacity style={styles.rateButton}>
            <Star size={20} color={Colors.warning} />
            <Text style={styles.rateButtonText}>Rate</Text>
          </TouchableOpacity>
        </View>
      )}
      
      {user.type === 'farmer' && (
        <FarmerDetails farmer={user as unknown as FarmerProfile} />
      )}
      
      {user.type === 'buyer' && (
        <BuyerDetails buyer={user as unknown as BuyerProfile} />
      )}
      
      {user.type === 'farmer' && userProduces.length > 0 && (
        <View style={styles.sectionContainer}>
          <View style={styles.sectionHeader}>
            <Text style={styles.sectionTitle}>Available Produce</Text>
            {userProduces.length > 3 && (
              <TouchableOpacity onPress={toggleShowAllProduces}>
                <Text style={styles.seeAllText}>
                  {showAllProduces ? 'Show Less' : 'See All'}
                </Text>
              </TouchableOpacity>
            )}
          </View>
          
          {displayedProduces.map(produce => (
            <ProduceCard key={produce.id} produce={produce} />
          ))}
        </View>
      )}
      
      <View style={styles.reviewsContainer}>
        <Text style={styles.sectionTitle}>Reviews</Text>
        <View style={styles.reviewCard}>
          <View style={styles.reviewHeader}>
            <Text style={styles.reviewerName}>John Doe</Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
            </View>
          </View>
          <Text style={styles.reviewDate}>June 5, 2025</Text>
          <Text style={styles.reviewText}>
            Great {user.type === 'farmer' ? 'farmer' : 'buyer'}! The produce was fresh and exactly as described. 
            Communication was excellent and delivery was on time. Highly recommended!
          </Text>
        </View>
        
        <View style={styles.reviewCard}>
          <View style={styles.reviewHeader}>
            <Text style={styles.reviewerName}>Jane Smith</Text>
            <View style={styles.ratingContainer}>
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} fill={Colors.warning} />
              <Star size={16} color={Colors.warning} />
            </View>
          </View>
          <Text style={styles.reviewDate}>May 28, 2025</Text>
          <Text style={styles.reviewText}>
            {user.type === 'farmer' 
              ? 'Good quality produce and fair prices. Would buy again.' 
              : 'Reliable buyer who pays on time. Good communication.'}
          </Text>
        </View>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  actionsContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  contactButton: {
    flex: 1,
    marginRight: 8,
  },
  rateButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: Colors.warning + '20',
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    marginLeft: 8,
  },
  rateButtonText: {
    color: Colors.warning,
    fontWeight: '600',
    marginLeft: 8,
  },
  sectionContainer: {
    marginVertical: 16,
    paddingHorizontal: 16,
  },
  sectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
  },
  seeAllText: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  reviewsContainer: {
    padding: 16,
    marginBottom: 24,
  },
  reviewCard: {
    backgroundColor: Colors.white,
    borderRadius: 12,
    padding: 16,
    marginTop: 12,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  reviewHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 4,
  },
  reviewerName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  ratingContainer: {
    flexDirection: 'row',
  },
  reviewDate: {
    fontSize: 12,
    color: Colors.textLight,
    marginBottom: 8,
  },
  reviewText: {
    fontSize: 14,
    color: Colors.text,
    lineHeight: 20,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: 'center',
    marginTop: 24,
  },
  backButton: {
    alignSelf: 'center',
    marginTop: 16,
  },
});