import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, ScrollView, TouchableOpacity, Alert, Platform } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { Camera, MapPin } from 'lucide-react-native';
import * as Location from 'expo-location';
import { useUserStore } from '@/store/userStore';
import Colors from '@/constants/colors';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import { FarmerProfile, BuyerProfile } from '@/types';

export default function EditProfileScreen() {
  const router = useRouter();
  const { currentUser, updateProfile, isLoading } = useUserStore();
  
  const [name, setName] = useState('');
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [avatar, setAvatar] = useState<string | undefined>('');
  const [address, setAddress] = useState('');
  const [location, setLocation] = useState<{latitude: number; longitude: number; address: string} | undefined>();
  
  // Farmer specific fields
  const [farmSize, setFarmSize] = useState('');
  const [farmType, setFarmType] = useState('');
  const [crops, setCrops] = useState<string[]>([]);
  const [newCrop, setNewCrop] = useState('');
  
  // Buyer specific fields
  const [businessType, setBusinessType] = useState('');
  const [businessScale, setBusinessScale] = useState('');
  const [paymentMethods, setPaymentMethods] = useState<string[]>([]);
  const [newPaymentMethod, setNewPaymentMethod] = useState('');
  const [purchasePreferences, setPurchasePreferences] = useState<string[]>([]);
  const [newPreference, setNewPreference] = useState('');
  
  const [errors, setErrors] = useState<{
    name?: string;
    email?: string;
    phone?: string;
  }>({});
  
  useEffect(() => {
    if (currentUser) {
      setName(currentUser.name);
      setEmail(currentUser.email);
      setPhone(currentUser.phone);
      setAvatar(currentUser.avatar);
      
      if (currentUser.location) {
        setLocation(currentUser.location);
        setAddress(currentUser.location.address);
      }
      
      if (currentUser.type === 'farmer') {
        const farmerProfile = currentUser as unknown as FarmerProfile;
        if (farmerProfile.farmSize) setFarmSize(farmerProfile.farmSize);
        if (farmerProfile.farmType) setFarmType(farmerProfile.farmType);
        if (farmerProfile.crops) setCrops(farmerProfile.crops);
      }
      
      if (currentUser.type === 'buyer') {
        const buyerProfile = currentUser as unknown as BuyerProfile;
        if (buyerProfile.businessType) setBusinessType(buyerProfile.businessType);
        if (buyerProfile.businessScale) setBusinessScale(buyerProfile.businessScale);
        if (buyerProfile.preferredPaymentMethods) setPaymentMethods(buyerProfile.preferredPaymentMethods);
        if (buyerProfile.purchasePreferences) setPurchasePreferences(buyerProfile.purchasePreferences);
      }
    }
  }, [currentUser]);
  
  if (!currentUser) {
    router.replace('/auth/login');
    return null;
  }
  
  const validate = () => {
    const newErrors: {
      name?: string;
      email?: string;
      phone?: string;
    } = {};
    
    if (!name) newErrors.name = 'Name is required';
    
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!phone) newErrors.phone = 'Phone number is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handlePickImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setAvatar(result.assets[0].uri);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  };
  
  const handleGetLocation = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      
      if (status !== 'granted') {
        Alert.alert('Permission Denied', 'Permission to access location was denied');
        return;
      }
      
      const currentLocation = await Location.getCurrentPositionAsync({});
      const { latitude, longitude } = currentLocation.coords;
      
      // Reverse geocode to get address
      const [geocode] = await Location.reverseGeocodeAsync({ latitude, longitude });
      
      if (geocode) {
        const formattedAddress = [
          geocode.street,
          geocode.city,
          geocode.region,
          geocode.postalCode,
          geocode.country
        ].filter(Boolean).join(', ');
        
        setLocation({
          latitude,
          longitude,
          address: formattedAddress
        });
        
        setAddress(formattedAddress);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to get location');
    }
  };
  
  const handleAddCrop = () => {
    if (newCrop.trim() && !crops.includes(newCrop.trim().toLowerCase())) {
      setCrops([...crops, newCrop.trim().toLowerCase()]);
      setNewCrop('');
    }
  };
  
  const handleRemoveCrop = (crop: string) => {
    setCrops(crops.filter(c => c !== crop));
  };
  
  const handleAddPaymentMethod = () => {
    if (newPaymentMethod.trim() && !paymentMethods.includes(newPaymentMethod.trim().toLowerCase())) {
      setPaymentMethods([...paymentMethods, newPaymentMethod.trim().toLowerCase()]);
      setNewPaymentMethod('');
    }
  };
  
  const handleRemovePaymentMethod = (method: string) => {
    setPaymentMethods(paymentMethods.filter(m => m !== method));
  };
  
  const handleAddPreference = () => {
    if (newPreference.trim() && !purchasePreferences.includes(newPreference.trim().toLowerCase())) {
      setPurchasePreferences([...purchasePreferences, newPreference.trim().toLowerCase()]);
      setNewPreference('');
    }
  };
  
  const handleRemovePreference = (preference: string) => {
    setPurchasePreferences(purchasePreferences.filter(p => p !== preference));
  };
  
  const handleSave = async () => {
    if (!validate()) return;
    
    try {
      const updatedProfile: Partial<FarmerProfile | BuyerProfile> = {
        name,
        email,
        phone,
        avatar,
        location: location ? {
          latitude: location.latitude,
          longitude: location.longitude,
          address: location.address
        } : undefined
      };
      
      if (currentUser.type === 'farmer') {
        Object.assign(updatedProfile, {
          farmSize,
          farmType,
          crops
        });
      }
      
      if (currentUser.type === 'buyer') {
        Object.assign(updatedProfile, {
          businessType,
          businessScale,
          preferredPaymentMethods: paymentMethods,
          purchasePreferences
        });
      }
      
      await updateProfile(updatedProfile);
      
      Alert.alert(
        'Success',
        'Your profile has been updated successfully',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update profile');
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen 
        options={{ 
          title: 'Edit Profile',
          headerBackTitle: 'Profile'
        }} 
      />
      
      <View style={styles.avatarContainer}>
        <TouchableOpacity onPress={handlePickImage}>
          {avatar ? (
            <Image
              source={{ uri: avatar }}
              style={styles.avatar}
              contentFit="cover"
            />
          ) : (
            <View style={styles.avatarPlaceholder}>
              <Text style={styles.avatarPlaceholderText}>
                {name.charAt(0).toUpperCase()}
              </Text>
            </View>
          )}
          <View style={styles.cameraButton}>
            <Camera size={16} color={Colors.white} />
          </View>
        </TouchableOpacity>
      </View>
      
      <View style={styles.formContainer}>
        <Input
          label="Full Name"
          placeholder="Enter your full name"
          value={name}
          onChangeText={setName}
          error={errors.name}
        />
        
        <Input
          label="Email"
          placeholder="Enter your email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          error={errors.email}
        />
        
        <Input
          label="Phone Number"
          placeholder="Enter your phone number"
          value={phone}
          onChangeText={setPhone}
          keyboardType="phone-pad"
          error={errors.phone}
        />
        
        <View style={styles.locationContainer}>
          <Text style={styles.label}>Location</Text>
          <View style={styles.locationInputContainer}>
            <Input
              placeholder="Your address"
              value={address}
              onChangeText={setAddress}
              containerStyle={styles.locationInput}
              inputStyle={styles.locationInputField}
            />
            <TouchableOpacity 
              style={styles.locationButton}
              onPress={handleGetLocation}
            >
              <MapPin size={20} color={Colors.white} />
            </TouchableOpacity>
          </View>
        </View>
        
        {currentUser.type === 'farmer' && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Farm Details</Text>
            
            <Input
              label="Farm Size"
              placeholder="e.g. 25 acres"
              value={farmSize}
              onChangeText={setFarmSize}
            />
            
            <Input
              label="Farm Type"
              placeholder="e.g. Mixed crops, Organic fruits"
              value={farmType}
              onChangeText={setFarmType}
            />
            
            <Text style={styles.label}>Crops Grown</Text>
            <View style={styles.tagsContainer}>
              {crops.map((crop, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>
                    {crop.charAt(0).toUpperCase() + crop.slice(1)}
                  </Text>
                  <TouchableOpacity 
                    onPress={() => handleRemoveCrop(crop)}
                    style={styles.tagRemove}
                  >
                    <Text style={styles.tagRemoveText}>×</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <View style={styles.addTagContainer}>
              <Input
                placeholder="Add a crop"
                value={newCrop}
                onChangeText={setNewCrop}
                containerStyle={styles.addTagInput}
              />
              <Button
                title="Add"
                onPress={handleAddCrop}
                style={styles.addTagButton}
                size="sm"
              />
            </View>
          </View>
        )}
        
        {currentUser.type === 'buyer' && (
          <View style={styles.sectionContainer}>
            <Text style={styles.sectionTitle}>Business Details</Text>
            
            <Input
              label="Business Type"
              placeholder="e.g. Restaurant, Grocery Store"
              value={businessType}
              onChangeText={setBusinessType}
            />
            
            <Input
              label="Business Scale"
              placeholder="e.g. Small, Medium, Large"
              value={businessScale}
              onChangeText={setBusinessScale}
            />
            
            <Text style={styles.label}>Preferred Payment Methods</Text>
            <View style={styles.tagsContainer}>
              {paymentMethods.map((method, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>
                    {method.charAt(0).toUpperCase() + method.slice(1)}
                  </Text>
                  <TouchableOpacity 
                    onPress={() => handleRemovePaymentMethod(method)}
                    style={styles.tagRemove}
                  >
                    <Text style={styles.tagRemoveText}>×</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <View style={styles.addTagContainer}>
              <Input
                placeholder="Add payment method"
                value={newPaymentMethod}
                onChangeText={setNewPaymentMethod}
                containerStyle={styles.addTagInput}
              />
              <Button
                title="Add"
                onPress={handleAddPaymentMethod}
                style={styles.addTagButton}
                size="sm"
              />
            </View>
            
            <Text style={styles.label}>Purchase Preferences</Text>
            <View style={styles.tagsContainer}>
              {purchasePreferences.map((preference, index) => (
                <View key={index} style={styles.tag}>
                  <Text style={styles.tagText}>
                    {preference.charAt(0).toUpperCase() + preference.slice(1)}
                  </Text>
                  <TouchableOpacity 
                    onPress={() => handleRemovePreference(preference)}
                    style={styles.tagRemove}
                  >
                    <Text style={styles.tagRemoveText}>×</Text>
                  </TouchableOpacity>
                </View>
              ))}
            </View>
            
            <View style={styles.addTagContainer}>
              <Input
                placeholder="Add preference"
                value={newPreference}
                onChangeText={setNewPreference}
                containerStyle={styles.addTagInput}
              />
              <Button
                title="Add"
                onPress={handleAddPreference}
                style={styles.addTagButton}
                size="sm"
              />
            </View>
          </View>
        )}
        
        <Button
          title="Save Changes"
          onPress={handleSave}
          loading={isLoading}
          style={styles.saveButton}
          size="lg"
          fullWidth
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  avatarContainer: {
    alignItems: 'center',
    marginVertical: 24,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
  },
  avatarPlaceholder: {
    width: 120,
    height: 120,
    borderRadius: 60,
    backgroundColor: Colors.primary + '30',
    alignItems: 'center',
    justifyContent: 'center',
  },
  avatarPlaceholderText: {
    fontSize: 48,
    fontWeight: '600',
    color: Colors.primary,
  },
  cameraButton: {
    position: 'absolute',
    bottom: 0,
    right: 0,
    backgroundColor: Colors.primary,
    width: 36,
    height: 36,
    borderRadius: 18,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 3,
    borderColor: Colors.white,
  },
  formContainer: {
    padding: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  locationContainer: {
    marginBottom: 16,
  },
  locationInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  locationInput: {
    flex: 1,
    marginBottom: 0,
  },
  locationInputField: {
    borderTopRightRadius: 0,
    borderBottomRightRadius: 0,
  },
  locationButton: {
    backgroundColor: Colors.primary,
    height: 50,
    width: 50,
    alignItems: 'center',
    justifyContent: 'center',
    borderTopRightRadius: 8,
    borderBottomRightRadius: 8,
  },
  sectionContainer: {
    marginTop: 24,
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  tagsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 8,
  },
  tag: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary + '20',
    borderRadius: 16,
    paddingHorizontal: 12,
    paddingVertical: 6,
    marginRight: 8,
    marginBottom: 8,
  },
  tagText: {
    color: Colors.primary,
    fontSize: 14,
    fontWeight: '500',
  },
  tagRemove: {
    marginLeft: 6,
  },
  tagRemoveText: {
    color: Colors.primary,
    fontSize: 16,
    fontWeight: '700',
  },
  addTagContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 16,
  },
  addTagInput: {
    flex: 1,
    marginBottom: 0,
    marginRight: 8,
  },
  addTagButton: {
    height: 50,
  },
  saveButton: {
    marginTop: 24,
    marginBottom: 32,
  },
});