import FontAwesome from "@expo/vector-icons/FontAwesome";
import { useFonts } from "expo-font";
import { Stack } from "expo-router";
import * as SplashScreen from "expo-splash-screen";
import { useEffect } from "react";
import { StatusBar } from "expo-status-bar";
import Colors from "@/constants/colors";

export const unstable_settings = {
  initialRouteName: "(tabs)",
};

SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const [loaded, error] = useFonts({
    ...FontAwesome.font,
  });

  useEffect(() => {
    if (error) {
      console.error(error);
      throw error;
    }
  }, [error]);

  useEffect(() => {
    if (loaded) {
      SplashScreen.hideAsync();
    }
  }, [loaded]);

  if (!loaded) {
    return null;
  }

  return <RootLayoutNav />;
}

function RootLayoutNav() {
  return (
    <>
      <StatusBar style="dark" backgroundColor={Colors.white} />
      <Stack
        screenOptions={{
          headerShown: false,
        }}
      >
        <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
        <Stack.Screen 
          name="modal" 
          options={{ 
            presentation: "modal",
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="marketplace/search" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="marketplace/create" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="marketplace/edit/[id]" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="messages/[id]" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="auth/login" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="auth/register" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="profile/edit" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="profile/listings" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="reservations/index" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="reservations/rate/[id]" 
          options={{ 
            headerShown: false,
          }} 
        />
        <Stack.Screen 
          name="analytics/index" 
          options={{ 
            headerShown: false,
          }} 
        />
      </Stack>
    </>
  );
}