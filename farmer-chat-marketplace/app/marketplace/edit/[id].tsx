import React, { useState, useEffect } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  ScrollView, 
  TouchableOpacity, 
  Alert,
  Platform
} from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Image } from 'expo-image';
import * as ImagePicker from 'expo-image-picker';
import { Camera, Plus, Trash2, ArrowLeft } from 'lucide-react-native';
import { useProduceStore } from '@/store/produceStore';
import { useUserStore } from '@/store/userStore';
import { ProduceCategory, Produce } from '@/types';
import Colors from '@/constants/colors';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';

export default function EditProduceScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { getProduceById, updateProduce, deleteProduce, isLoading } = useProduceStore();
  
  const produce = getProduceById(id);
  
  const [title, setTitle] = useState('');
  const [category, setCategory] = useState<ProduceCategory>('vegetables');
  const [variety, setVariety] = useState('');
  const [quantity, setQuantity] = useState('');
  const [unit, setUnit] = useState('kg');
  const [price, setPrice] = useState('');
  const [minOrderQuantity, setMinOrderQuantity] = useState('');
  const [harvestDate, setHarvestDate] = useState('');
  const [description, setDescription] = useState('');
  const [growingPractice, setGrowingPractice] = useState<'organic' | 'conventional'>('conventional');
  const [deliveryOptions, setDeliveryOptions] = useState<string[]>(['farm pickup']);
  const [images, setImages] = useState<string[]>([]);
  const [available, setAvailable] = useState(true);
  
  const [errors, setErrors] = useState<{
    title?: string;
    category?: string;
    variety?: string;
    quantity?: string;
    unit?: string;
    price?: string;
    minOrderQuantity?: string;
    harvestDate?: string;
    description?: string;
    images?: string;
  }>({});
  
  useEffect(() => {
    if (produce) {
      setTitle(produce.title);
      setCategory(produce.category);
      setVariety(produce.variety);
      setQuantity(produce.quantity.toString());
      setUnit(produce.unit);
      setPrice(produce.price.toString());
      setMinOrderQuantity(produce.minOrderQuantity.toString());
      setHarvestDate(new Date(produce.harvestDate).toLocaleDateString());
      setDescription(produce.description);
      setGrowingPractice(produce.growingPractice === 'organic' ? 'organic' : 'conventional');
      setDeliveryOptions(produce.deliveryOptions);
      setImages(produce.images);
      setAvailable(produce.available);
    }
  }, [produce]);
  
  if (!currentUser || currentUser.type !== 'farmer') {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <Text style={styles.errorText}>Only farmers can edit produce listings</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.actionButton}
        />
      </View>
    );
  }
  
  if (!produce) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <Text style={styles.errorText}>Produce not found</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.actionButton}
        />
      </View>
    );
  }
  
  if (produce.farmerId !== currentUser.id) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <Text style={styles.errorText}>You can only edit your own produce listings</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.actionButton}
        />
      </View>
    );
  }
  
  const validate = () => {
    const newErrors: {
      title?: string;
      category?: string;
      variety?: string;
      quantity?: string;
      unit?: string;
      price?: string;
      minOrderQuantity?: string;
      harvestDate?: string;
      description?: string;
      images?: string;
    } = {};
    
    if (!title) newErrors.title = 'Title is required';
    if (!variety) newErrors.variety = 'Variety is required';
    if (!quantity) newErrors.quantity = 'Quantity is required';
    if (!unit) newErrors.unit = 'Unit is required';
    if (!price) newErrors.price = 'Price is required';
    if (!minOrderQuantity) newErrors.minOrderQuantity = 'Minimum order quantity is required';
    if (!harvestDate) newErrors.harvestDate = 'Harvest date is required';
    if (!description) newErrors.description = 'Description is required';
    if (images.length === 0) newErrors.images = 'At least one image is required';
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleAddImage = async () => {
    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: true,
        aspect: [4, 3],
        quality: 0.8,
      });
      
      if (!result.canceled && result.assets && result.assets.length > 0) {
        setImages([...images, result.assets[0].uri]);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to pick image');
    }
  };
  
  const handleRemoveImage = (index: number) => {
    setImages(images.filter((_, i) => i !== index));
  };
  
  const toggleDeliveryOption = (option: string) => {
    if (deliveryOptions.includes(option)) {
      setDeliveryOptions(deliveryOptions.filter(o => o !== option));
    } else {
      setDeliveryOptions([...deliveryOptions, option]);
    }
  };
  
  const handleUpdate = async () => {
    if (!validate()) return;
    
    try {
      await updateProduce(produce.id, {
        title,
        category,
        variety,
        quantity: Number(quantity),
        unit,
        price: Number(price),
        priceUnit: `USD/${unit}`,
        harvestDate: new Date(harvestDate).toISOString(),
        description,
        images,
        growingPractice,
        deliveryOptions,
        minOrderQuantity: Number(minOrderQuantity),
        available,
      });
      
      Alert.alert(
        'Success',
        'Your produce listing has been updated successfully',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update produce listing');
    }
  };
  
  const handleDelete = async () => {
    Alert.alert(
      'Delete Listing',
      'Are you sure you want to delete this listing? This action cannot be undone.',
      [
        { text: 'Cancel', style: 'cancel' },
        { 
          text: 'Delete', 
          style: 'destructive',
          onPress: async () => {
            try {
              await deleteProduce(produce.id);
              Alert.alert(
                'Success',
                'Your produce listing has been deleted',
                [{ text: 'OK', onPress: () => router.replace('/marketplace') }]
              );
            } catch (error) {
              Alert.alert('Error', 'Failed to delete produce listing');
            }
          }
        }
      ]
    );
  };
  
  const categories: ProduceCategory[] = [
    'vegetables', 'fruits', 'grains', 'dairy', 'meat', 'poultry', 'other'
  ];
  
  const deliveryOptionsList = [
    'farm pickup', 'local delivery', 'shipping', 'bulk shipping'
  ];

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Edit Produce</Text>
        <View style={styles.headerSpacer} />
      </View>
      
      <View style={styles.formContainer}>
        <Input
          label="Title"
          placeholder="e.g. Fresh Organic Tomatoes"
          value={title}
          onChangeText={setTitle}
          error={errors.title}
        />
        
        <Text style={styles.label}>Category</Text>
        <View style={styles.categoryContainer}>
          {categories.map((cat) => (
            <TouchableOpacity
              key={cat}
              style={[
                styles.categoryButton,
                category === cat && styles.categoryButtonActive,
              ]}
              onPress={() => setCategory(cat)}
            >
              <Text
                style={[
                  styles.categoryButtonText,
                  category === cat && styles.categoryButtonTextActive,
                ]}
              >
                {cat.charAt(0).toUpperCase() + cat.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <Input
          label="Variety"
          placeholder="e.g. Roma, Beefsteak"
          value={variety}
          onChangeText={setVariety}
          error={errors.variety}
        />
        
        <View style={styles.rowContainer}>
          <View style={styles.rowItem}>
            <Input
              label="Quantity"
              placeholder="e.g. 500"
              value={quantity}
              onChangeText={setQuantity}
              keyboardType="numeric"
              error={errors.quantity}
            />
          </View>
          
          <View style={styles.rowItem}>
            <Input
              label="Unit"
              placeholder="e.g. kg, lb, pieces"
              value={unit}
              onChangeText={setUnit}
              error={errors.unit}
            />
          </View>
        </View>
        
        <View style={styles.rowContainer}>
          <View style={styles.rowItem}>
            <Input
              label="Price per Unit (USD)"
              placeholder="e.g. 2.50"
              value={price}
              onChangeText={setPrice}
              keyboardType="numeric"
              error={errors.price}
            />
          </View>
          
          <View style={styles.rowItem}>
            <Input
              label="Minimum Order"
              placeholder="e.g. 50"
              value={minOrderQuantity}
              onChangeText={setMinOrderQuantity}
              keyboardType="numeric"
              error={errors.minOrderQuantity}
            />
          </View>
        </View>
        
        <Input
          label="Harvest Date (MM/DD/YYYY)"
          placeholder="e.g. 06/30/2025"
          value={harvestDate}
          onChangeText={setHarvestDate}
          error={errors.harvestDate}
        />
        
        <Input
          label="Description"
          placeholder="Describe your produce..."
          value={description}
          onChangeText={setDescription}
          multiline
          numberOfLines={4}
          error={errors.description}
          inputStyle={styles.textArea}
        />
        
        <Text style={styles.label}>Growing Practice</Text>
        <View style={styles.rowContainer}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              growingPractice === 'organic' && styles.optionButtonActive,
            ]}
            onPress={() => setGrowingPractice('organic')}
          >
            <Text
              style={[
                styles.optionButtonText,
                growingPractice === 'organic' && styles.optionButtonTextActive,
              ]}
            >
              Organic
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.optionButton,
              growingPractice === 'conventional' && styles.optionButtonActive,
            ]}
            onPress={() => setGrowingPractice('conventional')}
          >
            <Text
              style={[
                styles.optionButtonText,
                growingPractice === 'conventional' && styles.optionButtonTextActive,
              ]}
            >
              Conventional
            </Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.label}>Delivery Options</Text>
        <View style={styles.deliveryContainer}>
          {deliveryOptionsList.map((option) => (
            <TouchableOpacity
              key={option}
              style={[
                styles.deliveryButton,
                deliveryOptions.includes(option) && styles.deliveryButtonActive,
              ]}
              onPress={() => toggleDeliveryOption(option)}
            >
              <Text
                style={[
                  styles.deliveryButtonText,
                  deliveryOptions.includes(option) && styles.deliveryButtonTextActive,
                ]}
              >
                {option.charAt(0).toUpperCase() + option.slice(1)}
              </Text>
            </TouchableOpacity>
          ))}
        </View>
        
        <Text style={styles.label}>Availability</Text>
        <View style={styles.rowContainer}>
          <TouchableOpacity
            style={[
              styles.optionButton,
              available && styles.optionButtonActive,
            ]}
            onPress={() => setAvailable(true)}
          >
            <Text
              style={[
                styles.optionButtonText,
                available && styles.optionButtonTextActive,
              ]}
            >
              Available
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[
              styles.optionButton,
              !available && styles.optionButtonActive,
            ]}
            onPress={() => setAvailable(false)}
          >
            <Text
              style={[
                styles.optionButtonText,
                !available && styles.optionButtonTextActive,
              ]}
            >
              Not Available
            </Text>
          </TouchableOpacity>
        </View>
        
        <Text style={styles.label}>Images</Text>
        {errors.images && <Text style={styles.errorText}>{errors.images}</Text>}
        <View style={styles.imagesContainer}>
          {images.map((image, index) => (
            <View key={index} style={styles.imageContainer}>
              <Image
                source={{ uri: image }}
                style={styles.image}
                contentFit="cover"
              />
              <TouchableOpacity
                style={styles.removeButton}
                onPress={() => handleRemoveImage(index)}
              >
                <Text style={styles.removeButtonText}>✕</Text>
              </TouchableOpacity>
            </View>
          ))}
          
          {images.length < 5 && (
            <TouchableOpacity
              style={styles.addImageButton}
              onPress={handleAddImage}
            >
              <Camera size={24} color={Colors.primary} />
              <Text style={styles.addImageText}>Add Image</Text>
            </TouchableOpacity>
          )}
        </View>
        
        <Button
          title="Update Listing"
          onPress={handleUpdate}
          loading={isLoading}
          style={styles.updateButton}
          size="lg"
          fullWidth
        />
        
        <Button
          title="Delete Listing"
          onPress={handleDelete}
          variant="outline"
          style={styles.deleteButton}
          textStyle={styles.deleteButtonText}
          size="lg"
          fullWidth
        />
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  formContainer: {
    padding: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 8,
  },
  categoryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  categoryButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.card,
    marginRight: 8,
    marginBottom: 8,
  },
  categoryButtonActive: {
    backgroundColor: Colors.primary,
  },
  categoryButtonText: {
    fontSize: 14,
    color: Colors.text,
  },
  categoryButtonTextActive: {
    color: Colors.white,
  },
  rowContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  rowItem: {
    flex: 1,
    marginRight: 8,
  },
  textArea: {
    height: 100,
    textAlignVertical: 'top',
  },
  optionButton: {
    flex: 1,
    paddingVertical: 12,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 16,
  },
  optionButtonActive: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  optionButtonText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
  },
  optionButtonTextActive: {
    color: Colors.white,
  },
  deliveryContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 16,
  },
  deliveryButton: {
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    backgroundColor: Colors.card,
    marginRight: 8,
    marginBottom: 8,
  },
  deliveryButtonActive: {
    backgroundColor: Colors.primary,
  },
  deliveryButtonText: {
    fontSize: 14,
    color: Colors.text,
  },
  deliveryButtonTextActive: {
    color: Colors.white,
  },
  imagesContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginBottom: 24,
  },
  imageContainer: {
    width: 100,
    height: 100,
    borderRadius: 8,
    marginRight: 8,
    marginBottom: 8,
    position: 'relative',
  },
  image: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  removeButton: {
    position: 'absolute',
    top: 4,
    right: 4,
    backgroundColor: Colors.error,
    borderRadius: 12,
    width: 24,
    height: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  removeButtonText: {
    color: Colors.white,
    fontSize: 12,
    fontWeight: '700',
  },
  addImageButton: {
    width: 100,
    height: 100,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: Colors.border,
    borderStyle: 'dashed',
    alignItems: 'center',
    justifyContent: 'center',
  },
  addImageText: {
    color: Colors.primary,
    fontSize: 14,
    marginTop: 8,
  },
  updateButton: {
    marginBottom: 16,
  },
  deleteButton: {
    marginBottom: 32,
    borderColor: Colors.error,
  },
  deleteButtonText: {
    color: Colors.error,
  },
  errorText: {
    color: Colors.error,
    fontSize: 14,
    marginBottom: 16,
  },
  actionButton: {
    marginTop: 16,
  },
});