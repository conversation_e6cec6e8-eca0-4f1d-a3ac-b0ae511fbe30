import React, { useState, useEffect } from 'react';
import { View, Text, StyleSheet, FlatList, TouchableOpacity, SafeAreaView } from 'react-native';
import { Stack, useRouter, useLocalSearchParams } from 'expo-router';
import { Filter, SlidersHorizontal, MapPin, X } from 'lucide-react-native';
import { useProduceStore } from '@/store/produceStore';
import Colors from '@/constants/colors';
import SearchBar from '@/components/produce/SearchBar';
import ProduceCard from '@/components/produce/ProduceCard';
import Button from '@/components/ui/Button';
import EmptyState from '@/components/ui/EmptyState';

export default function SearchScreen() {
  const router = useRouter();
  const params = useLocalSearchParams<{ query?: string; category?: string }>();
  
  const { 
    produces, 
    filteredProduces, 
    fetchProduces, 
    searchProduces, 
    filterByCategory,
    searchQuery,
    selectedCategory,
    isLoading 
  } = useProduceStore();
  
  const [refreshing, setRefreshing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);
  const [sortBy, setSortBy] = useState<'price' | 'date' | 'distance'>('date');
  
  useEffect(() => {
    fetchProduces();
    
    if (params.query) {
      searchProduces(params.query);
    }
    
    if (params.category) {
      filterByCategory(params.category);
    }
  }, [params]);
  
  const handleRefresh = async () => {
    setRefreshing(true);
    await fetchProduces();
    setRefreshing(false);
  };
  
  const handleSearch = (text: string) => {
    searchProduces(text);
  };
  
  const toggleFilters = () => {
    setShowFilters(!showFilters);
  };
  
  const handleSortChange = (sort: 'price' | 'date' | 'distance') => {
    setSortBy(sort);
    setShowFilters(false);
  };
  
  const resetFilters = () => {
    setSortBy('date');
    searchProduces('');
    filterByCategory(null);
    setShowFilters(false);
  };
  
  // Sort the produces based on the selected sort option
  const sortedProduces = [...filteredProduces].sort((a, b) => {
    if (sortBy === 'price') {
      return a.price - b.price;
    } else if (sortBy === 'date') {
      return new Date(b.harvestDate).getTime() - new Date(a.harvestDate).getTime();
    }
    return 0;
  });

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <SearchBar 
          value={searchQuery} 
          onChangeText={handleSearch} 
        />
        
        <View style={styles.filtersRow}>
          <Text style={styles.resultsText}>
            {filteredProduces.length} {filteredProduces.length === 1 ? 'result' : 'results'}
            {selectedCategory ? ` in ${selectedCategory}` : ''}
          </Text>
          
          <TouchableOpacity style={styles.filterButton} onPress={toggleFilters}>
            <SlidersHorizontal size={18} color={Colors.primary} />
            <Text style={styles.filterButtonText}>Sort</Text>
          </TouchableOpacity>
        </View>
      </View>
      
      {showFilters && (
        <View style={styles.filtersOverlay}>
          <View style={styles.filtersModal}>
            <View style={styles.filtersHeader}>
              <Text style={styles.filtersTitle}>Sort By</Text>
              <TouchableOpacity onPress={toggleFilters}>
                <X size={24} color={Colors.text} />
              </TouchableOpacity>
            </View>
            
            <View style={styles.sortOptions}>
              <TouchableOpacity 
                style={[styles.sortOption, sortBy === 'date' && styles.sortOptionActive]}
                onPress={() => handleSortChange('date')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'date' && styles.sortOptionTextActive]}>
                  Newest First
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.sortOption, sortBy === 'price' && styles.sortOptionActive]}
                onPress={() => handleSortChange('price')}
              >
                <Text style={[styles.sortOptionText, sortBy === 'price' && styles.sortOptionTextActive]}>
                  Price: Low to High
                </Text>
              </TouchableOpacity>
              
              <TouchableOpacity 
                style={[styles.sortOption, sortBy === 'distance' && styles.sortOptionActive]}
                onPress={() => handleSortChange('distance')}
              >
                <View style={styles.sortOptionWithIcon}>
                  <MapPin size={16} color={sortBy === 'distance' ? Colors.white : Colors.text} />
                  <Text style={[styles.sortOptionText, sortBy === 'distance' && styles.sortOptionTextActive]}>
                    Distance
                  </Text>
                </View>
              </TouchableOpacity>
            </View>
            
            <Button 
              title="Reset Filters" 
              onPress={resetFilters} 
              variant="outline"
              style={styles.resetButton}
            />
          </View>
        </View>
      )}
      
      <View style={styles.content}>
        {isLoading && filteredProduces.length === 0 ? (
          <View style={styles.loadingContainer}>
            <Text style={styles.loadingText}>Searching...</Text>
          </View>
        ) : filteredProduces.length === 0 ? (
          <EmptyState
            title="No results found"
            description="Try adjusting your search or filters"
            icon={<Filter size={48} color={Colors.textLight} />}
            buttonTitle="Clear Search"
            onButtonPress={resetFilters}
          />
        ) : (
          <FlatList
            data={sortedProduces}
            renderItem={({ item }) => <ProduceCard produce={item} />}
            keyExtractor={(item) => item.id}
            contentContainerStyle={styles.listContent}
            onRefresh={handleRefresh}
            refreshing={refreshing}
            showsVerticalScrollIndicator={false}
          />
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    backgroundColor: Colors.white,
    paddingBottom: 8,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    zIndex: 10,
  },
  filtersRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
  },
  resultsText: {
    fontSize: 14,
    color: Colors.textLight,
    fontWeight: '500',
  },
  filterButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary + '15',
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
  },
  filterButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: Colors.primary,
    marginLeft: 6,
  },
  filtersOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: Colors.overlay,
    zIndex: 100,
    justifyContent: 'flex-end',
  },
  filtersModal: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    padding: 24,
    maxHeight: '50%',
  },
  filtersHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 24,
  },
  filtersTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: Colors.text,
  },
  sortOptions: {
    marginBottom: 24,
  },
  sortOption: {
    backgroundColor: Colors.inputBackground,
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    marginBottom: 12,
  },
  sortOptionActive: {
    backgroundColor: Colors.primary,
  },
  sortOptionText: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
  },
  sortOptionTextActive: {
    color: Colors.white,
  },
  sortOptionWithIcon: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  resetButton: {
    marginTop: 8,
  },
  content: {
    flex: 1,
  },
  listContent: {
    padding: 16,
    paddingBottom: 100,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    fontSize: 16,
    color: Colors.textLight,
    fontWeight: '500',
  },
});