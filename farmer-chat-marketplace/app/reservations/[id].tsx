import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert } from 'react-native';
import { useLocalSearchParams, Stack, useRouter } from 'expo-router';
import { Image } from 'expo-image';
import { useReservationStore } from '@/store/reservationStore';
import { useUserStore } from '@/store/userStore';
import { useProduceStore } from '@/store/produceStore';
import { useMessageStore } from '@/store/messageStore';
import Colors from '@/constants/colors';
import Button from '@/components/ui/Button';
import Badge from '@/components/ui/Badge';
import Avatar from '@/components/ui/Avatar';
import { mockUsers } from '@/mocks/users';

export default function ReservationDetailScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { currentUser } = useUserStore();
  const { getReservationById, updateReservationStatus } = useReservationStore();
  const { getProduceById } = useProduceStore();
  const { startNewConversation } = useMessageStore();
  
  const [loading, setLoading] = useState(false);
  
  const reservation = getReservationById(id);
  const produce = reservation ? getProduceById(reservation.produceId) : null;
  const farmer = mockUsers.find(user => user.id === reservation?.farmerId);
  const buyer = mockUsers.find(user => user.id === reservation?.buyerId);
  
  if (!reservation || !produce || !farmer || !buyer || !currentUser) {
    return (
      <View style={styles.container}>
        <Text>Reservation not found</Text>
      </View>
    );
  }
  
  const isFarmer = currentUser.id === farmer.id;
  const isBuyer = currentUser.id === buyer.id;
  
  const getStatusBadge = () => {
    switch (reservation.status) {
      case 'pending':
        return <Badge label="Pending" variant="warning" />;
      case 'accepted':
        return <Badge label="Accepted" variant="info" />;
      case 'completed':
        return <Badge label="Completed" variant="success" />;
      case 'rejected':
        return <Badge label="Rejected" variant="error" />;
      case 'cancelled':
        return <Badge label="Cancelled" variant="error" />;
      default:
        return null;
    }
  };
  
  const handleStatusUpdate = async (status: 'accepted' | 'rejected' | 'completed' | 'cancelled') => {
    setLoading(true);
    try {
      await updateReservationStatus(reservation.id, status);
      Alert.alert(
        'Status Updated',
        `Reservation has been ${status}`,
        [{ text: 'OK' }]
      );
    } catch (error) {
      Alert.alert('Error', 'Failed to update reservation status');
    } finally {
      setLoading(false);
    }
  };
  
  const handleMessage = async () => {
    if (!currentUser) return;
    
    const otherUserId = isFarmer ? buyer.id : farmer.id;
    
    setLoading(true);
    try {
      const conversationId = await startNewConversation([currentUser.id, otherUserId]);
      router.push(`/messages/${conversationId}`);
    } catch (error) {
      Alert.alert('Error', 'Failed to start conversation');
    } finally {
      setLoading(false);
    }
  };

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      <Stack.Screen 
        options={{ 
          title: 'Reservation Details',
          headerBackTitle: 'Back'
        }} 
      />
      
      <View style={styles.header}>
        <View style={styles.statusContainer}>
          <Text style={styles.reservationId}>Reservation #{reservation.id.slice(-6)}</Text>
          {getStatusBadge()}
        </View>
        <Text style={styles.date}>
          Created on {new Date(reservation.createdAt).toLocaleDateString()}
        </Text>
      </View>
      
      <View style={styles.produceContainer}>
        <Image
          source={{ uri: produce.images[0] }}
          style={styles.produceImage}
          contentFit="cover"
        />
        <View style={styles.produceInfo}>
          <Text style={styles.produceTitle}>{produce.title}</Text>
          <Text style={styles.producePrice}>${produce.price.toFixed(2)} / {produce.unit}</Text>
        </View>
      </View>
      
      <View style={styles.detailsContainer}>
        <Text style={styles.sectionTitle}>Reservation Details</Text>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Quantity:</Text>
          <Text style={styles.detailValue}>{reservation.quantity} {produce.unit}</Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Price:</Text>
          <Text style={styles.detailValue}>
            ${(reservation.proposedPrice || produce.price).toFixed(2)} / {produce.unit}
          </Text>
        </View>
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Total:</Text>
          <Text style={styles.detailValueHighlight}>
            ${((reservation.proposedPrice || produce.price) * reservation.quantity).toFixed(2)}
          </Text>
        </View>
        
        {reservation.deliveryPreference && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Delivery:</Text>
            <Text style={styles.detailValue}>
              {reservation.deliveryPreference.charAt(0).toUpperCase() + 
               reservation.deliveryPreference.slice(1)}
            </Text>
          </View>
        )}
        
        {reservation.paymentTerms && (
          <View style={styles.detailRow}>
            <Text style={styles.detailLabel}>Payment Terms:</Text>
            <Text style={styles.detailValue}>
              {reservation.paymentTerms.charAt(0).toUpperCase() + 
               reservation.paymentTerms.slice(1)}
            </Text>
          </View>
        )}
        
        <View style={styles.detailRow}>
          <Text style={styles.detailLabel}>Harvest Date:</Text>
          <Text style={styles.detailValue}>
            {new Date(produce.harvestDate).toLocaleDateString()}
          </Text>
        </View>
      </View>
      
      <View style={styles.userContainer}>
        <Text style={styles.sectionTitle}>
          {isFarmer ? 'Buyer' : 'Farmer'}
        </Text>
        
        <View style={styles.userCard}>
          <Avatar 
            source={isFarmer ? buyer.avatar : farmer.avatar} 
            name={isFarmer ? buyer.name : farmer.name} 
            size="md" 
            verified={isFarmer ? buyer.verified : farmer.verified}
          />
          <View style={styles.userInfo}>
            <Text style={styles.userName}>{isFarmer ? buyer.name : farmer.name}</Text>
            <Text style={styles.userType}>
              {isFarmer ? 'Buyer' : 'Farmer'}
            </Text>
          </View>
        </View>
      </View>
      
      <View style={styles.actionsContainer}>
        <Button 
          title="Message" 
          onPress={handleMessage} 
          variant="outline"
          style={styles.messageButton}
          loading={loading}
        />
        
        {isFarmer && reservation.status === 'pending' && (
          <View style={styles.farmerActions}>
            <Button 
              title="Accept" 
              onPress={() => handleStatusUpdate('accepted')} 
              style={styles.acceptButton}
              loading={loading}
            />
            <Button 
              title="Reject" 
              onPress={() => handleStatusUpdate('rejected')} 
              variant="outline"
              style={styles.rejectButton}
              loading={loading}
            />
          </View>
        )}
        
        {isFarmer && reservation.status === 'accepted' && (
          <Button 
            title="Mark as Completed" 
            onPress={() => handleStatusUpdate('completed')} 
            style={styles.completeButton}
            loading={loading}
          />
        )}
        
        {isBuyer && reservation.status === 'pending' && (
          <Button 
            title="Cancel Reservation" 
            onPress={() => handleStatusUpdate('cancelled')} 
            variant="outline"
            style={styles.cancelButton}
            loading={loading}
          />
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    padding: 16,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  statusContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  reservationId: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
  },
  date: {
    fontSize: 14,
    color: Colors.textLight,
  },
  produceContainer: {
    flexDirection: 'row',
    padding: 16,
    backgroundColor: Colors.white,
    marginTop: 12,
  },
  produceImage: {
    width: 80,
    height: 80,
    borderRadius: 8,
  },
  produceInfo: {
    flex: 1,
    marginLeft: 16,
    justifyContent: 'center',
  },
  produceTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  producePrice: {
    fontSize: 14,
    color: Colors.primary,
    fontWeight: '500',
  },
  detailsContainer: {
    padding: 16,
    backgroundColor: Colors.white,
    marginTop: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  detailLabel: {
    fontSize: 14,
    color: Colors.textLight,
  },
  detailValue: {
    fontSize: 14,
    color: Colors.text,
    fontWeight: '500',
  },
  detailValueHighlight: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '700',
  },
  userContainer: {
    padding: 16,
    backgroundColor: Colors.white,
    marginTop: 12,
  },
  userCard: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  userInfo: {
    marginLeft: 12,
  },
  userName: {
    fontSize: 16,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 4,
  },
  userType: {
    fontSize: 14,
    color: Colors.textLight,
  },
  actionsContainer: {
    padding: 16,
    backgroundColor: Colors.white,
    marginTop: 12,
    marginBottom: 24,
  },
  messageButton: {
    marginBottom: 16,
  },
  farmerActions: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  acceptButton: {
    flex: 1,
    marginRight: 8,
  },
  rejectButton: {
    flex: 1,
    marginLeft: 8,
  },
  completeButton: {
    width: '100%',
  },
  cancelButton: {
    width: '100%',
  },
});