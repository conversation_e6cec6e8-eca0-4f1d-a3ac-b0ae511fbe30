import React, { useState } from 'react';
import { View, Text, StyleSheet, ScrollView, Alert, TouchableOpacity } from 'react-native';
import { useLocalSearchParams, Stack, useRouter } from 'expo-router';
import { useReservationStore } from '@/store/reservationStore';
import { useUserStore } from '@/store/userStore';
import Colors from '@/constants/colors';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';
import Rating from '@/components/ui/Rating';
import { mockUsers } from '@/mocks/users';
import { ArrowLeft } from 'lucide-react-native';

export default function RateReservationScreen() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const router = useRouter();
  const { getReservationById } = useReservationStore();
  const { currentUser } = useUserStore();
  
  const [rating, setRating] = useState(5);
  const [comment, setComment] = useState('');
  const [loading, setLoading] = useState(false);
  const [paymentOnTime, setPaymentOnTime] = useState(true);
  const [wouldDoBusinessAgain, setWouldDoBusinessAgain] = useState(true);
  
  const reservation = getReservationById(id);
  
  if (!reservation || !currentUser) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <Text style={styles.errorText}>Reservation not found</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.backButton}
        />
      </View>
    );
  }
  
  // Determine who is being rated
  const isFarmer = currentUser.id === reservation.farmerId;
  const userToRate = mockUsers.find(user => 
    user.id === (isFarmer ? reservation.buyerId : reservation.farmerId)
  );
  
  if (!userToRate) {
    return (
      <View style={styles.container}>
        <Stack.Screen options={{ headerShown: false }} />
        <Text style={styles.errorText}>User not found</Text>
        <Button 
          title="Go Back" 
          onPress={() => router.back()} 
          style={styles.backButton}
        />
      </View>
    );
  }
  
  const handleRatingChange = (value: number) => {
    setRating(value);
  };
  
  const handleSubmit = () => {
    setLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setLoading(false);
      Alert.alert(
        'Rating Submitted',
        'Thank you for your feedback!',
        [{ text: 'OK', onPress: () => router.back() }]
      );
    }, 1000);
  };

  return (
    <ScrollView style={styles.container} contentContainerStyle={styles.contentContainer}>
      <Stack.Screen options={{ headerShown: false }} />
      
      {/* Custom Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backButton}
          onPress={() => router.back()}
        >
          <ArrowLeft size={24} color={Colors.text} />
        </TouchableOpacity>
        <Text style={styles.headerTitle}>Rate Experience</Text>
        <View style={styles.headerSpacer} />
      </View>
      
      <Text style={styles.title}>
        Rate your experience with {userToRate.name}
      </Text>
      
      <Text style={styles.subtitle}>
        Your feedback helps build trust in our community
      </Text>
      
      <View style={styles.ratingContainer}>
        <Rating
          initialValue={rating}
          onRatingChange={handleRatingChange}
          size={40}
        />
        <Text style={styles.ratingText}>
          {rating === 5 ? 'Excellent' : 
           rating === 4 ? 'Very Good' : 
           rating === 3 ? 'Good' : 
           rating === 2 ? 'Fair' : 'Poor'}
        </Text>
      </View>
      
      <Input
        label="Comments (Optional)"
        placeholder="Share your experience..."
        value={comment}
        onChangeText={setComment}
        multiline
        numberOfLines={4}
        inputStyle={styles.commentInput}
      />
      
      <View style={styles.questionContainer}>
        <Text style={styles.questionLabel}>
          Was the {isFarmer ? 'payment made on time' : 'produce as described'}?
        </Text>
        <View style={styles.questionButtons}>
          <Button
            title="Yes"
            variant="outline"
            style={[
              styles.questionButton, 
              paymentOnTime && styles.questionButtonActive
            ]}
            onPress={() => setPaymentOnTime(true)}
          />
          <Button
            title="No"
            variant="outline"
            style={[
              styles.questionButton,
              !paymentOnTime && styles.questionButtonActive
            ]}
            onPress={() => setPaymentOnTime(false)}
          />
        </View>
      </View>
      
      <View style={styles.questionContainer}>
        <Text style={styles.questionLabel}>
          Would you do business with {userToRate.name} again?
        </Text>
        <View style={styles.questionButtons}>
          <Button
            title="Yes"
            variant="outline"
            style={[
              styles.questionButton, 
              wouldDoBusinessAgain && styles.questionButtonActive
            ]}
            onPress={() => setWouldDoBusinessAgain(true)}
          />
          <Button
            title="No"
            variant="outline"
            style={[
              styles.questionButton,
              !wouldDoBusinessAgain && styles.questionButtonActive
            ]}
            onPress={() => setWouldDoBusinessAgain(false)}
          />
        </View>
      </View>
      
      <Button
        title="Submit Rating"
        onPress={handleSubmit}
        style={styles.submitButton}
        loading={loading}
        size="lg"
      />
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingVertical: 12,
    paddingTop: 50,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  backButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.text,
    flex: 1,
    textAlign: 'center',
  },
  headerSpacer: {
    width: 40,
  },
  contentContainer: {
    padding: 16,
  },
  title: {
    fontSize: 22,
    fontWeight: '700',
    color: Colors.text,
    marginBottom: 8,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    color: Colors.textLight,
    marginBottom: 24,
    textAlign: 'center',
  },
  ratingContainer: {
    alignItems: 'center',
    marginBottom: 24,
  },
  ratingText: {
    fontSize: 18,
    fontWeight: '600',
    color: Colors.warning,
    marginTop: 8,
  },
  commentInput: {
    height: 120,
    textAlignVertical: 'top',
  },
  questionContainer: {
    marginTop: 16,
    marginBottom: 8,
  },
  questionLabel: {
    fontSize: 16,
    fontWeight: '500',
    color: Colors.text,
    marginBottom: 12,
  },
  questionButtons: {
    flexDirection: 'row',
  },
  questionButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  questionButtonActive: {
    backgroundColor: Colors.primary + '20',
    borderColor: Colors.primary,
  },
  submitButton: {
    marginTop: 32,
    marginBottom: 24,
  },
  errorText: {
    fontSize: 16,
    color: Colors.error,
    textAlign: 'center',
    marginTop: 24,
  },
});