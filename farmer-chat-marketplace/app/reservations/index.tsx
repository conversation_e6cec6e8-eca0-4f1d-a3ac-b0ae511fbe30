import React, { useEffect } from 'react';
import { View, Text, StyleSheet, FlatList } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import { useReservationStore } from '@/store/reservationStore';
import Colors from '@/constants/colors';
import ReservationCard from '@/components/reservation/ReservationCard';
import Button from '@/components/ui/Button';

export default function ReservationsScreen() {
  const router = useRouter();
  const { currentUser, isAuthenticated } = useUserStore();
  const { 
    reservations, 
    fetchReservations, 
    getReservationsByFarmerId, 
    getReservationsByBuyerId,
    isLoading 
  } = useReservationStore();
  
  useEffect(() => {
    if (isAuthenticated) {
      fetchReservations();
    }
  }, [isAuthenticated]);
  
  const handleLogin = () => {
    router.push('/auth/login');
  };
  
  if (!isAuthenticated || !currentUser) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>Sign in to view reservations</Text>
        <Text style={styles.emptySubtitle}>
          Track your produce reservations and manage orders
        </Text>
        <Button 
          title="Sign In" 
          onPress={handleLogin} 
          style={styles.signInButton}
        />
      </View>
    );
  }
  
  const userReservations = currentUser.type === 'farmer'
    ? getReservationsByFarmerId(currentUser.id)
    : getReservationsByBuyerId(currentUser.id);
  
  if (isLoading && userReservations.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>Loading reservations...</Text>
      </View>
    );
  }
  
  if (userReservations.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyTitle}>No reservations yet</Text>
        <Text style={styles.emptySubtitle}>
          {currentUser.type === 'farmer'
            ? 'When buyers reserve your produce, they will appear here'
            : 'Reserve produce from the marketplace to see them here'}
        </Text>
        <Button 
          title="Browse Marketplace" 
          onPress={() => router.push('/marketplace')} 
          style={styles.browseButton}
        />
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <Stack.Screen 
        options={{ 
          title: currentUser.type === 'farmer' ? 'Incoming Reservations' : 'Your Reservations',
          headerBackTitle: 'Back'
        }} 
      />
      
      <FlatList
        data={userReservations}
        renderItem={({ item }) => (
          <ReservationCard 
            reservation={item} 
            showFarmer={currentUser.type === 'buyer'}
            showBuyer={currentUser.type === 'farmer'}
          />
        )}
        keyExtractor={(item) => item.id}
        contentContainerStyle={styles.listContent}
      />
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  listContent: {
    paddingVertical: 8,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
    backgroundColor: Colors.background,
  },
  emptyTitle: {
    fontSize: 20,
    fontWeight: '600',
    color: Colors.text,
    marginBottom: 12,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: 16,
    color: Colors.textLight,
    textAlign: 'center',
    marginBottom: 24,
  },
  signInButton: {
    minWidth: 200,
  },
  browseButton: {
    minWidth: 200,
  },
});