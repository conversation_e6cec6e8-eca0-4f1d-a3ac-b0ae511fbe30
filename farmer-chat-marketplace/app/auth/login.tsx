import React, { useState } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Image, Alert } from 'react-native';
import { Stack, useRouter } from 'expo-router';
import { useUserStore } from '@/store/userStore';
import Colors from '@/constants/colors';
import Input from '@/components/ui/Input';
import Button from '@/components/ui/Button';

export default function LoginScreen() {
  const router = useRouter();
  const { login, isLoading } = useUserStore();
  
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [errors, setErrors] = useState<{ email?: string; password?: string }>({});
  
  const validate = () => {
    const newErrors: { email?: string; password?: string } = {};
    
    if (!email) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!password) {
      newErrors.password = 'Password is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };
  
  const handleLogin = async () => {
    if (!validate()) return;
    
    try {
      await login(email, password);
      router.replace('/');
    } catch (error) {
      Alert.alert('Login Failed', 'Invalid email or password');
    }
  };
  
  const handleDemoLogin = async (userType: 'farmer' | 'buyer') => {
    try {
      if (userType === 'farmer') {
        await login('<EMAIL>', 'password');
      } else {
        await login('<EMAIL>', 'password');
      }
      router.replace('/');
    } catch (error) {
      Alert.alert('Login Failed', 'Could not log in with demo account');
    }
  };
  
  const navigateToRegister = () => {
    router.push('/auth/register');
  };

  return (
    <View style={styles.container}>
      <Stack.Screen options={{ title: 'Sign In', headerBackTitle: 'Back' }} />
      
      <View style={styles.logoContainer}>
        <Image
          source={{ uri: 'https://images.unsplash.com/photo-*************-501348b61469?ixlib=rb-1.2.1&auto=format&fit=crop&w=500&q=60' }}
          style={styles.logo}
        />
        <Text style={styles.logoText}>Farmer Chat</Text>
      </View>
      
      <View style={styles.formContainer}>
        <Input
          label="Email"
          placeholder="Enter your email"
          value={email}
          onChangeText={setEmail}
          keyboardType="email-address"
          autoCapitalize="none"
          error={errors.email}
        />
        
        <Input
          label="Password"
          placeholder="Enter your password"
          value={password}
          onChangeText={setPassword}
          secureTextEntry
          error={errors.password}
        />
        
        <Button
          title="Sign In"
          onPress={handleLogin}
          loading={isLoading}
          style={styles.button}
          fullWidth
        />
        
        <View style={styles.demoContainer}>
          <Text style={styles.demoText}>Try a demo account:</Text>
          <View style={styles.demoButtons}>
            <Button
              title="Farmer Demo"
              onPress={() => handleDemoLogin('farmer')}
              variant="outline"
              style={styles.demoButton}
              loading={isLoading}
            />
            <Button
              title="Buyer Demo"
              onPress={() => handleDemoLogin('buyer')}
              variant="outline"
              style={styles.demoButton}
              loading={isLoading}
            />
          </View>
        </View>
      </View>
      
      <View style={styles.footer}>
        <Text style={styles.footerText}>Don't have an account?</Text>
        <TouchableOpacity onPress={navigateToRegister}>
          <Text style={styles.footerLink}>Sign Up</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    padding: 24,
  },
  logoContainer: {
    alignItems: 'center',
    marginVertical: 40,
  },
  logo: {
    width: 100,
    height: 100,
    borderRadius: 50,
  },
  logoText: {
    fontSize: 24,
    fontWeight: '700',
    color: Colors.primary,
    marginTop: 16,
  },
  formContainer: {
    marginBottom: 24,
  },
  button: {
    marginTop: 16,
  },
  demoContainer: {
    marginTop: 32,
    alignItems: 'center',
  },
  demoText: {
    fontSize: 16,
    color: Colors.text,
    marginBottom: 16,
  },
  demoButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },
  demoButton: {
    flex: 1,
    marginHorizontal: 8,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginTop: 'auto',
  },
  footerText: {
    fontSize: 16,
    color: Colors.text,
  },
  footerLink: {
    fontSize: 16,
    color: Colors.primary,
    fontWeight: '600',
    marginLeft: 8,
  },
});