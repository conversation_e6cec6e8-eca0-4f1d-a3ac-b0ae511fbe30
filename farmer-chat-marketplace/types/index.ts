export type UserType = 'farmer' | 'buyer';

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  type: UserType;
  location?: {
    latitude: number;
    longitude: number;
    address: string;
  };
  avatar?: string;
  verified: boolean;
  rating: number;
  createdAt: string;
}

export interface FarmerProfile extends User {
  type: 'farmer';
  farmSize?: string;
  farmType?: string;
  crops?: string[];
  bankDetails?: {
    accountName: string;
    accountNumber: string;
    bankName: string;
  };
}

export interface BuyerProfile extends User {
  type: 'buyer';
  businessType?: string;
  businessScale?: string;
  preferredPaymentMethods?: string[];
  purchasePreferences?: string[];
}

export type ProduceCategory = 
  | 'vegetables' 
  | 'fruits' 
  | 'grains' 
  | 'dairy' 
  | 'meat' 
  | 'poultry' 
  | 'other';

export interface Produce {
  id: string;
  farmerId: string;
  title: string;
  category: ProduceCategory;
  variety: string;
  grade?: string;
  quantity: number;
  unit: string;
  price: number;
  priceUnit: string;
  harvestDate: string;
  description: string;
  images: string[];
  growingPractice: 'organic' | 'conventional' | 'other';
  deliveryOptions: string[];
  minOrderQuantity: number;
  available: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface Reservation {
  id: string;
  produceId: string;
  buyerId: string;
  farmerId: string;
  quantity: number;
  proposedPrice?: number;
  status: 'pending' | 'accepted' | 'rejected' | 'completed' | 'cancelled';
  deliveryPreference?: string;
  paymentTerms?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  id: string;
  senderId: string;
  receiverId: string;
  content: string;
  timestamp: string;
  read: boolean;
  reservationId?: string;
}

export interface Conversation {
  id: string;
  participants: string[];
  lastMessage?: Message;
  unreadCount: number;
  updatedAt: string;
}

export interface Rating {
  id: string;
  fromUserId: string;
  toUserId: string;
  reservationId: string;
  score: number;
  comment?: string;
  createdAt: string;
}