{"version": 3, "sources": ["utils.ts"], "names": ["Platform", "findNodeHandle", "findNodeHandleRN", "handlerIDToTag", "toArray", "RNGestureHandlerModule", "ghQueueMicrotask", "isConfigParam", "param", "name", "undefined", "Object", "filterConfig", "props", "validProps", "defaults", "filteredConfig", "key", "value", "transformIntoHandlerTags", "top", "left", "bottom", "right", "handlerIDs", "OS", "map", "current", "filter", "handle", "handlerID", "handlerTag", "node", "flushOperationsScheduled", "scheduleFlushOperations", "flushOperations"], "mappings": "AACA,SAASA,QAAT,EAAmBC,cAAc,IAAIC,gBAArC,QAA6D,cAA7D;AACA,SAASC,cAAT,QAA+B,oBAA/B;AACA,SAASC,OAAT,QAAwB,UAAxB;AACA,OAAOC,sBAAP,MAAmC,2BAAnC;AACA,SAASC,gBAAT,QAAiC,qBAAjC;;AAEA,SAASC,aAAT,CAAuBC,KAAvB,EAAuCC,IAAvC,EAAqD;AACnD;AACA;AACA,SACED,KAAK,KAAKE,SAAV,KACCF,KAAK,KAAKG,MAAM,CAACH,KAAD,CAAhB,IACC,EAAE,gBAAiBA,KAAnB,CAFF,KAGAC,IAAI,KAAK,sBAHT,IAIAA,IAAI,KAAK,gBALX;AAOD;;AAED,OAAO,SAASG,YAAT,CACLC,KADK,EAELC,UAFK,EAGLC,QAAiC,GAAG,EAH/B,EAIL;AACA,QAAMC,cAAc,GAAG,EAAE,GAAGD;AAAL,GAAvB;;AACA,OAAK,MAAME,GAAX,IAAkBH,UAAlB,EAA8B;AAC5B,QAAII,KAAK,GAAGL,KAAK,CAACI,GAAD,CAAjB;;AACA,QAAIV,aAAa,CAACW,KAAD,EAAQD,GAAR,CAAjB,EAA+B;AAC7B,UAAIA,GAAG,KAAK,sBAAR,IAAkCA,GAAG,KAAK,SAA9C,EAAyD;AACvDC,QAAAA,KAAK,GAAGC,wBAAwB,CAACN,KAAK,CAACI,GAAD,CAAN,CAAhC;AACD,OAFD,MAEO,IAAIA,GAAG,KAAK,SAAR,IAAqB,OAAOC,KAAP,KAAiB,QAA1C,EAAoD;AACzDA,QAAAA,KAAK,GAAG;AAAEE,UAAAA,GAAG,EAAEF,KAAP;AAAcG,UAAAA,IAAI,EAAEH,KAApB;AAA2BI,UAAAA,MAAM,EAAEJ,KAAnC;AAA0CK,UAAAA,KAAK,EAAEL;AAAjD,SAAR;AACD;;AACDF,MAAAA,cAAc,CAACC,GAAD,CAAd,GAAsBC,KAAtB;AACD;AACF;;AACD,SAAOF,cAAP;AACD;AAED,OAAO,SAASG,wBAAT,CAAkCK,UAAlC,EAAmD;AACxDA,EAAAA,UAAU,GAAGpB,OAAO,CAACoB,UAAD,CAApB;;AAEA,MAAIxB,QAAQ,CAACyB,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOD,UAAU,CACdE,GADI,CACA,CAAC;AAAEC,MAAAA;AAAF,KAAD,KAAmCA,OADnC,EAEJC,MAFI,CAEIC,MAAD,IAAiBA,MAFpB,CAAP;AAGD,GAPuD,CAQxD;;;AACA,SAAOL,UAAU,CACdE,GADI,CAEFI,SAAD;AAAA;;AAAA,WACE3B,cAAc,CAAC2B,SAAD,CAAd,2BAA6BA,SAAS,CAACH,OAAvC,uDAA6B,mBAAmBI,UAAhD,KAA8D,CAAC,CADjE;AAAA,GAFG,EAKJH,MALI,CAKIG,UAAD,IAAwBA,UAAU,GAAG,CALxC,CAAP;AAMD;AAED,OAAO,SAAS9B,cAAT,CACL+B,IADK,EAEkE;AACvE,MAAIhC,QAAQ,CAACyB,EAAT,KAAgB,KAApB,EAA2B;AACzB,WAAOO,IAAP;AACD;;AACD,SAAO9B,gBAAgB,CAAC8B,IAAD,CAAvB;AACD;AACD,IAAIC,wBAAwB,GAAG,KAA/B;AAEA,OAAO,SAASC,uBAAT,GAAmC;AACxC,MAAI,CAACD,wBAAL,EAA+B;AAC7BA,IAAAA,wBAAwB,GAAG,IAA3B;AACA3B,IAAAA,gBAAgB,CAAC,MAAM;AACrBD,MAAAA,sBAAsB,CAAC8B,eAAvB;AAEAF,MAAAA,wBAAwB,GAAG,KAA3B;AACD,KAJe,CAAhB;AAKD;AACF", "sourcesContent": ["import * as React from 'react';\nimport { Platform, findNodeHandle as findNodeHandleRN } from 'react-native';\nimport { handlerIDToTag } from './handlersRegistry';\nimport { toArray } from '../utils';\nimport RNGestureHandlerModule from '../RNGestureHandlerModule';\nimport { ghQueueMicrotask } from '../ghQueueMicrotask';\n\nfunction isConfigParam(param: unknown, name: string) {\n  // param !== Object(param) returns false if `param` is a function\n  // or an object and returns true if `param` is null\n  return (\n    param !== undefined &&\n    (param !== Object(param) ||\n      !('__isNative' in (param as Record<string, unknown>))) &&\n    name !== 'onHandlerStateChange' &&\n    name !== 'onGestureEvent'\n  );\n}\n\nexport function filterConfig(\n  props: Record<string, unknown>,\n  validProps: string[],\n  defaults: Record<string, unknown> = {}\n) {\n  const filteredConfig = { ...defaults };\n  for (const key of validProps) {\n    let value = props[key];\n    if (isConfigParam(value, key)) {\n      if (key === 'simultaneousHandlers' || key === 'waitFor') {\n        value = transformIntoHandlerTags(props[key]);\n      } else if (key === 'hitSlop' && typeof value !== 'object') {\n        value = { top: value, left: value, bottom: value, right: value };\n      }\n      filteredConfig[key] = value;\n    }\n  }\n  return filteredConfig;\n}\n\nexport function transformIntoHandlerTags(handlerIDs: any) {\n  handlerIDs = toArray(handlerIDs);\n\n  if (Platform.OS === 'web') {\n    return handlerIDs\n      .map(({ current }: { current: any }) => current)\n      .filter((handle: any) => handle);\n  }\n  // converts handler string IDs into their numeric tags\n  return handlerIDs\n    .map(\n      (handlerID: any) =>\n        handlerIDToTag[handlerID] || handlerID.current?.handlerTag || -1\n    )\n    .filter((handlerTag: number) => handlerTag > 0);\n}\n\nexport function findNodeHandle(\n  node: null | number | React.Component<any, any> | React.ComponentClass<any>\n): null | number | React.Component<any, any> | React.ComponentClass<any> {\n  if (Platform.OS === 'web') {\n    return node;\n  }\n  return findNodeHandleRN(node);\n}\nlet flushOperationsScheduled = false;\n\nexport function scheduleFlushOperations() {\n  if (!flushOperationsScheduled) {\n    flushOperationsScheduled = true;\n    ghQueueMicrotask(() => {\n      RNGestureHandlerModule.flushOperations();\n\n      flushOperationsScheduled = false;\n    });\n  }\n}\n"]}