{"version": 3, "sources": ["utils.ts"], "names": ["React", "toArray", "object", "Array", "isArray", "withPrevAndCurrent", "array", "mapFn", "previousArr", "currentArr", "transformedArr", "for<PERSON>ach", "current", "i", "previous", "transformed", "push", "hasProperty", "key", "Object", "prototype", "hasOwnProperty", "call", "isTestEnv", "global", "process", "env", "NODE_ENV", "tagMessage", "msg", "isF<PERSON><PERSON>", "nativeFabricUIManager", "isReact19", "version", "startsWith", "isRemoteDebuggingEnabled", "localGlobal", "nativeCallSyncHook", "__REMOTEDEV__", "RN$Bridgeless", "deepEqual", "obj1", "obj2", "keys1", "keys", "keys2", "length", "includes", "INT32_MAX"], "mappings": "AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,OAAO,SAASC,OAAT,CAAoBC,MAApB,EAA0C;AAC/C,MAAI,CAACC,KAAK,CAACC,OAAN,CAAcF,MAAd,CAAL,EAA4B;AAC1B,WAAO,CAACA,MAAD,CAAP;AACD;;AAED,SAAOA,MAAP;AACD;AAMD,OAAO,SAASG,kBAAT,CACLC,KADK,EAELC,KAFK,EAGU;AACf,QAAMC,WAAmC,GAAG,CAAC,IAAD,CAA5C;AACA,QAAMC,UAAU,GAAG,CAAC,GAAGH,KAAJ,CAAnB;AACA,QAAMI,cAA6B,GAAG,EAAtC;AACAD,EAAAA,UAAU,CAACE,OAAX,CAAmB,CAACC,OAAD,EAAUC,CAAV,KAAgB;AACjC;AACA;AACA;AACA,UAAMC,QAAQ,GAAGN,WAAW,CAACK,CAAD,CAA5B;AACA,UAAME,WAAW,GAAGR,KAAK,CAACO,QAAD,EAAWF,OAAX,CAAzB;AACAJ,IAAAA,WAAW,CAACQ,IAAZ,CAAiBD,WAAjB;AACAL,IAAAA,cAAc,CAACM,IAAf,CAAoBD,WAApB;AACD,GARD;AASA,SAAOL,cAAP;AACD,C,CAED;;AACA,OAAO,SAASO,WAAT,CAAqBf,MAArB,EAAqCgB,GAArC,EAAkD;AACvD,SAAOC,MAAM,CAACC,SAAP,CAAiBC,cAAjB,CAAgCC,IAAhC,CAAqCpB,MAArC,EAA6CgB,GAA7C,CAAP;AACD;AAED,OAAO,SAASK,SAAT,GAA8B;AACnC;AACA,SAAON,WAAW,CAACO,MAAD,EAAS,SAAT,CAAX,IAAkCC,OAAO,CAACC,GAAR,CAAYC,QAAZ,KAAyB,MAAlE;AACD;AAED,OAAO,SAASC,UAAT,CAAoBC,GAApB,EAAiC;AACtC,SAAQ,kCAAiCA,GAAI,EAA7C;AACD,C,CAED;AACA;;AACA,OAAO,SAASC,QAAT,GAA6B;AAAA;;AAClC;AACA,SAAO,CAAC,aAACN,MAAD,oCAAC,QAAQO,qBAAT,CAAR;AACD;AAED,OAAO,SAASC,SAAT,GAAqB;AAC1B,SAAOhC,KAAK,CAACiC,OAAN,CAAcC,UAAd,CAAyB,KAAzB,CAAP;AACD;AAED,OAAO,SAASC,wBAAT,GAA6C;AAClD;AACA;AACA,QAAMC,WAAW,GAAGZ,MAApB;AACA,SACE,CAAC,CAACY,WAAW,CAACC,kBAAb,IAAmC,CAAC,CAACD,WAAW,CAACE,aAAlD,KACA,CAACF,WAAW,CAACG,aAFf;AAID;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,OAAO,SAASC,SAAT,CAAmBC,IAAnB,EAA8BC,IAA9B,EAAyC;AAC9C,MAAID,IAAI,KAAKC,IAAb,EAAmB;AACjB,WAAO,IAAP;AACD;;AAED,MACE,OAAOD,IAAP,KAAgB,QAAhB,IACA,OAAOC,IAAP,KAAgB,QADhB,IAEAD,IAAI,KAAK,IAFT,IAGAC,IAAI,KAAK,IAJX,EAKE;AACA,WAAO,KAAP;AACD;;AAED,QAAMC,KAAK,GAAGxB,MAAM,CAACyB,IAAP,CAAYH,IAAZ,CAAd;AACA,QAAMI,KAAK,GAAG1B,MAAM,CAACyB,IAAP,CAAYF,IAAZ,CAAd;;AAEA,MAAIC,KAAK,CAACG,MAAN,KAAiBD,KAAK,CAACC,MAA3B,EAAmC;AACjC,WAAO,KAAP;AACD;;AAED,OAAK,MAAM5B,GAAX,IAAkByB,KAAlB,EAAyB;AACvB,QAAI,CAACE,KAAK,CAACE,QAAN,CAAe7B,GAAf,CAAD,IAAwB,CAACsB,SAAS,CAACC,IAAI,CAACvB,GAAD,CAAL,EAAYwB,IAAI,CAACxB,GAAD,CAAhB,CAAtC,EAA8D;AAC5D,aAAO,KAAP;AACD;AACF;;AAED,SAAO,IAAP;AACD;AAED,OAAO,MAAM8B,SAAS,GAAG,KAAK,EAAL,GAAU,CAA5B", "sourcesContent": ["import React from 'react';\n\nexport function toArray<T>(object: T | T[]): T[] {\n  if (!Array.isArray(object)) {\n    return [object];\n  }\n\n  return object;\n}\n\nexport type withPrevAndCurrentMapFn<T, Transformed> = (\n  previous: Transformed | null,\n  current: T\n) => Transformed;\nexport function withPrevAndCurrent<T, Transformed>(\n  array: T[],\n  mapFn: withPrevAndCurrentMapFn<T, Transformed>\n): Transformed[] {\n  const previousArr: (null | Transformed)[] = [null];\n  const currentArr = [...array];\n  const transformedArr: Transformed[] = [];\n  currentArr.forEach((current, i) => {\n    // This type cast is fine and solves problem mentioned in https://github.com/software-mansion/react-native-gesture-handler/pull/2867 (namely that `previous` can be undefined).\n    // Unfortunately, linter on our CI does not allow this type of casting as it is unnecessary. To bypass that we use eslint-disable.\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-type-assertion\n    const previous = previousArr[i] as Transformed | null;\n    const transformed = mapFn(previous, current);\n    previousArr.push(transformed);\n    transformedArr.push(transformed);\n  });\n  return transformedArr;\n}\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function hasProperty(object: object, key: string) {\n  return Object.prototype.hasOwnProperty.call(object, key);\n}\n\nexport function isTestEnv(): boolean {\n  // @ts-ignore Do not use `@types/node` because it will prioritise Node types over RN types which breaks the types (ex. setTimeout) in React Native projects.\n  return hasProperty(global, 'process') && process.env.NODE_ENV === 'test';\n}\n\nexport function tagMessage(msg: string) {\n  return `[react-native-gesture-handler] ${msg}`;\n}\n\n// Helper method to check whether Fabric is enabled, however global.nativeFabricUIManager\n// may not be initialized before the first render\nexport function isFabric(): boolean {\n  // @ts-expect-error nativeFabricUIManager is not yet included in the RN types\n  return !!global?.nativeFabricUIManager;\n}\n\nexport function isReact19() {\n  return React.version.startsWith('19.');\n}\n\nexport function isRemoteDebuggingEnabled(): boolean {\n  // react-native-reanimated checks if in remote debugging in the same way\n  // @ts-ignore global is available but node types are not included\n  const localGlobal = global as any;\n  return (\n    (!localGlobal.nativeCallSyncHook || !!localGlobal.__REMOTEDEV__) &&\n    !localGlobal.RN$Bridgeless\n  );\n}\n\n/**\n * Recursively compares two objects for deep equality.\n *\n * **Note:** This function does not support cyclic references.\n *\n * @param obj1 - The first object to compare.\n * @param obj2 - The second object to compare.\n * @returns `true` if the objects are deeply equal, `false` otherwise.\n */\nexport function deepEqual(obj1: any, obj2: any) {\n  if (obj1 === obj2) {\n    return true;\n  }\n\n  if (\n    typeof obj1 !== 'object' ||\n    typeof obj2 !== 'object' ||\n    obj1 === null ||\n    obj2 === null\n  ) {\n    return false;\n  }\n\n  const keys1 = Object.keys(obj1);\n  const keys2 = Object.keys(obj2);\n\n  if (keys1.length !== keys2.length) {\n    return false;\n  }\n\n  for (const key of keys1) {\n    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {\n      return false;\n    }\n  }\n\n  return true;\n}\n\nexport const INT32_MAX = 2 ** 31 - 1;\n"]}