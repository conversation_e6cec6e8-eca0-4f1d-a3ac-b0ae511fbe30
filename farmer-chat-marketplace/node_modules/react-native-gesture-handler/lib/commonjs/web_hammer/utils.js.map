{"version": 3, "sources": ["utils.ts"], "names": ["isnan", "v", "Number", "isNaN", "isValidNumber", "TEST_MIN_IF_NOT_NAN", "value", "limit", "VEC_LEN_SQ", "x", "y", "TEST_MAX_IF_NOT_NAN", "max", "fireAfterInterval", "method", "interval", "setTimeout"], "mappings": ";;;;;;;;AAAA;AACO,MAAMA,KAAK,GAAIC,CAAD,IAAgBC,MAAM,CAACC,KAAP,CAAaF,CAAb,CAA9B,C,CAEP;;;;;AACO,MAAMG,aAAa,GAAIH,CAAD,IAC3B,OAAOA,CAAP,KAAa,QAAb,IAAyB,CAACC,MAAM,CAACC,KAAP,CAAaF,CAAb,CADrB;;;;AAGA,MAAMI,mBAAmB,GAAG,CAACC,KAAD,EAAgBC,KAAhB,KACjC,CAACP,KAAK,CAACO,KAAD,CAAN,KACEA,KAAK,GAAG,CAAR,IAAaD,KAAK,IAAIC,KAAvB,IAAkCA,KAAK,IAAI,CAAT,IAAcD,KAAK,IAAIC,KAD1D,CADK;;;;AAGA,MAAMC,UAAU,GAAG,CAAC;AAAEC,EAAAA,CAAC,GAAG,CAAN;AAASC,EAAAA,CAAC,GAAG;AAAb,IAAmB,EAApB,KAA2BD,CAAC,GAAGA,CAAJ,GAAQC,CAAC,GAAGA,CAA1D;;;;AACA,MAAMC,mBAAmB,GAAG,CAACL,KAAD,EAAgBM,GAAhB,KACjC,CAACZ,KAAK,CAACY,GAAD,CAAN,KAAiBA,GAAG,GAAG,CAAN,IAAWN,KAAK,GAAGM,GAApB,IAA6BA,GAAG,IAAI,CAAP,IAAYN,KAAK,GAAGM,GAAjE,CADK;;;;AAGA,SAASC,iBAAT,CACLC,MADK,EAELC,QAFK,EAGL;AACA,MAAI,CAACA,QAAL,EAAe;AACbD,IAAAA,MAAM;AACN,WAAO,IAAP;AACD;;AACD,SAAOE,UAAU,CAAC,MAAMF,MAAM,EAAb,EAAiBC,QAAjB,CAAjB;AACD", "sourcesContent": ["// TODO(TS) remove if not necessary after rewrite\nexport const isnan = (v: unknown) => Number.isNaN(v);\n\n// TODO(TS) remove if not necessary after rewrite\nexport const isValidNumber = (v: unknown) =>\n  typeof v === 'number' && !Number.isNaN(v);\n\nexport const TEST_MIN_IF_NOT_NAN = (value: number, limit: number): boolean =>\n  !isnan(limit) &&\n  ((limit < 0 && value <= limit) || (limit >= 0 && value >= limit));\nexport const VEC_LEN_SQ = ({ x = 0, y = 0 } = {}) => x * x + y * y;\nexport const TEST_MAX_IF_NOT_NAN = (value: number, max: number) =>\n  !isnan(max) && ((max < 0 && value < max) || (max >= 0 && value > max));\n\nexport function fireAfterInterval(\n  method: () => void,\n  interval?: number | boolean\n) {\n  if (!interval) {\n    method();\n    return null;\n  }\n  return setTimeout(() => method(), interval as number);\n}\n"]}