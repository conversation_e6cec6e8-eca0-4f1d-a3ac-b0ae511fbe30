{"version": 3, "file": "codeMod.js", "names": ["_commonCodeMod", "data", "require", "_matchBrackets", "addObjcImports", "source", "imports", "lines", "split", "lineIndexWithFirstImport", "findIndex", "line", "match", "importElement", "includes", "importStatement", "splice", "join", "addSwiftImports", "findObjcInterfaceCodeBlock", "contents", "declaration", "start", "search", "RegExp", "end", "indexOf", "code", "substring", "findObjcFunctionCodeBlock", "selector", "symbols", "argsCount", "length", "pattern", "i", "argSymbol", "findMatchingBracketPosition", "insertContentsInsideObjcFunctionBlock", "srcContents", "insertion", "options", "insertContentsInsideFunctionBlock", "insertContentsInsideObjcInterfaceBlock", "codeBlock", "position", "firstNewLineIndex", "insertContentsAtOffset", "endLen", "findSwiftFunctionCodeBlock", "parenthesesIndex", "funcName", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchOffset", "funcCandidateRegExp", "funcCandidateOffset", "searchFromOffset", "paramsStartOffset", "paramsEndOffset", "paramsString", "params", "map", "parseSwiftFunctionParam", "<PERSON><PERSON><PERSON><PERSON>", "codeBlockStart", "codeBlockEnd", "param<PERSON><PERSON>le", "semiIndex", "parameterName", "typeString", "trim", "insertContentsInsideSwiftClassBlock", "Error", "firstBracketIndex", "endBracketIndex", "insertContentsInsideSwiftFunctionBlock", "language", "indent", "repeat", "lastReturnIndex", "lastIndexOf"], "sources": ["../../src/ios/codeMod.ts"], "sourcesContent": ["import { CodeBlock, insertContentsAtOffset, searchFromOffset } from '../utils/commonCodeMod';\nimport { findMatchingBracketPosition } from '../utils/matchBrackets';\n\ninterface SwiftFunctionParam {\n  argumentLabel: string;\n  parameterName: string;\n  typeString: string;\n}\n\ninterface InsertContentFunctionOptions {\n  position: 'head' | 'tail' | 'tailBeforeLastReturn';\n  indent?: number;\n}\n\n/**\n * Add Objective-C import\n * @param source source contents\n * @param imports array of imports, e.g. ['<Foundation/Foundation.h>']\n * @returns updated contents\n */\nexport function addObjcImports(source: string, imports: string[]): string {\n  const lines = source.split('\\n');\n  // Try to insert statements after first #import where would probably not in #if block\n  const lineIndexWithFirstImport = lines.findIndex((line) => line.match(/^#import .*$/));\n  for (const importElement of imports) {\n    if (!source.includes(importElement)) {\n      const importStatement = `#import ${importElement}`;\n      lines.splice(lineIndexWithFirstImport + 1, 0, importStatement);\n    }\n  }\n  return lines.join('\\n');\n}\n\n/**\n * Add Swift import\n * @param source source contents\n * @param imports array of imports, e.g. ['Expo']\n * @returns updated contents\n */\nexport function addSwiftImports(source: string, imports: string[]): string {\n  const lines = source.split('\\n');\n  // Try to insert statements after first import where would probably not in #if block\n  const lineIndexWithFirstImport = lines.findIndex((line) => line.match(/^import .*$/));\n  for (const importElement of imports) {\n    if (!source.includes(importElement)) {\n      const importStatement = `import ${importElement}`;\n      lines.splice(lineIndexWithFirstImport + 1, 0, importStatement);\n    }\n  }\n  return lines.join('\\n');\n}\n\n/**\n * Find code block of Objective-C interface or implementation\n *\n * @param contents source contents\n * @param declaration interface/implementation, e.g. '@interface Foo'\n * @returns found CodeBlock, or null if not found\n */\nexport function findObjcInterfaceCodeBlock(\n  contents: string,\n  declaration: string\n): CodeBlock | null {\n  const start = contents.search(new RegExp(`^${declaration}\\\\W`, 'm'));\n  if (start < 0) {\n    return null;\n  }\n\n  let end = contents.indexOf('\\n@end', start);\n  end += 5; // '\\n@end'.length === 5\n\n  return {\n    start,\n    end,\n    code: contents.substring(start, end),\n  };\n}\n\n/**\n * Find code block of Objective-C function without declaration, will return only {} block\n *\n * @param contents source contents\n * @param selector function selector, e.g. 'doSomething:withSomeValue:'\n * @returns found CodeBlock, or null if not found.\n */\nexport function findObjcFunctionCodeBlock(contents: string, selector: string): CodeBlock | null {\n  const symbols = selector.split(':');\n  const argsCount = symbols.length - 1;\n  let pattern = '^[\\\\-+]\\\\s*\\\\(.+?\\\\)';\n  if (argsCount === 0) {\n    pattern += `${symbols[0]}\\\\s+`;\n  } else {\n    for (let i = 0; i < argsCount; ++i) {\n      const argSymbol = `${symbols[i]}:\\\\(.+\\\\)\\\\w+`;\n      pattern += `${argSymbol}\\\\s+`;\n    }\n  }\n  pattern += '{';\n  let start = contents.search(new RegExp(pattern, 'm'));\n  if (start < 0) {\n    return null;\n  }\n  start = contents.indexOf('{', start);\n\n  const end = findMatchingBracketPosition(contents, '{', start);\n  return {\n    start,\n    end,\n    code: contents.substring(start, end + 1),\n  };\n}\n\n/**\n * Insert contents to the Objective-C function block\n *\n * @param srcContents source contents\n * @param selector function selector, e.g. 'doSomething:withSomeValue:'\n * @param insertion code to insert\n * @param options insertion options\n * @returns updated contents\n */\nexport function insertContentsInsideObjcFunctionBlock(\n  srcContents: string,\n  selector: string,\n  insertion: string,\n  options: InsertContentFunctionOptions\n): string {\n  return insertContentsInsideFunctionBlock(srcContents, selector, insertion, options, 'objc');\n}\n\n/**\n * Insert contents to the Objective-C interface/implementation block\n *\n * @param srcContents source contents\n * @param declaration interface/implementation, e.g. '@interface Foo'\n * @param insertion code to insert\n * @param options insertion options\n * @returns updated contents\n */\nexport function insertContentsInsideObjcInterfaceBlock(\n  srcContents: string,\n  declaration: string,\n  insertion: string,\n  options: {\n    position: 'head' | 'tail';\n  }\n): string {\n  const codeBlock = findObjcInterfaceCodeBlock(srcContents, declaration);\n  if (!codeBlock) {\n    return srcContents;\n  }\n\n  const { position } = options;\n  if (position === 'head') {\n    const firstNewLineIndex = srcContents.indexOf('\\n', codeBlock.start);\n    srcContents = insertContentsAtOffset(srcContents, insertion, firstNewLineIndex);\n  } else if (position === 'tail') {\n    const endLen = '@end'.length;\n    srcContents = insertContentsAtOffset(srcContents, insertion, codeBlock.end - endLen);\n  }\n  return srcContents;\n}\n\n/**\n * Find code block of Swift function without declaration, will return only {} block\n *\n * @param contents source contents\n * @param selector function selector, e.g. 'doSomething(_:withSomeValue:)'\n * @returns found CodeBlock, or null if not found.\n */\nexport function findSwiftFunctionCodeBlock(contents: string, selector: string): CodeBlock | null {\n  const parenthesesIndex = selector.indexOf('(');\n  // `functName` === 'doSomething' of 'doSomething(_:withSomeValue:)'\n  const funcName = selector.substring(0, parenthesesIndex);\n  // `argLabels` === ['_', 'withSomeValue'] 'doSomething(_:withSomeValue:)'\n  const argLabels = selector.substring(parenthesesIndex + 1, selector.length - 2).split(':');\n\n  let searchOffset = 0;\n  const funcCandidateRegExp = new RegExp(`\\\\sfunc\\\\s+${funcName}\\\\(`, 'm');\n  let funcCandidateOffset = searchFromOffset(contents, funcCandidateRegExp, searchOffset);\n  while (funcCandidateOffset >= 0) {\n    // Parse function parameters\n    const paramsStartOffset = contents.indexOf('(', funcCandidateOffset);\n    const paramsEndOffset = findMatchingBracketPosition(contents, '(', paramsStartOffset);\n    const paramsString = contents.substring(paramsStartOffset + 1, paramsEndOffset);\n    const params = paramsString.split(',').map(parseSwiftFunctionParam);\n\n    // Prepare offset for next round\n    searchOffset = paramsEndOffset + 1;\n    funcCandidateOffset = searchFromOffset(contents, funcCandidateRegExp, searchOffset);\n\n    // Try to match function parameters\n    if (argLabels.length !== params.length) {\n      continue;\n    }\n    for (let i = 0; i < argLabels.length; ++i) {\n      if (argLabels[i] !== params[i].argumentLabel) {\n        continue;\n      }\n    }\n\n    // This function is matched one, get the code block.\n    const codeBlockStart = contents.indexOf('{', paramsEndOffset);\n    const codeBlockEnd = findMatchingBracketPosition(contents, '{', paramsEndOffset);\n    const codeBlock = contents.substring(codeBlockStart, codeBlockEnd + 1);\n    return {\n      start: codeBlockStart,\n      end: codeBlockEnd,\n      code: codeBlock,\n    };\n  }\n\n  return null;\n}\n\nfunction parseSwiftFunctionParam(paramTuple: string): SwiftFunctionParam {\n  const semiIndex = paramTuple.indexOf(':');\n  const [argumentLabel, parameterName] = paramTuple.substring(0, semiIndex).split(/\\s+/);\n  const typeString = paramTuple.substring(semiIndex + 1).trim();\n  return {\n    argumentLabel,\n    parameterName,\n    typeString,\n  };\n}\n\n/**\n * Insert contents to the swift class block\n *\n * @param srcContents source contents\n * @param declaration class/extension declaration, e.g. 'class AppDelegate'\n * @param insertion code to append\n * @param options insertion options\n * @returns updated contents\n */\nexport function insertContentsInsideSwiftClassBlock(\n  srcContents: string,\n  declaration: string,\n  insertion: string,\n  options: {\n    position: 'head' | 'tail';\n  }\n): string {\n  const start = srcContents.search(new RegExp(`\\\\s*${declaration}.*?[\\\\(\\\\{]`));\n  if (start < 0) {\n    throw new Error(`Unable to find class code block - declaration[${declaration}]`);\n  }\n\n  const { position } = options;\n  if (position === 'head') {\n    const firstBracketIndex = srcContents.indexOf('{', start);\n    srcContents = insertContentsAtOffset(srcContents, insertion, firstBracketIndex + 1);\n  } else if (position === 'tail') {\n    const endBracketIndex = findMatchingBracketPosition(srcContents, '{', start);\n    srcContents = insertContentsAtOffset(srcContents, insertion, endBracketIndex);\n  }\n  return srcContents;\n}\n\n/**\n * Insert contents to the Swift function block\n *\n * @param srcContents source contents\n * @param selector function selector, e.g. 'doSomething:withSomeValue:'\n * @param insertion code to insert\n * @param options insertion options\n * @returns updated contents\n */\nexport function insertContentsInsideSwiftFunctionBlock(\n  srcContents: string,\n  selector: string,\n  insertion: string,\n  options: InsertContentFunctionOptions\n): string {\n  return insertContentsInsideFunctionBlock(srcContents, selector, insertion, options, 'swift');\n}\n\nfunction insertContentsInsideFunctionBlock(\n  srcContents: string,\n  selector: string,\n  insertion: string,\n  options: InsertContentFunctionOptions,\n  language: 'objc' | 'swift'\n): string {\n  const codeBlock =\n    language === 'objc'\n      ? findObjcFunctionCodeBlock(srcContents, selector)\n      : findSwiftFunctionCodeBlock(srcContents, selector);\n  if (!codeBlock) {\n    return srcContents;\n  }\n\n  const { position } = options;\n  const indent = ' '.repeat(options.indent ?? 2);\n\n  if (position === 'head') {\n    srcContents = insertContentsAtOffset(\n      srcContents,\n      `\\n${indent}${insertion}`,\n      codeBlock.start + 1\n    );\n  } else if (position === 'tail') {\n    srcContents = insertContentsAtOffset(srcContents, `\\n${indent}${insertion}`, codeBlock.end - 1);\n  } else if (position === 'tailBeforeLastReturn') {\n    let lastReturnIndex = srcContents.lastIndexOf(' return ', codeBlock.end);\n    if (lastReturnIndex < 0) {\n      throw new Error(`Cannot find last return statement:\\n${srcContents}`);\n    }\n    lastReturnIndex += 1; // +1 for the prefix space\n    srcContents = insertContentsAtOffset(srcContents, `${insertion}\\n${indent}`, lastReturnIndex);\n  }\n\n  return srcContents;\n}\n"], "mappings": ";;;;;;;;;;;;;;AAAA,SAAAA,eAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,cAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,eAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,cAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAaA;AACA;AACA;AACA;AACA;AACA;AACO,SAASG,cAAcA,CAACC,MAAc,EAAEC,OAAiB,EAAU;EACxE,MAAMC,KAAK,GAAGF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC;EAChC;EACA,MAAMC,wBAAwB,GAAGF,KAAK,CAACG,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACC,KAAK,CAAC,cAAc,CAAC,CAAC;EACtF,KAAK,MAAMC,aAAa,IAAIP,OAAO,EAAE;IACnC,IAAI,CAACD,MAAM,CAACS,QAAQ,CAACD,aAAa,CAAC,EAAE;MACnC,MAAME,eAAe,GAAG,WAAWF,aAAa,EAAE;MAClDN,KAAK,CAACS,MAAM,CAACP,wBAAwB,GAAG,CAAC,EAAE,CAAC,EAAEM,eAAe,CAAC;IAChE;EACF;EACA,OAAOR,KAAK,CAACU,IAAI,CAAC,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACO,SAASC,eAAeA,CAACb,MAAc,EAAEC,OAAiB,EAAU;EACzE,MAAMC,KAAK,GAAGF,MAAM,CAACG,KAAK,CAAC,IAAI,CAAC;EAChC;EACA,MAAMC,wBAAwB,GAAGF,KAAK,CAACG,SAAS,CAAEC,IAAI,IAAKA,IAAI,CAACC,KAAK,CAAC,aAAa,CAAC,CAAC;EACrF,KAAK,MAAMC,aAAa,IAAIP,OAAO,EAAE;IACnC,IAAI,CAACD,MAAM,CAACS,QAAQ,CAACD,aAAa,CAAC,EAAE;MACnC,MAAME,eAAe,GAAG,UAAUF,aAAa,EAAE;MACjDN,KAAK,CAACS,MAAM,CAACP,wBAAwB,GAAG,CAAC,EAAE,CAAC,EAAEM,eAAe,CAAC;IAChE;EACF;EACA,OAAOR,KAAK,CAACU,IAAI,CAAC,IAAI,CAAC;AACzB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,0BAA0BA,CACxCC,QAAgB,EAChBC,WAAmB,EACD;EAClB,MAAMC,KAAK,GAAGF,QAAQ,CAACG,MAAM,CAAC,IAAIC,MAAM,CAAC,IAAIH,WAAW,KAAK,EAAE,GAAG,CAAC,CAAC;EACpE,IAAIC,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,IAAI;EACb;EAEA,IAAIG,GAAG,GAAGL,QAAQ,CAACM,OAAO,CAAC,QAAQ,EAAEJ,KAAK,CAAC;EAC3CG,GAAG,IAAI,CAAC,CAAC,CAAC;;EAEV,OAAO;IACLH,KAAK;IACLG,GAAG;IACHE,IAAI,EAAEP,QAAQ,CAACQ,SAAS,CAACN,KAAK,EAAEG,GAAG;EACrC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASI,yBAAyBA,CAACT,QAAgB,EAAEU,QAAgB,EAAoB;EAC9F,MAAMC,OAAO,GAAGD,QAAQ,CAACtB,KAAK,CAAC,GAAG,CAAC;EACnC,MAAMwB,SAAS,GAAGD,OAAO,CAACE,MAAM,GAAG,CAAC;EACpC,IAAIC,OAAO,GAAG,sBAAsB;EACpC,IAAIF,SAAS,KAAK,CAAC,EAAE;IACnBE,OAAO,IAAI,GAAGH,OAAO,CAAC,CAAC,CAAC,MAAM;EAChC,CAAC,MAAM;IACL,KAAK,IAAII,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGH,SAAS,EAAE,EAAEG,CAAC,EAAE;MAClC,MAAMC,SAAS,GAAG,GAAGL,OAAO,CAACI,CAAC,CAAC,eAAe;MAC9CD,OAAO,IAAI,GAAGE,SAAS,MAAM;IAC/B;EACF;EACAF,OAAO,IAAI,GAAG;EACd,IAAIZ,KAAK,GAAGF,QAAQ,CAACG,MAAM,CAAC,IAAIC,MAAM,CAACU,OAAO,EAAE,GAAG,CAAC,CAAC;EACrD,IAAIZ,KAAK,GAAG,CAAC,EAAE;IACb,OAAO,IAAI;EACb;EACAA,KAAK,GAAGF,QAAQ,CAACM,OAAO,CAAC,GAAG,EAAEJ,KAAK,CAAC;EAEpC,MAAMG,GAAG,GAAG,IAAAY,4CAA2B,EAACjB,QAAQ,EAAE,GAAG,EAAEE,KAAK,CAAC;EAC7D,OAAO;IACLA,KAAK;IACLG,GAAG;IACHE,IAAI,EAAEP,QAAQ,CAACQ,SAAS,CAACN,KAAK,EAAEG,GAAG,GAAG,CAAC;EACzC,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASa,qCAAqCA,CACnDC,WAAmB,EACnBT,QAAgB,EAChBU,SAAiB,EACjBC,OAAqC,EAC7B;EACR,OAAOC,iCAAiC,CAACH,WAAW,EAAET,QAAQ,EAAEU,SAAS,EAAEC,OAAO,EAAE,MAAM,CAAC;AAC7F;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,sCAAsCA,CACpDJ,WAAmB,EACnBlB,WAAmB,EACnBmB,SAAiB,EACjBC,OAEC,EACO;EACR,MAAMG,SAAS,GAAGzB,0BAA0B,CAACoB,WAAW,EAAElB,WAAW,CAAC;EACtE,IAAI,CAACuB,SAAS,EAAE;IACd,OAAOL,WAAW;EACpB;EAEA,MAAM;IAAEM;EAAS,CAAC,GAAGJ,OAAO;EAC5B,IAAII,QAAQ,KAAK,MAAM,EAAE;IACvB,MAAMC,iBAAiB,GAAGP,WAAW,CAACb,OAAO,CAAC,IAAI,EAAEkB,SAAS,CAACtB,KAAK,CAAC;IACpEiB,WAAW,GAAG,IAAAQ,uCAAsB,EAACR,WAAW,EAAEC,SAAS,EAAEM,iBAAiB,CAAC;EACjF,CAAC,MAAM,IAAID,QAAQ,KAAK,MAAM,EAAE;IAC9B,MAAMG,MAAM,GAAG,MAAM,CAACf,MAAM;IAC5BM,WAAW,GAAG,IAAAQ,uCAAsB,EAACR,WAAW,EAAEC,SAAS,EAAEI,SAAS,CAACnB,GAAG,GAAGuB,MAAM,CAAC;EACtF;EACA,OAAOT,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASU,0BAA0BA,CAAC7B,QAAgB,EAAEU,QAAgB,EAAoB;EAC/F,MAAMoB,gBAAgB,GAAGpB,QAAQ,CAACJ,OAAO,CAAC,GAAG,CAAC;EAC9C;EACA,MAAMyB,QAAQ,GAAGrB,QAAQ,CAACF,SAAS,CAAC,CAAC,EAAEsB,gBAAgB,CAAC;EACxD;EACA,MAAME,SAAS,GAAGtB,QAAQ,CAACF,SAAS,CAACsB,gBAAgB,GAAG,CAAC,EAAEpB,QAAQ,CAACG,MAAM,GAAG,CAAC,CAAC,CAACzB,KAAK,CAAC,GAAG,CAAC;EAE1F,IAAI6C,YAAY,GAAG,CAAC;EACpB,MAAMC,mBAAmB,GAAG,IAAI9B,MAAM,CAAC,cAAc2B,QAAQ,KAAK,EAAE,GAAG,CAAC;EACxE,IAAII,mBAAmB,GAAG,IAAAC,iCAAgB,EAACpC,QAAQ,EAAEkC,mBAAmB,EAAED,YAAY,CAAC;EACvF,OAAOE,mBAAmB,IAAI,CAAC,EAAE;IAC/B;IACA,MAAME,iBAAiB,GAAGrC,QAAQ,CAACM,OAAO,CAAC,GAAG,EAAE6B,mBAAmB,CAAC;IACpE,MAAMG,eAAe,GAAG,IAAArB,4CAA2B,EAACjB,QAAQ,EAAE,GAAG,EAAEqC,iBAAiB,CAAC;IACrF,MAAME,YAAY,GAAGvC,QAAQ,CAACQ,SAAS,CAAC6B,iBAAiB,GAAG,CAAC,EAAEC,eAAe,CAAC;IAC/E,MAAME,MAAM,GAAGD,YAAY,CAACnD,KAAK,CAAC,GAAG,CAAC,CAACqD,GAAG,CAACC,uBAAuB,CAAC;;IAEnE;IACAT,YAAY,GAAGK,eAAe,GAAG,CAAC;IAClCH,mBAAmB,GAAG,IAAAC,iCAAgB,EAACpC,QAAQ,EAAEkC,mBAAmB,EAAED,YAAY,CAAC;;IAEnF;IACA,IAAID,SAAS,CAACnB,MAAM,KAAK2B,MAAM,CAAC3B,MAAM,EAAE;MACtC;IACF;IACA,KAAK,IAAIE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGiB,SAAS,CAACnB,MAAM,EAAE,EAAEE,CAAC,EAAE;MACzC,IAAIiB,SAAS,CAACjB,CAAC,CAAC,KAAKyB,MAAM,CAACzB,CAAC,CAAC,CAAC4B,aAAa,EAAE;QAC5C;MACF;IACF;;IAEA;IACA,MAAMC,cAAc,GAAG5C,QAAQ,CAACM,OAAO,CAAC,GAAG,EAAEgC,eAAe,CAAC;IAC7D,MAAMO,YAAY,GAAG,IAAA5B,4CAA2B,EAACjB,QAAQ,EAAE,GAAG,EAAEsC,eAAe,CAAC;IAChF,MAAMd,SAAS,GAAGxB,QAAQ,CAACQ,SAAS,CAACoC,cAAc,EAAEC,YAAY,GAAG,CAAC,CAAC;IACtE,OAAO;MACL3C,KAAK,EAAE0C,cAAc;MACrBvC,GAAG,EAAEwC,YAAY;MACjBtC,IAAI,EAAEiB;IACR,CAAC;EACH;EAEA,OAAO,IAAI;AACb;AAEA,SAASkB,uBAAuBA,CAACI,UAAkB,EAAsB;EACvE,MAAMC,SAAS,GAAGD,UAAU,CAACxC,OAAO,CAAC,GAAG,CAAC;EACzC,MAAM,CAACqC,aAAa,EAAEK,aAAa,CAAC,GAAGF,UAAU,CAACtC,SAAS,CAAC,CAAC,EAAEuC,SAAS,CAAC,CAAC3D,KAAK,CAAC,KAAK,CAAC;EACtF,MAAM6D,UAAU,GAAGH,UAAU,CAACtC,SAAS,CAACuC,SAAS,GAAG,CAAC,CAAC,CAACG,IAAI,CAAC,CAAC;EAC7D,OAAO;IACLP,aAAa;IACbK,aAAa;IACbC;EACF,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASE,mCAAmCA,CACjDhC,WAAmB,EACnBlB,WAAmB,EACnBmB,SAAiB,EACjBC,OAEC,EACO;EACR,MAAMnB,KAAK,GAAGiB,WAAW,CAAChB,MAAM,CAAC,IAAIC,MAAM,CAAC,OAAOH,WAAW,aAAa,CAAC,CAAC;EAC7E,IAAIC,KAAK,GAAG,CAAC,EAAE;IACb,MAAM,IAAIkD,KAAK,CAAC,iDAAiDnD,WAAW,GAAG,CAAC;EAClF;EAEA,MAAM;IAAEwB;EAAS,CAAC,GAAGJ,OAAO;EAC5B,IAAII,QAAQ,KAAK,MAAM,EAAE;IACvB,MAAM4B,iBAAiB,GAAGlC,WAAW,CAACb,OAAO,CAAC,GAAG,EAAEJ,KAAK,CAAC;IACzDiB,WAAW,GAAG,IAAAQ,uCAAsB,EAACR,WAAW,EAAEC,SAAS,EAAEiC,iBAAiB,GAAG,CAAC,CAAC;EACrF,CAAC,MAAM,IAAI5B,QAAQ,KAAK,MAAM,EAAE;IAC9B,MAAM6B,eAAe,GAAG,IAAArC,4CAA2B,EAACE,WAAW,EAAE,GAAG,EAAEjB,KAAK,CAAC;IAC5EiB,WAAW,GAAG,IAAAQ,uCAAsB,EAACR,WAAW,EAAEC,SAAS,EAAEkC,eAAe,CAAC;EAC/E;EACA,OAAOnC,WAAW;AACpB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASoC,sCAAsCA,CACpDpC,WAAmB,EACnBT,QAAgB,EAChBU,SAAiB,EACjBC,OAAqC,EAC7B;EACR,OAAOC,iCAAiC,CAACH,WAAW,EAAET,QAAQ,EAAEU,SAAS,EAAEC,OAAO,EAAE,OAAO,CAAC;AAC9F;AAEA,SAASC,iCAAiCA,CACxCH,WAAmB,EACnBT,QAAgB,EAChBU,SAAiB,EACjBC,OAAqC,EACrCmC,QAA0B,EAClB;EACR,MAAMhC,SAAS,GACbgC,QAAQ,KAAK,MAAM,GACf/C,yBAAyB,CAACU,WAAW,EAAET,QAAQ,CAAC,GAChDmB,0BAA0B,CAACV,WAAW,EAAET,QAAQ,CAAC;EACvD,IAAI,CAACc,SAAS,EAAE;IACd,OAAOL,WAAW;EACpB;EAEA,MAAM;IAAEM;EAAS,CAAC,GAAGJ,OAAO;EAC5B,MAAMoC,MAAM,GAAG,GAAG,CAACC,MAAM,CAACrC,OAAO,CAACoC,MAAM,IAAI,CAAC,CAAC;EAE9C,IAAIhC,QAAQ,KAAK,MAAM,EAAE;IACvBN,WAAW,GAAG,IAAAQ,uCAAsB,EAClCR,WAAW,EACX,KAAKsC,MAAM,GAAGrC,SAAS,EAAE,EACzBI,SAAS,CAACtB,KAAK,GAAG,CACpB,CAAC;EACH,CAAC,MAAM,IAAIuB,QAAQ,KAAK,MAAM,EAAE;IAC9BN,WAAW,GAAG,IAAAQ,uCAAsB,EAACR,WAAW,EAAE,KAAKsC,MAAM,GAAGrC,SAAS,EAAE,EAAEI,SAAS,CAACnB,GAAG,GAAG,CAAC,CAAC;EACjG,CAAC,MAAM,IAAIoB,QAAQ,KAAK,sBAAsB,EAAE;IAC9C,IAAIkC,eAAe,GAAGxC,WAAW,CAACyC,WAAW,CAAC,UAAU,EAAEpC,SAAS,CAACnB,GAAG,CAAC;IACxE,IAAIsD,eAAe,GAAG,CAAC,EAAE;MACvB,MAAM,IAAIP,KAAK,CAAC,uCAAuCjC,WAAW,EAAE,CAAC;IACvE;IACAwC,eAAe,IAAI,CAAC,CAAC,CAAC;IACtBxC,WAAW,GAAG,IAAAQ,uCAAsB,EAACR,WAAW,EAAE,GAAGC,SAAS,KAAKqC,MAAM,EAAE,EAAEE,eAAe,CAAC;EAC/F;EAEA,OAAOxC,WAAW;AACpB", "ignoreList": []}