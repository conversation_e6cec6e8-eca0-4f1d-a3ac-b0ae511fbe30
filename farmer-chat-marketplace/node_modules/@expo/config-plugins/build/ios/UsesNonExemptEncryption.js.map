{"version": 3, "file": "UsesNonExemptEncryption.js", "names": ["_iosPlugins", "data", "require", "withUsesNonExemptEncryption", "exports", "createInfoPlistPluginWithPropertyGuard", "setUsesNonExemptEncryption", "infoPlistProperty", "expoConfigProperty", "getUsesNonExemptEncryption", "config", "ios", "usesNonExemptEncryption", "ITSAppUsesNonExemptEncryption", "infoPlist"], "sources": ["../../src/ios/UsesNonExemptEncryption.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\n\nexport const withUsesNonExemptEncryption = createInfoPlistPluginWithPropertyGuard(\n  setUsesNonExemptEncryption,\n  {\n    infoPlistProperty: 'ITSAppUsesNonExemptEncryption',\n    expoConfigProperty: 'ios.config.usesNonExemptEncryption',\n  },\n  'withUsesNonExemptEncryption'\n);\n\nexport function getUsesNonExemptEncryption(config: Pick<ExpoConfig, 'ios'>) {\n  return config?.ios?.config?.usesNonExemptEncryption ?? null;\n}\n\nexport function setUsesNonExemptEncryption(\n  config: Pick<ExpoConfig, 'ios'>,\n  { ITSAppUsesNonExemptEncryption, ...infoPlist }: InfoPlist\n): InfoPlist {\n  const usesNonExemptEncryption = getUsesNonExemptEncryption(config);\n\n  // Make no changes if the key is left blank\n  if (usesNonExemptEncryption === null) {\n    return infoPlist;\n  }\n\n  return {\n    ...infoPlist,\n    ITSAppUsesNonExemptEncryption: usesNonExemptEncryption,\n  };\n}\n"], "mappings": ";;;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAME,2BAA2B,GAAAC,OAAA,CAAAD,2BAAA,GAAG,IAAAE,oDAAsC,EAC/EC,0BAA0B,EAC1B;EACEC,iBAAiB,EAAE,+BAA+B;EAClDC,kBAAkB,EAAE;AACtB,CAAC,EACD,6BACF,CAAC;AAEM,SAASC,0BAA0BA,CAACC,MAA+B,EAAE;EAC1E,OAAOA,MAAM,EAAEC,GAAG,EAAED,MAAM,EAAEE,uBAAuB,IAAI,IAAI;AAC7D;AAEO,SAASN,0BAA0BA,CACxCI,MAA+B,EAC/B;EAAEG,6BAA6B;EAAE,GAAGC;AAAqB,CAAC,EAC/C;EACX,MAAMF,uBAAuB,GAAGH,0BAA0B,CAACC,MAAM,CAAC;;EAElE;EACA,IAAIE,uBAAuB,KAAK,IAAI,EAAE;IACpC,OAAOE,SAAS;EAClB;EAEA,OAAO;IACL,GAAGA,SAAS;IACZD,6BAA6B,EAAED;EACjC,CAAC;AACH", "ignoreList": []}