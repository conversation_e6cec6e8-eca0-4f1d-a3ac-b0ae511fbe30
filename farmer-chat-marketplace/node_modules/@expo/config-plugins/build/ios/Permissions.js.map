{"version": 3, "file": "Permissions.js", "names": ["_debug", "data", "_interopRequireDefault", "require", "_iosPlugins", "e", "__esModule", "default", "debug", "Debug", "applyPermissions", "defaults", "permissions", "infoPlist", "entries", "Object", "length", "JSON", "stringify", "permission", "description", "createPermissionsPlugin", "name", "withIosPermissions", "config", "withInfoPlist", "modResults", "defineProperty", "value"], "sources": ["../../src/ios/Permissions.ts"], "sourcesContent": ["import Debug from 'debug';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { ConfigPlugin } from '../Plugin.types';\nimport { withInfoPlist } from '../plugins/ios-plugins';\n\nconst debug = Debug('expo:config-plugins:ios:permissions');\n\nexport function applyPermissions<Defaults extends Record<string, string> = Record<string, string>>(\n  defaults: Defaults,\n  permissions: Partial<Record<keyof Defaults, string | false>>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  const entries = Object.entries(defaults);\n  if (entries.length === 0) {\n    debug(`No defaults provided: ${JSON.stringify(permissions)}`);\n  }\n  for (const [permission, description] of entries) {\n    if (permissions[permission] === false) {\n      debug(`Deleting \"${permission}\"`);\n      delete infoPlist[permission];\n    } else {\n      infoPlist[permission] = permissions[permission] || infoPlist[permission] || description;\n      debug(`Setting \"${permission}\" to \"${infoPlist[permission]}\"`);\n    }\n  }\n  return infoPlist;\n}\n\n/**\n * Helper method for creating mods to apply default permissions.\n *\n * @param action\n */\nexport function createPermissionsPlugin<\n  Defaults extends Record<string, string> = Record<string, string>,\n>(defaults: Defaults, name?: string) {\n  const withIosPermissions: ConfigPlugin<Record<keyof Defaults, string | undefined | false>> = (\n    config,\n    permissions\n  ) =>\n    withInfoPlist(config, async (config) => {\n      config.modResults = applyPermissions(defaults, permissions, config.modResults);\n      return config;\n    });\n  if (name) {\n    Object.defineProperty(withIosPermissions, 'name', {\n      value: name,\n    });\n  }\n  return withIosPermissions;\n}\n"], "mappings": ";;;;;;;AAAA,SAAAA,OAAA;EAAA,MAAAC,IAAA,GAAAC,sBAAA,CAAAC,OAAA;EAAAH,MAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAIA,SAAAG,YAAA;EAAA,MAAAH,IAAA,GAAAE,OAAA;EAAAC,WAAA,YAAAA,CAAA;IAAA,OAAAH,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAAuD,SAAAC,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAEvD,MAAMG,KAAK,GAAG,IAAAC,gBAAK,EAAC,qCAAqC,CAAC;AAEnD,SAASC,gBAAgBA,CAC9BC,QAAkB,EAClBC,WAA4D,EAC5DC,SAAoB,EACT;EACX,MAAMC,OAAO,GAAGC,MAAM,CAACD,OAAO,CAACH,QAAQ,CAAC;EACxC,IAAIG,OAAO,CAACE,MAAM,KAAK,CAAC,EAAE;IACxBR,KAAK,CAAC,yBAAyBS,IAAI,CAACC,SAAS,CAACN,WAAW,CAAC,EAAE,CAAC;EAC/D;EACA,KAAK,MAAM,CAACO,UAAU,EAAEC,WAAW,CAAC,IAAIN,OAAO,EAAE;IAC/C,IAAIF,WAAW,CAACO,UAAU,CAAC,KAAK,KAAK,EAAE;MACrCX,KAAK,CAAC,aAAaW,UAAU,GAAG,CAAC;MACjC,OAAON,SAAS,CAACM,UAAU,CAAC;IAC9B,CAAC,MAAM;MACLN,SAAS,CAACM,UAAU,CAAC,GAAGP,WAAW,CAACO,UAAU,CAAC,IAAIN,SAAS,CAACM,UAAU,CAAC,IAAIC,WAAW;MACvFZ,KAAK,CAAC,YAAYW,UAAU,SAASN,SAAS,CAACM,UAAU,CAAC,GAAG,CAAC;IAChE;EACF;EACA,OAAON,SAAS;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACO,SAASQ,uBAAuBA,CAErCV,QAAkB,EAAEW,IAAa,EAAE;EACnC,MAAMC,kBAAoF,GAAGA,CAC3FC,MAAM,EACNZ,WAAW,KAEX,IAAAa,2BAAa,EAACD,MAAM,EAAE,MAAOA,MAAM,IAAK;IACtCA,MAAM,CAACE,UAAU,GAAGhB,gBAAgB,CAACC,QAAQ,EAAEC,WAAW,EAAEY,MAAM,CAACE,UAAU,CAAC;IAC9E,OAAOF,MAAM;EACf,CAAC,CAAC;EACJ,IAAIF,IAAI,EAAE;IACRP,MAAM,CAACY,cAAc,CAACJ,kBAAkB,EAAE,MAAM,EAAE;MAChDK,KAAK,EAAEN;IACT,CAAC,CAAC;EACJ;EACA,OAAOC,kBAAkB;AAC3B", "ignoreList": []}