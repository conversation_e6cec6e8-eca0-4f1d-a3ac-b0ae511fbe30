{"version": 3, "file": "Version.js", "names": ["_iosPlugins", "data", "require", "withVersion", "exports", "createInfoPlistPluginWithPropertyGuard", "setVersion", "infoPlistProperty", "expoConfigProperty", "expoPropertyGetter", "getVersion", "withBuildNumber", "setBuildNumber", "config", "ios", "version", "infoPlist", "CFBundleShortVersionString", "getBuildNumber", "buildNumber", "CFBundleVersion"], "sources": ["../../src/ios/Version.ts"], "sourcesContent": ["import { ExpoConfig } from '@expo/config-types';\n\nimport { InfoPlist } from './IosConfig.types';\nimport { createInfoPlistPluginWithPropertyGuard } from '../plugins/ios-plugins';\n\nexport const withVersion = createInfoPlistPluginWithPropertyGuard(\n  setVersion,\n  {\n    infoPlistProperty: 'CFBundleShortVersionString',\n    expoConfigProperty: 'version | ios.version',\n    expoPropertyGetter: getVersion,\n  },\n  'withVersion'\n);\n\nexport const withBuildNumber = createInfoPlistPluginWithPropertyGuard(\n  setBuildNumber,\n  {\n    infoPlistProperty: 'CFBundleVersion',\n    expoConfigProperty: 'ios.buildNumber',\n  },\n  'withBuildNumber'\n);\n\nexport function getVersion(config: Pick<ExpoConfig, 'version' | 'ios'>) {\n  return config.ios?.version || config.version || '1.0.0';\n}\n\nexport function setVersion(\n  config: Pick<ExpoConfig, 'version' | 'ios'>,\n  infoPlist: InfoPlist\n): InfoPlist {\n  return {\n    ...infoPlist,\n    CFBundleShortVersionString: getVersion(config),\n  };\n}\n\nexport function getBuildNumber(config: Pick<ExpoConfig, 'ios'>) {\n  return config.ios?.buildNumber ? config.ios.buildNumber : '1';\n}\n\nexport function setBuildNumber(config: Pick<ExpoConfig, 'ios'>, infoPlist: InfoPlist): InfoPlist {\n  return {\n    ...infoPlist,\n    CFBundleVersion: getBuildNumber(config),\n  };\n}\n"], "mappings": ";;;;;;;;;;AAGA,SAAAA,YAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,WAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAEO,MAAME,WAAW,GAAAC,OAAA,CAAAD,WAAA,GAAG,IAAAE,oDAAsC,EAC/DC,UAAU,EACV;EACEC,iBAAiB,EAAE,4BAA4B;EAC/CC,kBAAkB,EAAE,uBAAuB;EAC3CC,kBAAkB,EAAEC;AACtB,CAAC,EACD,aACF,CAAC;AAEM,MAAMC,eAAe,GAAAP,OAAA,CAAAO,eAAA,GAAG,IAAAN,oDAAsC,EACnEO,cAAc,EACd;EACEL,iBAAiB,EAAE,iBAAiB;EACpCC,kBAAkB,EAAE;AACtB,CAAC,EACD,iBACF,CAAC;AAEM,SAASE,UAAUA,CAACG,MAA2C,EAAE;EACtE,OAAOA,MAAM,CAACC,GAAG,EAAEC,OAAO,IAAIF,MAAM,CAACE,OAAO,IAAI,OAAO;AACzD;AAEO,SAAST,UAAUA,CACxBO,MAA2C,EAC3CG,SAAoB,EACT;EACX,OAAO;IACL,GAAGA,SAAS;IACZC,0BAA0B,EAAEP,UAAU,CAACG,MAAM;EAC/C,CAAC;AACH;AAEO,SAASK,cAAcA,CAACL,MAA+B,EAAE;EAC9D,OAAOA,MAAM,CAACC,GAAG,EAAEK,WAAW,GAAGN,MAAM,CAACC,GAAG,CAACK,WAAW,GAAG,GAAG;AAC/D;AAEO,SAASP,cAAcA,CAACC,MAA+B,EAAEG,SAAoB,EAAa;EAC/F,OAAO;IACL,GAAGA,SAAS;IACZI,eAAe,EAAEF,cAAc,CAACL,MAAM;EACxC,CAAC;AACH", "ignoreList": []}