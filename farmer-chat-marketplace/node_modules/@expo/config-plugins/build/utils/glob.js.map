{"version": 3, "file": "glob.js", "names": ["withSortedGlobResult", "glob", "sort", "a", "b", "localeCompare"], "sources": ["../../src/utils/glob.ts"], "sourcesContent": ["/**\n * Sort the glob result alphabetically, to ensure results are identical across different devices (Linux/MacOS).\n * Since `glob@9` the results are determined by the OS and not guaranteed to be sorted.\n *\n * @see https://github.com/isaacs/node-glob/issues/576#issuecomment-1972765500\n */\nexport function withSortedGlobResult(glob: string[]) {\n  return glob.sort((a, b) => a.localeCompare(b));\n}\n"], "mappings": ";;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACO,SAASA,oBAAoBA,CAACC,IAAc,EAAE;EACnD,OAAOA,IAAI,CAACC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,CAACE,aAAa,CAACD,CAAC,CAAC,CAAC;AAChD", "ignoreList": []}