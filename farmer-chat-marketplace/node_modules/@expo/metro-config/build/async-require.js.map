{"version": 3, "file": "async-require.js", "sourceRoot": "", "sources": ["../src/async-require.ts"], "names": [], "mappings": ";AAAA;;;;;;;;;GASG;;AAWH,SAAS,eAAe,CAAC,QAAgB,EAAE,KAAyB;IAClE,MAAM,UAAU,GAA4C,MAAc,CACxE,GAAG,uBAAuB,mBAAmB,CAC9C,CAAC;IAEF,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;QACvB,MAAM,cAAc,GAAG,MAAM,CAAC,QAAQ,CAAC,CAAC;QACxC,IAAI,KAAK,IAAI,IAAI,EAAE,CAAC;YAClB,MAAM,UAAU,GAAG,KAAK,CAAC,cAAc,CAAC,CAAC;YACzC,IAAI,UAAU,IAAI,IAAI,EAAE,CAAC;gBACvB,0DAA0D;gBAC1D,OAAO,UAAU,CAAC,UAAU,CAAC,CAAC;YAChC,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,SAAS,CAAC;AACnB,CAAC;AAED,SAAS,gBAAgB,CAAI,QAAgB,EAAE,KAAyB;IACtE,MAAM,sBAAsB,GAAG,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;IAChE,MAAM,SAAS,GAAG,GAAG,EAAE,CAAE,OAAmC,CAAC,SAAS,CAAI,QAAQ,CAAC,CAAC;IAEpF,IAAI,sBAAsB,IAAI,IAAI,EAAE,CAAC;QACnC,OAAO,sBAAsB,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,SAAS,EAAE,CAAC;AACrB,CAAC;AAED,KAAK,UAAU,YAAY,CACzB,QAAgB,EAChB,KAAyB,EACzB,UAAmB,CAAC,SAAS;;IAE7B,OAAO,gBAAgB,CAAI,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC9C,CAAC;AAED,wEAAwE;AACxE,0BAA0B;AAC1B,YAAY,CAAC,wBAAwB,GAAG,SAAS,wBAAwB,CACvE,QAAgB,EAChB,KAAyB;IAEzB,OAAO,gBAAgB,CAAC,QAAQ,EAAE,KAAK,CAAC,CAAC;AAC3C,CAAC,CAAC;AAEF,YAAY,CAAC,QAAQ,GAAG,UACtB,QAAgB,EAChB,KAAyB,EACzB,UAAmB,CAAC,SAAS;;IAE7B,eAAe,CAAC,QAAQ,EAAE,KAAK,CAAC,EAAE,IAAI,CACpC,GAAG,EAAE,GAAE,CAAC,EACR,GAAG,EAAE,GAAE,CAAC,CACT,CAAC;AACJ,CAAC,CAAC;AAEF,YAAY,CAAC,gBAAgB,GAAG,SAAS,gBAAgB,CACvD,QAAgB,EAChB,KAAyB;IAEzB,IAAI,CAAC,KAAK,EAAE,CAAC;QACX,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAC;IACzE,CAAC;IACD,MAAM,EAAE,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;IAC3B,IAAI,CAAC,EAAE,EAAE,CAAC;QACR,MAAM,IAAI,KAAK,CAAC,oDAAoD,GAAG,EAAE,CAAC,CAAC;IAC7E,CAAC;IACD,OAAO,EAAE,CAAC;AACZ,CAAC,CAAC;AAEF,MAAM,CAAC,OAAO,GAAG,YAAY,CAAC"}