/**
 * Copyright (c) 650 Industries.
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import React from 'react';
type Props = {
    heading: string;
    children: React.ReactNode;
    action?: any;
};
export declare function LogBoxInspectorSection(props: Props): React.JSX.Element;
export {};
//# sourceMappingURL=LogBoxInspectorSection.d.ts.map