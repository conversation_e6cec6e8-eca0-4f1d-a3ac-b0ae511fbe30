/**
 * Copyright (c) 650 Industries.
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
export declare function fetchAsync(url: string): Promise<{
    body: string;
    status: number;
    headers: Record<string, string>;
}>;
//# sourceMappingURL=fetchAsync.native.d.ts.map