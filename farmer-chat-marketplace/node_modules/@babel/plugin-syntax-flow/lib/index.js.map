{"version": 3, "names": ["_helper<PERSON>lugin<PERSON><PERSON>s", "require", "_default", "exports", "default", "declare", "api", "options", "assertVersion", "all", "enums", "undefined", "Error", "console", "warn", "name", "manipulateOptions", "opts", "parserOpts", "plugins", "some", "p", "Array", "isArray", "push"], "sources": ["../src/index.ts"], "sourcesContent": ["import { declare } from \"@babel/helper-plugin-utils\";\n\nexport interface Options {\n  all?: boolean;\n}\n\nexport default declare((api, options: Options) => {\n  api.assertVersion(REQUIRED_VERSION(7));\n\n  // When enabled and plugins includes flow, all files should be parsed as if\n  // the @flow pragma was provided.\n  // @ts-expect-error Babel 7\n  const { all, enums } = options;\n\n  if (typeof all !== \"boolean\" && all !== undefined) {\n    throw new Error(\".all must be a boolean, or undefined\");\n  }\n\n  if (process.env.BABEL_8_BREAKING) {\n    if (enums !== undefined) {\n      throw new Error(\n        \"The .enums option has been removed and it's now always enabled. Please remove it from your config.\",\n      );\n    }\n  } else {\n    if (enums === false) {\n      console.warn(\n        \"The .enums option has been removed and it's now always enabled.\",\n      );\n    }\n  }\n\n  return {\n    name: \"syntax-flow\",\n\n    manipulateOptions(opts, parserOpts) {\n      if (!process.env.BABEL_8_BREAKING) {\n        // If the file has already enabled TS, assume that this is not a\n        // valid Flowtype file.\n        if (\n          parserOpts.plugins.some(\n            p => (Array.isArray(p) ? p[0] : p) === \"typescript\",\n          )\n        ) {\n          return;\n        }\n      }\n\n      if (process.env.BABEL_8_BREAKING) {\n        parserOpts.plugins.push([\"flow\", { all }]);\n      } else {\n        // @ts-expect-error Babel 7\n        parserOpts.plugins.push([\"flow\", { all, enums }]);\n      }\n    },\n  };\n});\n"], "mappings": ";;;;;;AAAA,IAAAA,kBAAA,GAAAC,OAAA;AAAqD,IAAAC,QAAA,GAAAC,OAAA,CAAAC,OAAA,GAMtC,IAAAC,0BAAO,EAAC,CAACC,GAAG,EAAEC,OAAgB,KAAK;EAChDD,GAAG,CAACE,aAAa,CAAkB,CAAE,CAAC;EAKtC,MAAM;IAAEC,GAAG;IAAEC;EAAM,CAAC,GAAGH,OAAO;EAE9B,IAAI,OAAOE,GAAG,KAAK,SAAS,IAAIA,GAAG,KAAKE,SAAS,EAAE;IACjD,MAAM,IAAIC,KAAK,CAAC,sCAAsC,CAAC;EACzD;EAQO;IACL,IAAIF,KAAK,KAAK,KAAK,EAAE;MACnBG,OAAO,CAACC,IAAI,CACV,iEACF,CAAC;IACH;EACF;EAEA,OAAO;IACLC,IAAI,EAAE,aAAa;IAEnBC,iBAAiBA,CAACC,IAAI,EAAEC,UAAU,EAAE;MACC;QAGjC,IACEA,UAAU,CAACC,OAAO,CAACC,IAAI,CACrBC,CAAC,IAAI,CAACC,KAAK,CAACC,OAAO,CAACF,CAAC,CAAC,GAAGA,CAAC,CAAC,CAAC,CAAC,GAAGA,CAAC,MAAM,YACzC,CAAC,EACD;UACA;QACF;MACF;MAIO;QAELH,UAAU,CAACC,OAAO,CAACK,IAAI,CAAC,CAAC,MAAM,EAAE;UAAEf,GAAG;UAAEC;QAAM,CAAC,CAAC,CAAC;MACnD;IACF;EACF,CAAC;AACH,CAAC,CAAC", "ignoreList": []}