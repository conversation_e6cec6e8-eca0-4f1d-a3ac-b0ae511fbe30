"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = _slicedToArray;
var _arrayWithHoles = require("./arrayWithHoles.js");
var _iterableToArrayLimit = require("./iterableToArrayLimit.js");
var _unsupportedIterableToArray = require("./unsupportedIterableToArray.js");
var _nonIterableRest = require("./nonIterableRest.js");
function _slicedToArray(arr, i) {
  return (0, _arrayWithHoles.default)(arr) || (0, _iterableToArrayLimit.default)(arr, i) || (0, _unsupportedIterableToArray.default)(arr, i) || (0, _nonIterableRest.default)();
}

//# sourceMappingURL=slicedToArray.js.map
