import"../shell/shell.js";import*as e from"../../core/i18n/i18n.js";import*as t from"../../ui/legacy/legacy.js";import*as n from"../../core/common/common.js";import*as i from"../../core/host/host.js";import*as o from"../../core/sdk/sdk.js";import*as a from"../../ui/legacy/components/utils/utils.js";import*as r from"../main/main.js";const l={performance:"Performance",showPerformance:"Show Performance",showRecentTimelineSessions:"Show recent timeline sessions",record:"Record",stop:"Stop",recordAndReload:"Record and reload"},s=e.i18n.registerUIStrings("panels/js_timeline/js_timeline-meta.ts",l),c=e.i18n.getLazilyComputedLocalizedString.bind(void 0,s);let g;async function m(){return g||(g=await import("../../panels/timeline/timeline.js")),g}function d(e){return void 0===g?[]:e(g)}t.ViewManager.registerViewExtension({location:"panel",id:"timeline",title:c(l.performance),commandPrompt:c(l.showPerformance),order:66,hasToolbar:!1,isPreviewFeature:!0,loadView:async()=>(await m()).TimelinePanel.TimelinePanel.instance({forceNew:null,isNode:!0})}),t.ActionRegistration.registerActionExtension({actionId:"timeline.show-history",loadActionDelegate:async()=>new((await m()).TimelinePanel.ActionDelegate),category:"PERFORMANCE",title:c(l.showRecentTimelineSessions),contextTypes:()=>d((e=>[e.TimelinePanel.TimelinePanel])),bindings:[{platform:"windows,linux",shortcut:"Ctrl+H"},{platform:"mac",shortcut:"Meta+Y"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.toggle-recording",category:"PERFORMANCE",iconClass:"record-start",toggleable:!0,toggledIconClass:"record-stop",toggleWithRedColor:!0,contextTypes:()=>d((e=>[e.TimelinePanel.TimelinePanel])),loadActionDelegate:async()=>new((await m()).TimelinePanel.ActionDelegate),options:[{value:!0,title:c(l.record)},{value:!1,title:c(l.stop)}],bindings:[{platform:"windows,linux",shortcut:"Ctrl+E"},{platform:"mac",shortcut:"Meta+E"}]}),t.ActionRegistration.registerActionExtension({actionId:"timeline.record-reload",iconClass:"refresh",contextTypes:()=>d((e=>[e.TimelinePanel.TimelinePanel])),category:"PERFORMANCE",title:c(l.recordAndReload),loadActionDelegate:async()=>new((await m()).TimelinePanel.ActionDelegate),bindings:[{platform:"windows,linux",shortcut:"Ctrl+Shift+E"},{platform:"mac",shortcut:"Meta+Shift+E"}]});const w={throttling:"Throttling",showThrottling:"Show Throttling",goOffline:"Go offline",device:"device",throttlingTag:"throttling",enableSlowGThrottling:"Enable slow `3G` throttling",enableFastGThrottling:"Enable fast `3G` throttling",goOnline:"Go online"},h=e.i18n.registerUIStrings("panels/mobile_throttling/mobile_throttling-meta.ts",w),p=e.i18n.getLazilyComputedLocalizedString.bind(void 0,h);let T;async function u(){return T||(T=await import("../../panels/mobile_throttling/mobile_throttling.js")),T}t.ViewManager.registerViewExtension({location:"settings-view",id:"throttling-conditions",title:p(w.throttling),commandPrompt:p(w.showThrottling),order:35,loadView:async()=>new((await u()).ThrottlingSettingsTab.ThrottlingSettingsTab),settings:["custom-network-conditions"],iconName:"performance"}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-offline",category:"NETWORK",title:p(w.goOffline),loadActionDelegate:async()=>new((await u()).ThrottlingManager.ActionDelegate),tags:[p(w.device),p(w.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-low-end-mobile",category:"NETWORK",title:p(w.enableSlowGThrottling),loadActionDelegate:async()=>new((await u()).ThrottlingManager.ActionDelegate),tags:[p(w.device),p(w.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-mid-tier-mobile",category:"NETWORK",title:p(w.enableFastGThrottling),loadActionDelegate:async()=>new((await u()).ThrottlingManager.ActionDelegate),tags:[p(w.device),p(w.throttlingTag)]}),t.ActionRegistration.registerActionExtension({actionId:"network-conditions.network-online",category:"NETWORK",title:p(w.goOnline),loadActionDelegate:async()=>new((await u()).ThrottlingManager.ActionDelegate),tags:[p(w.device),p(w.throttlingTag)]}),n.Settings.registerSettingExtension({storageType:"Synced",settingName:"custom-network-conditions",settingType:"array",defaultValue:[]});const f={main:"Main",networkTitle:"Scripts",showNode:"Show Scripts"},y=e.i18n.registerUIStrings("entrypoints/js_app/js_app.ts",f),A=e.i18n.getLocalizedString.bind(void 0,y),E=e.i18n.getLazilyComputedLocalizedString.bind(void 0,y);let S,b;class R{static instance(e={forceNew:null}){const{forceNew:t}=e;return S&&!t||(S=new R),S}async run(){i.userMetrics.actionTaken(i.UserMetrics.Action.ConnectToNodeJSDirectly),o.Connections.initMainConnection((async()=>{o.TargetManager.TargetManager.instance().createTarget("main",A(f.main),o.Target.Type.Node,null).runtimeAgent().invoke_runIfWaitingForDebugger()}),a.TargetDetachedDialog.TargetDetachedDialog.webSocketConnectionLost)}}t.ViewManager.registerViewExtension({location:"navigator-view",id:"navigator-network",title:E(f.networkTitle),commandPrompt:E(f.showNode),order:2,persistence:"permanent",loadView:async()=>(await async function(){return b||(b=await import("../../panels/sources/sources.js")),b}()).SourcesNavigator.NetworkNavigatorView.instance()}),n.Runnable.registerEarlyInitializationRunnable(R.instance),new r.MainImpl.MainImpl;export{R as JsMainImpl};
