import*as e from"../../models/heap_snapshot_model/heap_snapshot_model.js";import*as t from"../../core/i18n/i18n.js";import*as s from"../../core/platform/platform.js";import*as n from"../../models/text_utils/text_utils.js";class i{#e;#t;#s;#n;#i;#o;#r;constructor(e,t){this.#e=e.strings,this.#t=1,this.#s=[],this.#n={},this.#i={},this.#o={},this.#r=null,this.#a(e),this.#d(e,t)}#a(e){const t=this.#e,s=e.snapshot.meta.trace_function_info_fields,n=s.indexOf("name"),i=s.indexOf("script_name"),o=s.indexOf("script_id"),r=s.indexOf("line"),d=s.indexOf("column"),h=s.length,l=e.trace_function_infos,c=l.length,u=this.#s=new Array(c/h);let g=0;for(let e=0;e<c;e+=h)u[g++]=new a(t[l[e+n]],t[l[e+i]],l[e+o],l[e+r],l[e+d])}#d(e,t){const s=e.trace_tree,n=this.#s,i=this.#i,r=e.snapshot.meta.trace_node_fields,a=r.indexOf("id"),d=r.indexOf("function_info_index"),h=r.indexOf("count"),l=r.indexOf("size"),c=r.indexOf("children"),u=r.length;return function e(s,r,g){const p=n[s[r+d]],f=s[r+a],I=t[f],m=I?I.count:0,x=I?I.size:0,N=new o(f,p,s[r+h],s[r+l],m,x,g);i[f]=N,p.addTraceTopNode(N);const S=s[r+c];for(let t=0;t<S.length;t+=u)N.children.push(e(S,t,N));return N}(s,0,null)}serializeTraceTops(){if(this.#r)return this.#r;const e=this.#r=[],t=this.#s;for(let s=0;s<t.length;s++){const n=t[s];if(0===n.totalCount)continue;const i=this.#t++,o=0===s;e.push(this.#h(i,n,n.totalCount,n.totalSize,n.totalLiveCount,n.totalLiveSize,!o)),this.#o[i]=n}return e.sort((function(e,t){return t.size-e.size})),e}serializeCallers(t){let s=this.#l(t);const n=[];for(;1===s.callers().length;)s=s.callers()[0],n.push(this.#c(s));const i=[],o=s.callers();for(let e=0;e<o.length;e++)i.push(this.#c(o[e]));return new e.HeapSnapshotModel.AllocationNodeCallers(n,i)}serializeAllocationStack(t){let s=this.#i[t];const n=[];for(;s;){const t=s.functionInfo;n.push(new e.HeapSnapshotModel.AllocationStackFrame(t.functionName,t.scriptName,t.scriptId,t.line,t.column)),s=s.parent}return n}traceIds(e){return this.#l(e).traceTopIds}#l(e){let t=this.#n[e];if(!t){t=this.#o[e].bottomUpRoot(),delete this.#o[e],this.#n[e]=t}return t}#c(e){const t=this.#t++;return this.#n[t]=e,this.#h(t,e.functionInfo,e.allocationCount,e.allocationSize,e.liveCount,e.liveSize,e.hasCallers())}#h(t,s,n,i,o,r,a){return new e.HeapSnapshotModel.SerializedAllocationNode(t,s.functionName,s.scriptName,s.scriptId,s.line,s.column,n,i,o,r,a)}}class o{id;functionInfo;allocationCount;allocationSize;liveCount;liveSize;parent;children;constructor(e,t,s,n,i,o,r){this.id=e,this.functionInfo=t,this.allocationCount=s,this.allocationSize=n,this.liveCount=i,this.liveSize=o,this.parent=r,this.children=[]}}class r{functionInfo;allocationCount;allocationSize;liveCount;liveSize;traceTopIds;#u;constructor(e){this.functionInfo=e,this.allocationCount=0,this.allocationSize=0,this.liveCount=0,this.liveSize=0,this.traceTopIds=[],this.#u=[]}addCaller(e){const t=e.functionInfo;let s;for(let e=0;e<this.#u.length;e++){const n=this.#u[e];if(n.functionInfo===t){s=n;break}}return s||(s=new r(t),this.#u.push(s)),s}callers(){return this.#u}hasCallers(){return this.#u.length>0}}class a{functionName;scriptName;scriptId;line;column;totalCount;totalSize;totalLiveCount;totalLiveSize;#r;#g;constructor(e,t,s,n,i){this.functionName=e,this.scriptName=t,this.scriptId=s,this.line=n,this.column=i,this.totalCount=0,this.totalSize=0,this.totalLiveCount=0,this.totalLiveSize=0,this.#r=[]}addTraceTopNode(e){0!==e.allocationCount&&(this.#r.push(e),this.totalCount+=e.allocationCount,this.totalSize+=e.allocationSize,this.totalLiveCount+=e.liveCount,this.totalLiveSize+=e.liveSize)}bottomUpRoot(){return this.#r.length?(this.#g||this.#p(),this.#g):null}#p(){this.#g=new r(this);for(let e=0;e<this.#r.length;e++){let t=this.#r[e],s=this.#g;const n=t.allocationCount,i=t.allocationSize,o=t.liveCount,r=t.liveSize,a=t.id;for(;s.allocationCount+=n,s.allocationSize+=i,s.liveCount+=o,s.liveSize+=r,s.traceTopIds.push(a),t=t.parent,null!==t;)s=s.addCaller(t)}}}var d=Object.freeze({__proto__:null,AllocationProfile:i,TopDownAllocationNode:o,BottomUpAllocationNode:r,FunctionAllocationInfo:a});class h{snapshot;edges;edgeIndex;constructor(e,t){this.snapshot=e,this.edges=e.containmentEdges,this.edgeIndex=t||0}clone(){return new h(this.snapshot,this.edgeIndex)}hasStringName(){throw new Error("Not implemented")}name(){throw new Error("Not implemented")}node(){return this.snapshot.createNode(this.nodeIndex())}nodeIndex(){if(void 0===this.snapshot.edgeToNodeOffset)throw new Error("edgeToNodeOffset is undefined");return this.edges.getValue(this.edgeIndex+this.snapshot.edgeToNodeOffset)}toString(){return"HeapSnapshotEdge: "+this.name()}type(){return this.snapshot.edgeTypes[this.rawType()]}itemIndex(){return this.edgeIndex}serialize(){return new e.HeapSnapshotModel.Edge(this.name(),this.node().serialize(),this.type(),this.edgeIndex)}rawType(){if(void 0===this.snapshot.edgeTypeOffset)throw new Error("edgeTypeOffset is undefined");return this.edges.getValue(this.edgeIndex+this.snapshot.edgeTypeOffset)}isInternal(){throw new Error("Not implemented")}isInvisible(){throw new Error("Not implemented")}isWeak(){throw new Error("Not implemented")}getValueForSorting(e){throw new Error("Not implemented")}nameIndex(){throw new Error("Not implemented")}}class l{#f;constructor(e){this.#f=e.createNode()}itemForIndex(e){return this.#f.nodeIndex=e,this.#f}}class c{#I;constructor(e){this.#I=e.createEdge(0)}itemForIndex(e){return this.#I.edgeIndex=e,this.#I}}class u{#m;constructor(e){this.#m=e.createRetainingEdge(0)}itemForIndex(e){return this.#m.setRetainerIndex(e),this.#m}}class g{#x;edge;constructor(e){this.#x=e,this.edge=e.snapshot.createEdge(e.edgeIndexesStart())}hasNext(){return this.edge.edgeIndex<this.#x.edgeIndexesEnd()}item(){return this.edge}next(){if(void 0===this.edge.snapshot.edgeFieldsCount)throw new Error("edgeFieldsCount is undefined");this.edge.edgeIndex+=this.edge.snapshot.edgeFieldsCount}}class p{snapshot;#N;#S;#y;#w;#T;constructor(e,t){this.snapshot=e,this.setRetainerIndex(t)}clone(){return new p(this.snapshot,this.retainerIndex())}hasStringName(){return this.edge().hasStringName()}name(){return this.edge().name()}nameIndex(){return this.edge().nameIndex()}node(){return this.nodeInternal()}nodeIndex(){if(void 0===this.#y)throw new Error("retainingNodeIndex is undefined");return this.#y}retainerIndex(){return this.#N}setRetainerIndex(e){if(e!==this.#N){if(!this.snapshot.retainingEdges||!this.snapshot.retainingNodes)throw new Error("Snapshot does not contain retaining edges or retaining nodes");this.#N=e,this.#S=this.snapshot.retainingEdges[e],this.#y=this.snapshot.retainingNodes[e],this.#w=null,this.#T=null}}set edgeIndex(e){this.setRetainerIndex(e)}nodeInternal(){return this.#T||(this.#T=this.snapshot.createNode(this.#y)),this.#T}edge(){return this.#w||(this.#w=this.snapshot.createEdge(this.#S)),this.#w}toString(){return this.edge().toString()}itemIndex(){return this.#N}serialize(){const t=this.node(),s=t.serialize();return s.distance=this.#O(),s.ignored=this.snapshot.isNodeIgnoredInRetainersView(t.nodeIndex),new e.HeapSnapshotModel.Edge(this.name(),s,this.type(),this.#S)}type(){return this.edge().type()}isInternal(){return this.edge().isInternal()}getValueForSorting(e){if("!edgeDistance"===e)return this.#O();throw new Error("Invalid field name")}#O(){return this.snapshot.isEdgeIgnoredInRetainersView(this.#S)?e.HeapSnapshotModel.baseUnreachableDistance:this.node().distanceForRetainersView()}}class f{#C;retainer;constructor(e){const t=e.snapshot,s=e.ordinal();if(!t.firstRetainerIndex)throw new Error("Snapshot does not contain firstRetainerIndex");const n=t.firstRetainerIndex[s];this.#C=t.firstRetainerIndex[s+1],this.retainer=t.createRetainingEdge(n)}hasNext(){return this.retainer.retainerIndex()<this.#C}item(){return this.retainer}next(){this.retainer.setRetainerIndex(this.retainer.retainerIndex()+1)}}class I{snapshot;nodeIndex;constructor(e,t){this.snapshot=e,this.nodeIndex=t||0}distance(){return this.snapshot.nodeDistances[this.nodeIndex/this.snapshot.nodeFieldCount]}distanceForRetainersView(){return this.snapshot.getDistanceForRetainersView(this.nodeIndex)}className(){throw new Error("Not implemented")}classIndex(){throw new Error("Not implemented")}dominatorIndex(){const e=this.snapshot.nodeFieldCount;return this.snapshot.dominatorsTree[this.nodeIndex/this.snapshot.nodeFieldCount]*e}edges(){return new g(this)}edgesCount(){return(this.edgeIndexesEnd()-this.edgeIndexesStart())/this.snapshot.edgeFieldsCount}id(){throw new Error("Not implemented")}rawName(){throw new Error("Not implemented")}isRoot(){return this.nodeIndex===this.snapshot.rootNodeIndex}isUserRoot(){throw new Error("Not implemented")}isHidden(){throw new Error("Not implemented")}isArray(){throw new Error("Not implemented")}isSynthetic(){throw new Error("Not implemented")}isDocumentDOMTreesRoot(){throw new Error("Not implemented")}name(){return this.snapshot.strings[this.nameInternal()]}retainedSize(){return this.snapshot.retainedSizes[this.ordinal()]}retainers(){return new f(this)}retainersCount(){const e=this.snapshot,t=this.ordinal();return e.firstRetainerIndex[t+1]-e.firstRetainerIndex[t]}selfSize(){const e=this.snapshot;return e.nodes.getValue(this.nodeIndex+e.nodeSelfSizeOffset)}type(){return this.snapshot.nodeTypes[this.rawType()]}traceNodeId(){const e=this.snapshot;return e.nodes.getValue(this.nodeIndex+e.nodeTraceNodeIdOffset)}itemIndex(){return this.nodeIndex}serialize(){return new e.HeapSnapshotModel.Node(this.id(),this.name(),this.distance(),this.nodeIndex,this.retainedSize(),this.selfSize(),this.type())}nameInternal(){const e=this.snapshot;return e.nodes.getValue(this.nodeIndex+e.nodeNameOffset)}edgeIndexesStart(){return this.snapshot.firstEdgeIndexes[this.ordinal()]}edgeIndexesEnd(){return this.snapshot.firstEdgeIndexes[this.ordinal()+1]}ordinal(){return this.nodeIndex/this.snapshot.nodeFieldCount}nextNodeIndex(){return this.nodeIndex+this.snapshot.nodeFieldCount}rawType(){const e=this.snapshot;return e.nodes.getValue(this.nodeIndex+e.nodeTypeOffset)}isFlatConsString(){if(this.rawType()!==this.snapshot.nodeConsStringType)return!1;for(let e=this.edges();e.hasNext();e.next()){const t=e.edge;if(!t.isInternal())continue;const s=t.name();if(("first"===s||"second"===s)&&""===t.node().name())return!0}return!1}}class m{node;#E;constructor(e){this.node=e,this.#E=e.snapshot.nodes.length}hasNext(){return this.node.nodeIndex<this.#E}item(){return this.node}next(){this.node.nodeIndex=this.node.nextNodeIndex()}}class x{#F;#b;#v;constructor(e,t){this.#F=e,this.#b=t,this.#v=0}hasNext(){return this.#v<this.#b.length}item(){const e=this.#b[this.#v];return this.#F.itemForIndex(e)}next(){++this.#v}}class N{#A;#z;constructor(e,t){this.#A=e,this.#z=t,this.skipFilteredItems()}hasNext(){return this.#A.hasNext()}item(){return this.#A.item()}next(){this.#A.next(),this.skipFilteredItems()}skipFilteredItems(){for(;this.#A.hasNext()&&this.#z&&!this.#z(this.#A.item());)this.#A.next()}}class S{#_;constructor(e){this.#_=e}updateStatus(e){this.sendUpdateEvent(t.i18n.serializeUIString(e))}updateProgress(e,s,n){const i=(100*(n?s/n:0)).toFixed(0);this.sendUpdateEvent(t.i18n.serializeUIString(e,{PH1:i}))}reportProblem(t){this.#_&&this.#_.sendEvent(e.HeapSnapshotModel.HeapSnapshotProgressEvent.BrokenSnapshot,t)}sendUpdateEvent(t){this.#_&&this.#_.sendEvent(e.HeapSnapshotModel.HeapSnapshotProgressEvent.Update,t)}}class y{#D;constructor(e){this.#D=[e]}addError(e){this.#D.length>100||this.#D.push(e)}toString(){return this.#D.join("\n  ")}}class w{nodes;containmentEdges;#R;#V;#k;strings;#j;#H;#M;rootNodeIndexInternal;#P;#U;#B;#L;#W;nodeTypeOffset;nodeNameOffset;nodeIdOffset;nodeSelfSizeOffset;#J;nodeTraceNodeIdOffset;nodeFieldCount;nodeTypes;nodeArrayType;nodeHiddenType;nodeObjectType;nodeNativeType;nodeStringType;nodeConsStringType;nodeSlicedStringType;nodeCodeType;nodeSyntheticType;edgeFieldsCount;edgeTypeOffset;edgeNameOffset;edgeToNodeOffset;edgeTypes;edgeElementType;edgeHiddenType;edgeInternalType;edgeShortcutType;edgeWeakType;edgeInvisibleType;#Q;#$;#q;#G;#K;nodeCount;#X;retainedSizes;firstEdgeIndexes;retainingNodes;retainingEdges;firstRetainerIndex;nodeDistances;firstDominatedNodeIndex;dominatedNodes;dominatorsTree;#Y;#Z;#ee;lazyStringCache;#te;#se;#ne;#ie;#oe;constructor(e,t){this.nodes=e.nodes,this.containmentEdges=e.edges,this.#R=e.snapshot.meta,this.#V=e.samples,this.#k=null,this.strings=e.strings,this.#j=e.locations,this.#H=t,this.#M=-5,this.rootNodeIndexInternal=0,e.snapshot.root_index&&(this.rootNodeIndexInternal=e.snapshot.root_index),this.#P={},this.#B={},this.#L={},this.#W=e,this.#te=new Set,this.#se=new Set,this.#ie=s.TypedArrayUtilities.createBitVector(this.strings.length),this.#oe=new Map}initialize(){const e=this.#R;this.nodeTypeOffset=e.node_fields.indexOf("type"),this.nodeNameOffset=e.node_fields.indexOf("name"),this.nodeIdOffset=e.node_fields.indexOf("id"),this.nodeSelfSizeOffset=e.node_fields.indexOf("self_size"),this.#J=e.node_fields.indexOf("edge_count"),this.nodeTraceNodeIdOffset=e.node_fields.indexOf("trace_node_id"),this.#Z=e.node_fields.indexOf("detachedness"),this.nodeFieldCount=e.node_fields.length,this.nodeTypes=e.node_types[this.nodeTypeOffset],this.nodeArrayType=this.nodeTypes.indexOf("array"),this.nodeHiddenType=this.nodeTypes.indexOf("hidden"),this.nodeObjectType=this.nodeTypes.indexOf("object"),this.nodeNativeType=this.nodeTypes.indexOf("native"),this.nodeStringType=this.nodeTypes.indexOf("string"),this.nodeConsStringType=this.nodeTypes.indexOf("concatenated string"),this.nodeSlicedStringType=this.nodeTypes.indexOf("sliced string"),this.nodeCodeType=this.nodeTypes.indexOf("code"),this.nodeSyntheticType=this.nodeTypes.indexOf("synthetic"),this.edgeFieldsCount=e.edge_fields.length,this.edgeTypeOffset=e.edge_fields.indexOf("type"),this.edgeNameOffset=e.edge_fields.indexOf("name_or_index"),this.edgeToNodeOffset=e.edge_fields.indexOf("to_node"),this.edgeTypes=e.edge_types[this.edgeTypeOffset],this.edgeTypes.push("invisible"),this.edgeElementType=this.edgeTypes.indexOf("element"),this.edgeHiddenType=this.edgeTypes.indexOf("hidden"),this.edgeInternalType=this.edgeTypes.indexOf("internal"),this.edgeShortcutType=this.edgeTypes.indexOf("shortcut"),this.edgeWeakType=this.edgeTypes.indexOf("weak"),this.edgeInvisibleType=this.edgeTypes.indexOf("invisible");const t=e.location_fields||[];this.#Q=t.indexOf("object_index"),this.#$=t.indexOf("script_id"),this.#q=t.indexOf("line"),this.#G=t.indexOf("column"),this.#K=t.length,this.nodeCount=this.nodes.length/this.nodeFieldCount,this.#X=this.containmentEdges.length/this.edgeFieldsCount,this.retainedSizes=new Float64Array(this.nodeCount),this.firstEdgeIndexes=new Uint32Array(this.nodeCount+1),this.retainingNodes=new Uint32Array(this.#X),this.retainingEdges=new Uint32Array(this.#X),this.firstRetainerIndex=new Uint32Array(this.nodeCount+1),this.nodeDistances=new Int32Array(this.nodeCount),this.firstDominatedNodeIndex=new Uint32Array(this.nodeCount+1),this.dominatedNodes=new Uint32Array(this.nodeCount-1),this.#H.updateStatus("Building edge indexes…"),this.buildEdgeIndexes(),this.#H.updateStatus("Building retainers…"),this.buildRetainers(),this.#H.updateStatus("Propagating DOM state…"),this.propagateDOMState(),this.#H.updateStatus("Calculating node flags…"),this.calculateFlags(),this.#H.updateStatus("Calculating distances…"),this.calculateDistances(!1),this.#H.updateStatus("Building postorder index…");const s=this.buildPostOrderIndex();if(this.#H.updateStatus("Building dominator tree…"),this.dominatorsTree=this.buildDominatorTree(s.postOrderIndex2NodeOrdinal,s.nodeOrdinal2PostOrderIndex),this.#H.updateStatus("Calculating shallow sizes…"),this.calculateShallowSizes(),this.#H.updateStatus("Calculating retained sizes…"),this.calculateRetainedSizes(s.postOrderIndex2NodeOrdinal),this.#H.updateStatus("Building dominated nodes…"),this.buildDominatedNodes(),this.#H.updateStatus("Calculating statistics…"),this.calculateStatistics(),this.#H.updateStatus("Calculating samples…"),this.buildSamples(),this.#H.updateStatus("Building locations…"),this.buildLocationMap(),this.#H.updateStatus("Finished processing."),this.#W.snapshot.trace_function_count){this.#H.updateStatus("Building allocation statistics…");const e=this.nodes.length,t=this.nodeFieldCount,s=this.rootNode(),n={};for(let i=0;i<e;i+=t){s.nodeIndex=i;const e=s.traceNodeId();let t=n[e];t||(n[e]=t={count:0,size:0,ids:[]}),t.count++,t.size+=s.selfSize(),t.ids.push(s.id())}this.#Y=new i(this.#W,n),this.#H.updateStatus("done")}}buildEdgeIndexes(){const e=this.nodes,t=this.nodeCount,s=this.firstEdgeIndexes,n=this.nodeFieldCount,i=this.edgeFieldsCount,o=this.#J;s[t]=this.containmentEdges.length;for(let r=0,a=0;r<t;++r)s[r]=a,a+=e.getValue(r*n+o)*i}buildRetainers(){const e=this.retainingNodes,t=this.retainingEdges,s=this.firstRetainerIndex,n=this.containmentEdges,i=this.edgeFieldsCount,o=this.nodeFieldCount,r=this.edgeToNodeOffset,a=this.firstEdgeIndexes,d=this.nodeCount;for(let e=r,t=n.length;e<t;e+=i){const t=n.getValue(e);if(t%o)throw new Error("Invalid toNodeIndex "+t);++s[t/o]}for(let t=0,n=0;t<d;t++){const i=s[t];s[t]=n,e[n]=i,n+=i}s[d]=e.length;let h=a[0];for(let l=0;l<d;++l){const d=h;h=a[l+1];const c=l*o;for(let a=d;a<h;a+=i){const i=n.getValue(a+r);if(i%o)throw new Error("Invalid toNodeIndex "+i);const d=s[i/o],h=d+--e[d];e[h]=c,t[h]=a}}}allNodes(){return new m(this.rootNode())}rootNode(){return this.createNode(this.rootNodeIndexInternal)}get rootNodeIndex(){return this.rootNodeIndexInternal}get totalSize(){return this.rootNode().retainedSize()}getDominatedIndex(e){if(e%this.nodeFieldCount)throw new Error("Invalid nodeIndex: "+e);return this.firstDominatedNodeIndex[e/this.nodeFieldCount]}createFilter(e){const{minNodeId:t,maxNodeId:s,allocationNodeId:n,filterName:i}=e;let o;if("number"==typeof n){if(o=this.createAllocationStackFilter(n),!o)throw new Error("Unable to create filter");o.key="AllocationNodeId: "+n}else"number"==typeof t&&"number"==typeof s?(o=this.createNodeIdFilter(t,s),o.key="NodeIdRange: "+t+".."+s):void 0!==i&&(o=this.createNamedFilter(i),o.key="NamedFilter: "+i);return o}search(e,t){const n=e.query;const i=e.isRegex?new RegExp(n):s.StringUtilities.createPlainTextSearchRegex(n,"i");const o=e.isRegex||!e.caseSensitive?function(e,t,s){return i.test(t)&&e.add(s),e}:function(e,t,s){return-1!==t.indexOf(n)&&e.add(s),e},r=this.strings.reduce(o,new Set);if(!r.size)return[];const a=this.createFilter(t),d=[],h=this.nodes.length,l=this.nodes,c=this.nodeNameOffset,u=this.nodeIdOffset,g=this.nodeFieldCount,p=this.rootNode();for(let e=0;e<h;e+=g)p.nodeIndex=e,a&&!a(p)||r.has(l.getValue(e+c))&&d.push(l.getValue(e+u));return d}aggregatesWithFilter(e){const t=this.createFilter(e),s=t?t.key:"allObjects";return this.getAggregatesByClassName(!1,s,t)}createNodeIdFilter(e,t){return function(s){const n=s.id();return n>e&&n<=t}}createAllocationStackFilter(e){if(!this.#Y)throw new Error("No Allocation Profile provided");const t=this.#Y.traceIds(e);if(!t.length)return;const s={};for(let e=0;e<t.length;e++)s[t[e]]=!0;return function(e){return Boolean(s[e.traceNodeId()])}}createNamedFilter(e){const t=s.TypedArrayUtilities.createBitVector(this.nodeCount),n=e=>{const s=e.nodeIndex/this.nodeFieldCount;return t.getBit(s)},i=e=>{const s=new Int32Array(this.nodeCount);for(let e=0;e<this.nodeCount;++e)s[e]=this.#M;const n=new Uint32Array(this.nodeCount);s[this.rootNode().ordinal()]=0,n[0]=this.rootNode().nodeIndex;this.bfs(n,1,s,e);for(let e=0;e<this.nodeCount;++e)s[e]!==this.#M&&t.setBit(e)},o=()=>{for(let e=0;e<this.nodeCount;++e)this.nodeDistances[e]===this.#M&&t.setBit(e)};switch(e){case"objectsRetainedByDetachedDomNodes":return i(((e,t)=>2!==this.nodes.getValue(t.nodeIndex()+this.#Z))),o(),e=>!n(e);case"objectsRetainedByConsole":return i(((e,t)=>!(e.isSynthetic()&&t.hasStringName()&&t.name().endsWith(" / DevTools console")))),o(),e=>!n(e);case"duplicatedStrings":{const e=new Map,s=this.createNode(0);for(let n=0;n<this.nodeCount;++n){s.nodeIndex=n*this.nodeFieldCount;const i=s.rawType();if(i===this.nodeStringType||i===this.nodeConsStringType){if(s.isFlatConsString())continue;const n=s.name(),i=e.get(n);void 0===i?e.set(n,s.nodeIndex):(t.setBit(i/this.nodeFieldCount),t.setBit(s.nodeIndex/this.nodeFieldCount))}}return n}}throw new Error("Invalid filter name")}getAggregatesByClassName(e,t,s){const n=this.buildAggregates(s);let i;return t&&this.#B[t]?i=this.#B[t]:(this.calculateClassesRetainedSize(n.aggregatesByClassIndex,s),i=n.aggregatesByClassName,t&&(this.#B[t]=i)),!e||t&&this.#L[t]||(this.sortAggregateIndexes(i),t&&(this.#L[t]=e)),i}allocationTracesTops(){return this.#Y.serializeTraceTops()}allocationNodeCallers(e){return this.#Y.serializeCallers(e)}allocationStack(e){const t=this.createNode(e).traceNodeId();return t?this.#Y.serializeAllocationStack(t):null}aggregatesForDiff(){if(this.#U)return this.#U;const e=this.getAggregatesByClassName(!0,"allObjects");this.#U={};const t=this.createNode();for(const s in e){const n=e[s].idxs,i=new Array(n.length),o=new Array(n.length);for(let e=0;e<n.length;e++)t.nodeIndex=n[e],i[e]=t.id(),o[e]=t.selfSize();this.#U[s]={indexes:n,ids:i,selfSizes:o}}return this.#U}isUserRoot(e){return!0}calculateShallowSizes(){}calculateDistances(t,s){const n=this.nodeCount;if(t){const e=s;s=(t,s)=>!this.#te.has(s.nodeIndex())&&(!e||e(t,s)),void 0===this.#ne&&(this.#ne=new Int32Array(n))}const i=t?this.#ne:this.nodeDistances,o=this.#M;for(let e=0;e<n;++e)i[e]=o;const r=new Uint32Array(this.nodeCount);let a=0;for(let e=this.rootNode().edges();e.hasNext();e.next()){const t=e.edge.node();this.isUserRoot(t)&&(i[t.ordinal()]=1,r[a++]=t.nodeIndex)}this.bfs(r,a,i,s),i[this.rootNode().ordinal()]=a>0?e.HeapSnapshotModel.baseSystemDistance:0,r[0]=this.rootNode().nodeIndex,a=1,this.bfs(r,a,i,s)}bfs(e,t,s,n){const i=this.edgeFieldsCount,o=this.nodeFieldCount,r=this.containmentEdges,a=this.firstEdgeIndexes,d=this.edgeToNodeOffset,h=this.edgeTypeOffset,l=this.nodeCount,c=this.edgeWeakType,u=this.#M;let g=0;const p=this.createEdge(0),f=this.createNode(0);for(;g<t;){const l=e[g++],I=l/o,m=s[I]+1,x=a[I],N=a[I+1];f.nodeIndex=l;for(let a=x;a<N;a+=i){if(r.getValue(a+h)===c)continue;const i=r.getValue(a+d),l=i/o;s[l]===u&&(p.edgeIndex=a,n&&!n(f,p)||(s[l]=m,e[t++]=i))}}if(t>l)throw new Error("BFS failed. Nodes to visit ("+t+") is more than nodes count ("+l+")")}buildAggregates(e){const t={},s={},n=[],i=this.nodes,o=i.length,r=this.nodeFieldCount,a=this.nodeSelfSizeOffset,d=this.rootNode(),h=this.nodeDistances;for(let l=0;l<o;l+=r){if(d.nodeIndex=l,e&&!e(d))continue;const o=i.getValue(l+a);if(!o)continue;const c=d.classIndex(),u=h[l/r];if(c in t){const e=t[c];if(!e)continue;e.distance=Math.min(e.distance,u),++e.count,e.self+=o,e.idxs.push(l)}else{const e=d.type(),i={count:1,distance:u,self:o,maxRet:0,type:e,name:"object"===e||"native"===e?d.className():null,idxs:[l]};t[c]=i,n.push(c),s[d.className()]=i}}for(let e=0,s=n.length;e<s;++e){const s=t[n[e]];s&&(s.idxs=s.idxs.slice())}return{aggregatesByClassName:s,aggregatesByClassIndex:t}}calculateClassesRetainedSize(e,t){const s=this.rootNodeIndexInternal,n=this.createNode(s),i=[s],o=[-1],r=[],a=new Map,d=this.nodeFieldCount,h=this.dominatedNodes,l=this.firstDominatedNodeIndex;for(;i.length;){const s=i.pop();n.nodeIndex=s;let c=n.classIndex();const u=Boolean(a.get(c)),g=s/d,p=l[g],f=l[g+1];u||t&&!t(n)||!n.selfSize()||(e[c].maxRet+=n.retainedSize(),p!==f&&(a.set(c,!0),o.push(i.length),r.push(c)));for(let e=p;e<f;e++)i.push(h[e]);const I=i.length;for(;o[o.length-1]===I;)o.pop(),c=r.pop(),a.set(c,!1)}}sortAggregateIndexes(e){const t=this.createNode(),s=this.createNode();for(const n in e)e[n].idxs.sort(((e,n)=>(t.nodeIndex=e,s.nodeIndex=n,t.id()<s.id()?-1:1)))}tryParseWeakMapEdgeName(e){if(this.#ie.getBit(e))return;const t=this.strings[e].match(/^\d+(?<duplicatedPart> \/ part of key \(.*? @\d+\) -> value \(.*? @\d+\) pair in WeakMap \(table @(?<tableId>\d+)\))$/);if(t)return t.groups;this.#ie.setBit(e)}isEssentialEdge(e,t){const s=this.containmentEdges.getValue(t+this.edgeTypeOffset);if(s===this.edgeInternalType){const s=this.containmentEdges.getValue(t+this.edgeNameOffset),n=this.tryParseWeakMapEdgeName(s);if(n){return this.nodes.getValue(e+this.nodeIdOffset)!==parseInt(n.tableId,10)}}return s!==this.edgeWeakType&&(s!==this.edgeShortcutType||e===this.rootNodeIndexInternal)}buildPostOrderIndex(){const e=this.nodeFieldCount,t=this.nodeCount,s=this.rootNodeIndexInternal/e,n=this.edgeFieldsCount,i=this.edgeToNodeOffset,o=this.firstEdgeIndexes,r=this.containmentEdges,a=this.userObjectsMapAndFlag(),d=a?a.map:null,h=a?a.flag:0,l=new Uint32Array(t),c=new Uint32Array(t),u=new Uint32Array(t),g=new Uint32Array(t),p=new Uint8Array(t);let f=0,I=0;l[0]=s,c[0]=o[s],p[s]=1;let m=0;for(;;){for(++m;I>=0;){const t=l[I],a=c[I];if(a<o[t+1]){if(c[I]+=n,!this.isEssentialEdge(t*e,a))continue;const u=r.getValue(a+i)/e;if(p[u])continue;const g=!d||d[t]&h,f=!d||d[u]&h;if(t!==s&&f&&!g)continue;++I,l[I]=u,c[I]=o[u],p[u]=1}else g[t]=f,u[f++]=t,--I}if(f===t||m>1)break;const a=new y(`Heap snapshot: ${t-f} nodes are unreachable from the root. Following nodes have only weak retainers:`),x=this.rootNode();--f,I=0,l[0]=s,c[0]=o[s+1];for(let s=0;s<t;++s){if(p[s]||!this.hasOnlyWeakRetainers(s))continue;l[++I]=s,c[I]=o[s],p[s]=1,x.nodeIndex=s*e;const t=[];for(let e=x.retainers();e.hasNext();e.next())t.push(`${e.item().node().name()}@${e.item().node().id()}.${e.item().name()}`);a.addError(`${x.name()} @${x.id()}  weak retainers: ${t.join(", ")}`)}console.warn(a.toString())}if(f!==t){const n=new y("Still found "+(t-f)+" unreachable nodes in heap snapshot:"),i=this.rootNode();--f;for(let s=0;s<t;++s)p[s]||(i.nodeIndex=s*e,n.addError(i.name()+" @"+i.id()),g[s]=f,u[f++]=s);g[s]=f,u[f++]=s,console.warn(n.toString())}return{postOrderIndex2NodeOrdinal:u,nodeOrdinal2PostOrderIndex:g}}hasOnlyWeakRetainers(e){const t=this.edgeTypeOffset,s=this.edgeWeakType,n=this.edgeShortcutType,i=this.containmentEdges,o=this.retainingEdges,r=this.firstRetainerIndex[e],a=this.firstRetainerIndex[e+1];for(let e=r;e<a;++e){const r=o[e],a=i.getValue(r+t);if(a!==s&&a!==n)return!1}return!0}buildDominatorTree(e,t){const n=this.nodeFieldCount,i=this.firstRetainerIndex,o=this.retainingNodes,r=this.retainingEdges,a=this.edgeFieldsCount,d=this.edgeToNodeOffset,h=this.firstEdgeIndexes,l=this.containmentEdges,c=this.rootNodeIndexInternal,u=this.userObjectsMapAndFlag(),g=u?u.map:null,p=u?u.flag:0,f=e.length,I=f-1,m=f,x=new Uint32Array(f);for(let e=0;e<I;++e)x[e]=m;x[I]=I;const N=s.TypedArrayUtilities.createBitVector(f);let S;{S=this.rootNodeIndexInternal/n;const e=h[S+1];for(let s=h[S];s<e;s+=a){if(!this.isEssentialEdge(this.rootNodeIndexInternal,s))continue;const e=l.getValue(s+d)/n;N.setBit(t[e])}}let y=!0;for(;y;){y=!1;for(let s=N.previous(I);s>=0;s=N.previous(s)){if(N.clearBit(s),x[s]===I)continue;S=e[s];const u=!g||g[S]&p;let f=m;const w=i[S],T=i[S+1];let O=!0;for(let e=w;e<T;++e){const s=r[e],i=o[e];if(!this.isEssentialEdge(i,s))continue;O=!1;const a=i/n,d=!g||g[a]&p;if(i!==c&&u&&!d)continue;let h=t[a];if(x[h]!==m){if(f===m)f=h;else for(;h!==f;){for(;h<f;)h=x[h];for(;f<h;)f=x[f]}if(f===I)break}}if(O&&(f=I),f!==m&&x[s]!==f){x[s]=f,y=!0,S=e[s];const i=h[S]+d,o=h[S+1];for(let e=i;e<o;e+=a){const s=l.getValue(e)/n;N.setBit(t[s])}}}}const w=new Uint32Array(f);for(let t=0,s=x.length;t<s;++t)S=e[t],w[S]=e[x[t]];return w}calculateRetainedSizes(e){const t=this.nodeCount,s=this.nodes,n=this.nodeSelfSizeOffset,i=this.nodeFieldCount,o=this.dominatorsTree,r=this.retainedSizes;for(let e=0;e<t;++e)r[e]=s.getValue(e*i+n);for(let s=0;s<t-1;++s){const t=e[s];r[o[t]]+=r[t]}}buildDominatedNodes(){const e=this.firstDominatedNodeIndex,t=this.dominatedNodes,s=this.nodeFieldCount,n=this.dominatorsTree;let i=0,o=this.nodeCount;const r=this.rootNodeIndexInternal/s;if(r===i)i=1;else{if(r!==o-1)throw new Error("Root node is expected to be either first or last");o-=1}for(let t=i;t<o;++t)++e[n[t]];let a=0;for(let s=0,n=this.nodeCount;s<n;++s){const n=t[a]=e[s];e[s]=a,a+=n}e[this.nodeCount]=t.length;for(let r=i;r<o;++r){let i=e[n[r]];i+=--t[i],t[i]=r*s}}iterateFilteredChildren(e,t,s){const n=this.firstEdgeIndexes[e],i=this.firstEdgeIndexes[e+1];for(let e=n;e<i;e+=this.edgeFieldsCount){const n=this.containmentEdges.getValue(e+this.edgeToNodeOffset)/this.nodeFieldCount;t(this.containmentEdges.getValue(e+this.edgeTypeOffset))&&s(n)}}addString(e){return this.strings.push(e),this.strings.length-1}propagateDOMState(){if(-1===this.#Z)return;console.time("propagateDOMState");const e=new Uint8Array(this.nodeCount),t=[],s=[],n=new Map,i=function(i,o,r){if(e[o])return;const a=o*i.nodeFieldCount;i.nodes.getValue(a+i.nodeTypeOffset)===i.nodeNativeType?(i.nodes.setValue(a+i.#Z,r),1===r?t.push(o):2===r&&(!function(e,t){const s=e.nodes.getValue(t+e.nodeNameOffset);let i=n.get(s);void 0===i&&(i=e.addString("Detached "+e.strings[s]),n.set(s,i)),e.nodes.setValue(t+e.nodeNameOffset,i)}(i,a),s.push(o)),e[o]=1):e[o]=1},o=function(e,t,s){e.iterateFilteredChildren(t,(t=>![e.edgeHiddenType,e.edgeInvisibleType,e.edgeWeakType].includes(t)),(t=>i(e,t,s)))};for(let e=0;e<this.nodeCount;++e){const t=this.nodes.getValue(e*this.nodeFieldCount+this.#Z);0!==t&&i(this,e,t)}for(;0!==t.length;){o(this,t.pop(),1)}for(;0!==s.length;){const e=s.pop();1!==this.nodes.getValue(e*this.nodeFieldCount+this.#Z)&&o(this,e,2)}console.timeEnd("propagateDOMState")}buildSamples(){const t=this.#V;if(!t||!t.length)return;const n=t.length/2,i=new Array(n),o=new Array(n),r=new Array(n),a=this.#R.sample_fields.indexOf("timestamp_us"),d=this.#R.sample_fields.indexOf("last_assigned_id");for(let e=0;e<n;e++)i[e]=0,o[e]=t[2*e+a]/1e3,r[e]=t[2*e+d];const h=this.nodes.length,l=this.nodeFieldCount,c=this.rootNode();for(let e=0;e<h;e+=l){c.nodeIndex=e;const t=c.id();if(t%2==0)continue;const o=s.ArrayUtilities.lowerBound(r,t,s.ArrayUtilities.DEFAULT_COMPARATOR);o!==n&&(i[o]+=c.selfSize())}this.#k=new e.HeapSnapshotModel.Samples(o,r,i)}buildLocationMap(){const t=new Map,s=this.#j;for(let n=0;n<s.length;n+=this.#K){const i=s[n+this.#Q],o=s[n+this.#$],r=s[n+this.#q],a=s[n+this.#G];t.set(i,new e.HeapSnapshotModel.Location(o,r,a))}this.#ee=t}getLocation(e){return this.#ee.get(e)||null}getSamples(){return this.#k}calculateFlags(){throw new Error("Not implemented")}calculateStatistics(){throw new Error("Not implemented")}userObjectsMapAndFlag(){throw new Error("Not implemented")}calculateSnapshotDiff(t,s){let n=this.#P[t];if(n)return n;n={};const i=this.getAggregatesByClassName(!0,"allObjects");for(const e in s){const t=s[e],o=this.calculateDiffForClass(t,i[e]);o&&(n[e]=o)}const o=new e.HeapSnapshotModel.AggregateForDiff;for(const e in i){if(e in s)continue;const t=this.calculateDiffForClass(o,i[e]);t&&(n[e]=t)}return this.#P[t]=n,n}calculateDiffForClass(t,s){const n=t.ids,i=t.indexes,o=t.selfSizes,r=s?s.idxs:[];let a=0,d=0;const h=n.length,l=r.length,c=new e.HeapSnapshotModel.Diff,u=this.createNode(r[d]);for(;a<h&&d<l;){const e=n[a];e<u.id()?(c.deletedIndexes.push(i[a]),c.removedCount++,c.removedSize+=o[a],++a):e>u.id()?(c.addedIndexes.push(r[d]),c.addedCount++,c.addedSize+=u.selfSize(),u.nodeIndex=r[++d]):(++a,u.nodeIndex=r[++d])}for(;a<h;)c.deletedIndexes.push(i[a]),c.removedCount++,c.removedSize+=o[a],++a;for(;d<l;)c.addedIndexes.push(r[d]),c.addedCount++,c.addedSize+=u.selfSize(),u.nodeIndex=r[++d];return c.countDelta=c.addedCount-c.removedCount,c.sizeDelta=c.addedSize-c.removedSize,c.addedCount||c.removedCount?c:null}nodeForSnapshotObjectId(e){for(let t=this.allNodes();t.hasNext();t.next())if(t.node.id()===e)return t.node;return null}nodeClassName(e){const t=this.nodeForSnapshotObjectId(e);return t?t.className():null}idsOfObjectsWithName(e){const t=[];for(let s=this.allNodes();s.hasNext();s.next())s.item().name()===e&&t.push(s.item().id());return t}createEdgesProvider(e){const t=this.createNode(e),s=this.containmentEdgesFilter(),n=new c(this);return new C(this,s,t.edges(),n)}createEdgesProviderForTest(e,t){const s=this.createNode(e),n=new c(this);return new C(this,t,s.edges(),n)}retainingEdgesFilter(){return null}containmentEdgesFilter(){return null}createRetainingEdgesProvider(e){const t=this.createNode(e),s=this.retainingEdgesFilter(),n=new u(this);return new C(this,s,t.retainers(),n)}createAddedNodesProvider(e,t){const s=this.#P[e][t];return new E(this,s.addedIndexes)}createDeletedNodesProvider(e){return new E(this,e)}createNodesProviderForClass(e,t){return new E(this,this.aggregatesWithFilter(t)[e].idxs)}maxJsNodeId(){const e=this.nodeFieldCount,t=this.nodes,s=t.length;let n=0;for(let i=this.nodeIdOffset;i<s;i+=e){const e=t.getValue(i);e%2!=0&&(n<e&&(n=e))}return n}updateStaticData(){return new e.HeapSnapshotModel.StaticData(this.nodeCount,this.rootNodeIndexInternal,this.totalSize,this.maxJsNodeId())}ignoreNodeInRetainersView(e){this.#te.add(e),this.calculateDistances(!0),this.#re()}unignoreNodeInRetainersView(e){this.#te.delete(e),0===this.#te.size?this.#ne=void 0:this.calculateDistances(!0),this.#re()}unignoreAllNodesInRetainersView(){this.#te.clear(),this.#ne=void 0,this.#re()}#re(){const e=this.#ne;if(this.#se.clear(),void 0===e)return;const t=new s.MapUtilities.Multimap,n=this.#M,{nodeCount:i,nodeFieldCount:o}=this,r=this.createNode(0);for(let s=0;s<i;++s)if(e[s]===n){r.nodeIndex=s*o;for(let e=r.edges();e.hasNext();e.next()){const s=e.edge;if(!s.isInternal())continue;const n=this.tryParseWeakMapEdgeName(s.nameIndex());n&&t.set(s.nodeIndex(),n.duplicatedPart)}}for(const e of t.keys()){r.nodeIndex=e;for(let s=r.retainers();s.hasNext();s.next()){const n=s.item();if(!n.isInternal())continue;const i=this.tryParseWeakMapEdgeName(n.nameIndex());if(i&&t.hasValue(e,i.duplicatedPart)){const e=this.retainingEdges[n.itemIndex()];this.#se.add(e)}}}}areNodesIgnoredInRetainersView(){return this.#te.size>0}getDistanceForRetainersView(t){const s=t/this.nodeFieldCount,n=(this.#ne??this.nodeDistances)[s];return n===this.#M?Math.max(0,this.nodeDistances[s])+e.HeapSnapshotModel.baseUnreachableDistance:n}isNodeIgnoredInRetainersView(e){return this.#te.has(e)}isEdgeIgnoredInRetainersView(e){return this.#se.has(e)}getIndexForSyntheticClassName(e){let t=this.#oe.get(e);return void 0===t&&(t=this.addString(e),this.#oe.set(e,t)),t}}class T{location_fields=[];node_fields=[];node_types=[];edge_fields=[];edge_types=[];trace_function_info_fields=[];trace_node_fields=[];sample_fields=[];type_strings={}}class O{iterator;#ae;#de;iterationOrder;currentComparator;#he;#le;constructor(e,t){this.iterator=e,this.#ae=t,this.#de=!e.hasNext(),this.iterationOrder=null,this.currentComparator=null,this.#he=0,this.#le=0}createIterationOrder(){if(!this.iterationOrder){this.iterationOrder=[];for(let e=this.iterator;e.hasNext();e.next())this.iterationOrder.push(e.item().itemIndex())}}isEmpty(){return this.#de}serializeItemsRange(t,s){if(this.createIterationOrder(),t>s)throw new Error("Start position > end position: "+t+" > "+s);if(!this.iterationOrder)throw new Error("Iteration order undefined");if(s>this.iterationOrder.length&&(s=this.iterationOrder.length),this.#he<s&&t<this.iterationOrder.length-this.#le&&this.currentComparator){const e=this.currentComparator;this.sort(e,this.#he,this.iterationOrder.length-1-this.#le,t,s-1),t<=this.#he&&(this.#he=s),s>=this.iterationOrder.length-this.#le&&(this.#le=this.iterationOrder.length-t)}let n=t;const i=s-t,o=new Array(i);for(let e=0;e<i;++e){const t=this.iterationOrder[n++],s=this.#ae.itemForIndex(t);o[e]=s.serialize()}return new e.HeapSnapshotModel.ItemsRange(t,s,this.iterationOrder.length,o)}sortAndRewind(e){this.currentComparator=e,this.#he=0,this.#le=0}}class C extends O{snapshot;constructor(e,t,s,n){super(t?new N(s,t):s,n),this.snapshot=e}sort(e,t,n,i,o){const r=e.fieldName1,a=e.fieldName2,d=e.ascending1,h=e.ascending2,l=this.iterator.item().clone(),c=l.clone(),u=this.snapshot.createNode(),g=this.snapshot.createNode();function p(e,t,s,n){l.edgeIndex=s,c.edgeIndex=n;let i=0;if("!edgeName"===e){if("__proto__"===c.name())return-1;if("__proto__"===l.name())return 1;i=l.hasStringName()===c.hasStringName()?l.name()<c.name()?-1:l.name()>c.name()?1:0:l.hasStringName()?-1:1}else i=l.getValueForSorting(e)-c.getValueForSorting(e);return t?i:-i}function f(e,t,s,n){l.edgeIndex=s,u.nodeIndex=l.nodeIndex();const i=u[e]();c.edgeIndex=n,g.nodeIndex=c.nodeIndex();const o=g[e](),r=i<o?-1:i>o?1:0;return t?r:-r}if(!this.iterationOrder)throw new Error("Iteration order not defined");function I(e){return e.startsWith("!edge")}I(r)?I(a)?s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=p(r,d,e,t);return 0===s&&(s=p(a,h,e,t)),0===s?e-t:s}),t,n,i,o):s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=p(r,d,e,t);return 0===s&&(s=f(a,h,e,t)),0===s?e-t:s}),t,n,i,o):I(a)?s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=f(r,d,e,t);return 0===s&&(s=p(a,h,e,t)),0===s?e-t:s}),t,n,i,o):s.ArrayUtilities.sortRange(this.iterationOrder,(function(e,t){let s=f(r,d,e,t);return 0===s&&(s=f(a,h,e,t)),0===s?e-t:s}),t,n,i,o)}}class E extends O{snapshot;constructor(e,t){const s=new l(e);super(new x(s,t),s),this.snapshot=e}nodePosition(e){this.createIterationOrder();const t=this.snapshot.createNode();let s=0;if(!this.iterationOrder)throw new Error("Iteration order not defined");for(;s<this.iterationOrder.length&&(t.nodeIndex=this.iterationOrder[s],t.id()!==e);s++);if(s===this.iterationOrder.length)return-1;const n=this.iterationOrder[s];let i=0;const o=this.currentComparator,r=this.buildCompareFunction(o);for(let e=0;e<this.iterationOrder.length;e++)r(this.iterationOrder[e],n)<0&&++i;return i}buildCompareFunction(e){const t=this.snapshot.createNode(),s=this.snapshot.createNode(),n=t[e.fieldName1],i=t[e.fieldName2],o=e.ascending1?1:-1,r=e.ascending2?1:-1;function a(e,n){const i=e.call(t),o=e.call(s);return i<o?-n:i>o?n:0}return function(e,d){t.nodeIndex=e,s.nodeIndex=d;let h=a(n,o);return 0===h&&(h=a(i,r)),h||e-d}}sort(e,t,n,i,o){if(!this.iterationOrder)throw new Error("Iteration order not defined");s.ArrayUtilities.sortRange(this.iterationOrder,this.buildCompareFunction(e),t,n,i,o)}}class F extends w{nodeFlags;lazyStringCache;flags;#ce;constructor(e,t){super(e,t),this.nodeFlags={canBeQueried:1,detachedDOMTreeNode:2,pageObject:4},this.lazyStringCache={},this.initialize()}createNode(e){return new b(this,void 0===e?-1:e)}createEdge(e){return new v(this,e)}createRetainingEdge(e){return new A(this,e)}containmentEdgesFilter(){return e=>!e.isInvisible()}retainingEdgesFilter(){const e=this.containmentEdgesFilter();return function(t){return e(t)&&!t.node().isRoot()&&!t.isWeak()}}calculateFlags(){this.flags=new Uint32Array(this.nodeCount),this.markDetachedDOMTreeNodes(),this.markQueriableHeapObjects(),this.markPageOwnedNodes()}#ue(){for(let e=this.rootNode().edges();e.hasNext();e.next())if(this.isUserRoot(e.edge.node()))return!0;return!1}calculateShallowSizes(){if(!this.#ue())return;const{nodeCount:e,nodes:t,nodeFieldCount:s,nodeSelfSizeOffset:n}=this,i=4294967295,o=4294967294;if(e>=o)throw new Error("Too many nodes for calculateShallowSizes");const r=new Uint32Array(e),a=[],d=this.createNode(0);for(let t=0;t<e;++t)d.isHidden()||d.isArray()?r[t]=i:(r[t]=t,a.push(t)),d.nodeIndex=d.nextNodeIndex();for(;0!==a.length;){const e=a.pop(),t=r[e];d.nodeIndex=e*s;for(let e=d.edges();e.hasNext();e.next()){const n=e.edge;if(n.isWeak())continue;const d=n.nodeIndex()/s;switch(r[d]){case i:r[d]=t,a.push(d);break;case d:case t:case o:break;default:r[d]=o,a.push(d)}}}for(let a=0;a<e;++a){const e=r[a];switch(e){case i:case o:case a:break;default:{const i=a*s,o=e*s;if(d.nodeIndex=o,d.isSynthetic()||d.isRoot())break;const r=t.getValue(i+n);t.setValue(i+n,0),t.setValue(o+n,t.getValue(o+n)+r);break}}}}calculateDistances(e){const t=new Set,s=this;super.calculateDistances(e,(function(e,n){if(e.isHidden()&&"sloppy_function_map"===n.name()&&"system / NativeContext"===e.rawName())return!1;if(e.isArray()&&"(map descriptors)"===e.rawName()){const e=parseInt(n.name(),10);return e<2||e%3!=1}if(n.isInternal()){const e=s.tryParseWeakMapEdgeName(n.nameIndex());if(e&&!t.delete(e.duplicatedPart))return t.add(e.duplicatedPart),!1}return!0}))}isUserRoot(e){return e.isUserRoot()||e.isDocumentDOMTreesRoot()}userObjectsMapAndFlag(){return{map:this.flags,flag:this.nodeFlags.pageObject}}flagsOfNode(e){return this.flags[e.nodeIndex/this.nodeFieldCount]}markDetachedDOMTreeNodes(){const e=this.nodes,t=e.length,s=this.nodeFieldCount,n=this.nodeNativeType,i=this.nodeTypeOffset,o=this.nodeFlags.detachedDOMTreeNode,r=this.rootNode();for(let a=0,d=0;a<t;a+=s,d++){e.getValue(a+i)===n&&(r.nodeIndex=a,r.name().startsWith("Detached ")&&(this.flags[d]|=o))}}markQueriableHeapObjects(){const e=this.nodeFlags.canBeQueried,t=this.edgeHiddenType,s=this.edgeInternalType,n=this.edgeInvisibleType,i=this.edgeWeakType,o=this.edgeToNodeOffset,r=this.edgeTypeOffset,a=this.edgeFieldsCount,d=this.containmentEdges,h=this.nodeFieldCount,l=this.firstEdgeIndexes,c=this.flags,u=[];for(let e=this.rootNode().edges();e.hasNext();e.next())e.edge.node().isUserRoot()&&u.push(e.edge.node().nodeIndex/h);for(;u.length;){const g=u.pop();if(c[g]&e)continue;c[g]|=e;const p=l[g],f=l[g+1];for(let l=p;l<f;l+=a){const a=d.getValue(l+o)/h;if(c[a]&e)continue;const g=d.getValue(l+r);g!==t&&g!==n&&g!==s&&g!==i&&u.push(a)}}}markPageOwnedNodes(){const e=this.edgeShortcutType,t=this.edgeElementType,s=this.edgeToNodeOffset,n=this.edgeTypeOffset,i=this.edgeFieldsCount,o=this.edgeWeakType,r=this.firstEdgeIndexes,a=this.containmentEdges,d=this.nodeFieldCount,h=this.nodeCount,l=this.flags,c=this.nodeFlags.pageObject,u=new Uint32Array(h);let g=0;const p=this.rootNodeIndexInternal/d,f=this.rootNode();for(let o=r[p],h=r[p+1];o<h;o+=i){const i=a.getValue(o+n),r=a.getValue(o+s);if(i===t){if(f.nodeIndex=r,!f.isDocumentDOMTreesRoot())continue}else if(i!==e)continue;const h=r/d;u[g++]=h,l[h]|=c}for(;g;){const e=u[--g],t=r[e],h=r[e+1];for(let e=t;e<h;e+=i){const t=a.getValue(e+s)/d;if(l[t]&c)continue;a.getValue(e+n)!==o&&(u[g++]=t,l[t]|=c)}}}calculateStatistics(){const t=this.nodeFieldCount,s=this.nodes,n=s.length,i=this.nodeTypeOffset,o=this.nodeSelfSizeOffset,r=this.nodeNativeType,a=this.nodeCodeType,d=this.nodeConsStringType,h=this.nodeSlicedStringType,l=this.nodeDistances;let c=0,u=0,g=0,p=0,f=0;const I=this.rootNode();for(let m=0;m<n;m+=t){const n=s.getValue(m+o);if(l[m/t]>=e.HeapSnapshotModel.baseSystemDistance){f+=n;continue}const x=s.getValue(m+i);I.nodeIndex=m,x===r?c+=n:x===a?u+=n:x===d||x===h||"string"===I.type()?g+=n:"Array"===I.name()&&(p+=this.calculateArraySize(I))}this.#ce=new e.HeapSnapshotModel.Statistics,this.#ce.total=this.totalSize,this.#ce.v8heap=this.totalSize-c,this.#ce.native=c,this.#ce.code=u,this.#ce.jsArrays=p,this.#ce.strings=g,this.#ce.system=f}calculateArraySize(e){let t=e.selfSize();const s=e.edgeIndexesStart(),n=e.edgeIndexesEnd(),i=this.containmentEdges,o=this.strings,r=this.edgeToNodeOffset,a=this.edgeTypeOffset,d=this.edgeNameOffset,h=this.edgeFieldsCount,l=this.edgeInternalType;for(let c=s;c<n;c+=h){if(i.getValue(c+a)!==l)continue;if("elements"!==o[i.getValue(c+d)])continue;const s=i.getValue(c+r);e.nodeIndex=s,1===e.retainersCount()&&(t+=e.selfSize());break}return t}getStatistics(){return this.#ce}}class b extends I{constructor(e,t){super(e,t)}canBeQueried(){const e=this.snapshot,t=e.flagsOfNode(this);return Boolean(t&e.nodeFlags.canBeQueried)}rawName(){return super.name()}name(){const e=this.snapshot;if(this.rawType()===e.nodeConsStringType){let t=e.lazyStringCache[this.nodeIndex];return void 0===t&&(t=this.consStringName(),e.lazyStringCache[this.nodeIndex]=t),t}return this.rawName()}consStringName(){const e=this.snapshot,t=e.nodeConsStringType,s=e.edgeInternalType,n=e.edgeFieldsCount,i=e.edgeToNodeOffset,o=e.edgeTypeOffset,r=e.edgeNameOffset,a=e.strings,d=e.containmentEdges,h=e.firstEdgeIndexes,l=e.nodeFieldCount,c=e.nodeTypeOffset,u=e.nodeNameOffset,g=e.nodes,p=[];p.push(this.nodeIndex);let f="";for(;p.length&&f.length<1024;){const e=p.pop();if(g.getValue(e+c)!==t){f+=a[g.getValue(e+u)];continue}const I=e/l,m=h[I],x=h[I+1];let N=0,S=0;for(let e=m;e<x&&(!N||!S);e+=n){if(d.getValue(e+o)===s){const t=a[d.getValue(e+r)];"first"===t?N=d.getValue(e+i):"second"===t&&(S=d.getValue(e+i))}}p.push(S),p.push(N)}return f}className(){const e=this.type();switch(e){case"hidden":return"(system)";case"object":case"native":{let e=this.name();if(e.startsWith("<")){const t=e.indexOf(" ");-1!==t&&(e=e.substring(0,t)+">")}else if(e.startsWith("Detached <")){const t=e.indexOf(" ",10);-1!==t&&(e=e.substring(0,t)+">")}return e}case"code":return"(compiled code)";case"closure":return"Function";case"regexp":return"RegExp";default:return"("+e+")"}}classIndex(){const e=this.snapshot,t=e.nodes,s=t.getValue(this.nodeIndex+e.nodeTypeOffset);if(s===e.nodeObjectType||s===e.nodeNativeType){const s=this.name();return s.startsWith("<")||s.startsWith("Detached <")?e.getIndexForSyntheticClassName(this.className()):t.getValue(this.nodeIndex+e.nodeNameOffset)}return-1-s}id(){const e=this.snapshot;return e.nodes.getValue(this.nodeIndex+e.nodeIdOffset)}isHidden(){return this.rawType()===this.snapshot.nodeHiddenType}isArray(){return this.rawType()===this.snapshot.nodeArrayType}isSynthetic(){return this.rawType()===this.snapshot.nodeSyntheticType}isUserRoot(){return!this.isSynthetic()}isDocumentDOMTreesRoot(){return this.isSynthetic()&&"(Document DOM trees)"===this.name()}serialize(){const e=super.serialize(),t=this.snapshot,s=t.flagsOfNode(this);return s&t.nodeFlags.canBeQueried&&(e.canBeQueried=!0),s&t.nodeFlags.detachedDOMTreeNode&&(e.detachedDOMTreeNode=!0),e}}class v extends h{constructor(e,t){super(e,t)}clone(){const e=this.snapshot;return new v(e,this.edgeIndex)}hasStringName(){return this.isShortcut()?isNaN(parseInt(this.nameInternal(),10)):this.hasStringNameInternal()}isElement(){return this.rawType()===this.snapshot.edgeElementType}isHidden(){return this.rawType()===this.snapshot.edgeHiddenType}isWeak(){return this.rawType()===this.snapshot.edgeWeakType}isInternal(){return this.rawType()===this.snapshot.edgeInternalType}isInvisible(){return this.rawType()===this.snapshot.edgeInvisibleType}isShortcut(){return this.rawType()===this.snapshot.edgeShortcutType}name(){const e=this.nameInternal();if(!this.isShortcut())return String(e);const t=parseInt(e,10);return String(isNaN(t)?e:t)}toString(){const e=this.name();switch(this.type()){case"context":return"->"+e;case"element":return"["+e+"]";case"weak":return"[["+e+"]]";case"property":return-1===e.indexOf(" ")?"."+e:'["'+e+'"]';case"shortcut":return"string"==typeof e?-1===e.indexOf(" ")?"."+e:'["'+e+'"]':"["+e+"]";case"internal":case"hidden":case"invisible":return"{"+e+"}"}return"?"+e+"?"}hasStringNameInternal(){const e=this.rawType(),t=this.snapshot;return e!==t.edgeElementType&&e!==t.edgeHiddenType}nameInternal(){return this.hasStringNameInternal()?this.snapshot.strings[this.nameOrIndex()]:this.nameOrIndex()}nameOrIndex(){return this.edges.getValue(this.edgeIndex+this.snapshot.edgeNameOffset)}rawType(){return this.edges.getValue(this.edgeIndex+this.snapshot.edgeTypeOffset)}nameIndex(){if(!this.hasStringNameInternal())throw new Error("Edge does not have string name");return this.nameOrIndex()}}class A extends p{constructor(e,t){super(e,t)}clone(){const e=this.snapshot;return new A(e,this.retainerIndex())}isHidden(){return this.edge().isHidden()}isInvisible(){return this.edge().isInvisible()}isShortcut(){return this.edge().isShortcut()}isWeak(){return this.edge().isWeak()}}var z=Object.freeze({__proto__:null,HeapSnapshotEdge:h,HeapSnapshotNodeIndexProvider:l,HeapSnapshotEdgeIndexProvider:c,HeapSnapshotRetainerEdgeIndexProvider:u,HeapSnapshotEdgeIterator:g,HeapSnapshotRetainerEdge:p,HeapSnapshotRetainerEdgeIterator:f,HeapSnapshotNode:I,HeapSnapshotNodeIterator:m,HeapSnapshotIndexRangeIterator:x,HeapSnapshotFilteredIterator:N,HeapSnapshotProgress:S,HeapSnapshotProblemReport:y,HeapSnapshot:w,HeapSnapshotHeader:class{title;meta;node_count;edge_count;trace_function_count;root_index;constructor(){this.title="",this.meta=new T,this.node_count=0,this.edge_count=0,this.trace_function_count=0,this.root_index=0}},HeapSnapshotItemProvider:O,HeapSnapshotEdgesProvider:C,HeapSnapshotNodesProvider:E,JSHeapSnapshot:F,JSHeapSnapshotNode:b,JSHeapSnapshotEdge:v,JSHeapSnapshotRetainerEdge:A});class _{#H;#ge;#pe;#fe;#Ie;#me;#xe;#Ne="";constructor(e){this.#Se(),this.#H=new S(e),this.#ge=[],this.#pe=null,this.#fe=!1,this.#ye()}dispose(){this.#Se()}#Se(){this.#Ne="",this.#Ie=void 0}close(){this.#fe=!0,this.#pe&&this.#pe("")}buildSnapshot(){this.#Ie=this.#Ie||{},this.#H.updateStatus("Processing snapshot…");const e=new F(this.#Ie,this.#H);return this.#Se(),e}#we(){let e=0;const t="0".charCodeAt(0),s="9".charCodeAt(0),n="]".charCodeAt(0),i=this.#Ne.length;for(;;){for(;e<i;){const i=this.#Ne.charCodeAt(e);if(t<=i&&i<=s)break;if(i===n)return this.#Ne=this.#Ne.slice(e+1),!1;++e}if(e===i)return this.#Ne="",!0;let o=0;const r=e;for(;e<i;){const n=this.#Ne.charCodeAt(e);if(t>n||n>s)break;o*=10,o+=n-t,++e}if(e===i)return this.#Ne=this.#Ne.slice(r),!0;if(!this.#me)throw new Error("Array not instantiated");this.#me.setValue(this.#xe++,o)}}#Te(){this.#H.updateStatus("Parsing strings…");const e=this.#Ne.lastIndexOf("]");if(-1===e)throw new Error("Incomplete JSON");if(this.#Ne=this.#Ne.slice(0,e+1),!this.#Ie)throw new Error("No snapshot in parseStringsArray");this.#Ie.strings=JSON.parse(this.#Ne)}write(e){this.#ge.push(e),this.#pe&&(this.#pe(this.#ge.shift()),this.#pe=null)}#Oe(){if(this.#ge.length>0)return Promise.resolve(this.#ge.shift());const{promise:e,resolve:t}=s.PromiseUtilities.promiseWithResolvers();return this.#pe=t,e}async#Ce(e,t){for(;;){const s=this.#Ne.indexOf(e,t||0);if(-1!==s)return s;t=this.#Ne.length-e.length+1,this.#Ne+=await this.#Oe()}}async#Ee(e,t,n){const i=await this.#Ce(e),o=await this.#Ce("[",i);for(this.#Ne=this.#Ne.slice(o+1),this.#me=void 0===n?s.TypedArrayUtilities.createExpandableBigUint32Array():s.TypedArrayUtilities.createFixedBigUint32Array(n),this.#xe=0;this.#we();)n?this.#H.updateProgress(t,this.#xe,this.#me.length):this.#H.updateStatus(t),this.#Ne+=await this.#Oe();const r=this.#me;return this.#me=null,r}async#ye(){const e='"snapshot"',t=await this.#Ce(e);if(-1===t)throw new Error("Snapshot token not found");this.#H.updateStatus("Loading snapshot info…");const s=this.#Ne.slice(t+10+1);let i=!1;const o=new n.TextUtils.BalancedJSONTokenizer((e=>{this.#Ne=o.remainder(),i=!0,this.#Ie=this.#Ie||{},this.#Ie.snapshot=JSON.parse(e)}));for(o.write(s);!i;)o.write(await this.#Oe());this.#Ie=this.#Ie||{};const r=await this.#Ee('"nodes"',"Loading nodes… {PH1}%",this.#Ie.snapshot.meta.node_fields.length*this.#Ie.snapshot.node_count);this.#Ie.nodes=r;const a=await this.#Ee('"edges"',"Loading edges… {PH1}%",this.#Ie.snapshot.meta.edge_fields.length*this.#Ie.snapshot.edge_count);if(this.#Ie.edges=a,this.#Ie.snapshot.trace_function_count){const e=await this.#Ee('"trace_function_infos"',"Loading allocation traces… {PH1}%",this.#Ie.snapshot.meta.trace_function_info_fields.length*this.#Ie.snapshot.trace_function_count);this.#Ie.trace_function_infos=e.asUint32ArrayOrFail();const t=await this.#Ce(":"),s=await this.#Ce('"',t),n=this.#Ne.indexOf("["),i=this.#Ne.lastIndexOf("]",s);this.#Ie.trace_tree=JSON.parse(this.#Ne.substring(n,i+1)),this.#Ne=this.#Ne.slice(i+1)}if(this.#Ie.snapshot.meta.sample_fields){const e=await this.#Ee('"samples"',"Loading samples…");this.#Ie.samples=e.asArrayOrFail()}if(this.#Ie.snapshot.meta.location_fields){const e=await this.#Ee('"locations"',"Loading locations…");this.#Ie.locations=e.asArrayOrFail()}else this.#Ie.locations=[];this.#H.updateStatus("Loading strings…");const d=await this.#Ce('"strings"'),h=await this.#Ce("[",d);for(this.#Ne=this.#Ne.slice(h);!this.#fe;)this.#Ne+=await this.#Oe();this.#Te()}}var D=Object.freeze({__proto__:null,HeapSnapshotLoader:_});var R=Object.freeze({__proto__:null,HeapSnapshotWorkerDispatcher:class{#Fe;#be;constructor(e){this.#Fe=[],this.#be=e}sendEvent(e,t){this.#be({eventName:e,data:t})}dispatchMessage({data:t}){const s={callId:t.callId,result:null,error:void 0,errorCallStack:void 0,errorMethodName:void 0};try{switch(t.disposition){case"createLoader":this.#Fe[t.objectId]=new _(this);break;case"dispose":delete this.#Fe[t.objectId];break;case"getter":{const e=this.#Fe[t.objectId][t.methodName];s.result=e;break}case"factory":{const e=this.#Fe[t.objectId],n=e[t.methodName].apply(e,t.methodArguments);n&&(this.#Fe[t.newObjectId]=n),s.result=Boolean(n);break}case"method":{const e=this.#Fe[t.objectId];s.result=e[t.methodName].apply(e,t.methodArguments);break}case"evaluateForTest":try{globalThis.HeapSnapshotWorker={AllocationProfile:d,HeapSnapshot:z,HeapSnapshotLoader:D},globalThis.HeapSnapshotModel=e,s.result=self.eval(t.source)}catch(e){s.result=e.toString()}}}catch(e){s.error=e.toString(),s.errorCallStack=e.stack,t.methodName&&(s.errorMethodName=t.methodName)}this.#be(s)}}});export{d as AllocationProfile,z as HeapSnapshot,D as HeapSnapshotLoader,R as HeapSnapshotWorkerDispatcher};
