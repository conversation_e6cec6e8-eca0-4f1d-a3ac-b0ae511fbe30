import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import"../../core/root/root.js";import*as o from"../../ui/legacy/legacy.js";const i={rendering:"Rendering",showRendering:"Show Rendering",paint:"paint",layout:"layout",fps:"fps",cssMediaType:"CSS media type",cssMediaFeature:"CSS media feature",visionDeficiency:"vision deficiency",colorVisionDeficiency:"color vision deficiency",reloadPage:"Reload page",hardReloadPage:"Hard reload page",forceAdBlocking:"Force ad blocking on this site",blockAds:"Block ads on this site",showAds:"Show ads on this site, if allowed",autoOpenDevTools:"Auto-open DevTools for popups",doNotAutoOpen:"Do not auto-open DevTools for popups",disablePaused:"Disable paused state overlay",toggleCssPrefersColorSchemeMedia:"Toggle CSS media feature prefers-color-scheme"},a=t.i18n.registerUIStrings("entrypoints/inspector_main/inspector_main-meta.ts",i),n=t.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let r;async function s(){return r||(r=await import("./inspector_main.js")),r}o.ViewManager.registerViewExtension({location:"drawer-view",id:"rendering",title:n(i.rendering),commandPrompt:n(i.showRendering),persistence:"closeable",order:50,loadView:async()=>new((await s()).RenderingOptions.RenderingOptionsView),tags:[n(i.paint),n(i.layout),n(i.fps),n(i.cssMediaType),n(i.cssMediaFeature),n(i.visionDeficiency),n(i.colorVisionDeficiency)]}),o.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.reload",loadActionDelegate:async()=>new((await s()).InspectorMain.ReloadActionDelegate),iconClass:"refresh",title:n(i.reloadPage),bindings:[{platform:"windows,linux",shortcut:"Ctrl+R"},{platform:"windows,linux",shortcut:"F5"},{platform:"mac",shortcut:"Meta+R"}]}),o.ActionRegistration.registerActionExtension({category:"NAVIGATION",actionId:"inspector-main.hard-reload",loadActionDelegate:async()=>new((await s()).InspectorMain.ReloadActionDelegate),title:n(i.hardReloadPage),bindings:[{platform:"windows,linux",shortcut:"Shift+Ctrl+R"},{platform:"windows,linux",shortcut:"Shift+F5"},{platform:"windows,linux",shortcut:"Ctrl+F5"},{platform:"windows,linux",shortcut:"Ctrl+Shift+F5"},{platform:"mac",shortcut:"Shift+Meta+R"}]}),o.ActionRegistration.registerActionExtension({actionId:"rendering.toggle-prefers-color-scheme",category:"RENDERING",title:n(i.toggleCssPrefersColorSchemeMedia),loadActionDelegate:async()=>new((await s()).RenderingOptions.ReloadActionDelegate)}),e.Settings.registerSettingExtension({category:"NETWORK",title:n(i.forceAdBlocking),settingName:"network.ad-blocking-enabled",settingType:"boolean",storageType:"Session",defaultValue:!1,options:[{value:!0,title:n(i.blockAds)},{value:!1,title:n(i.showAds)}]}),e.Settings.registerSettingExtension({category:"GLOBAL",storageType:"Synced",title:n(i.autoOpenDevTools),settingName:"auto-attach-to-created-pages",settingType:"boolean",order:2,defaultValue:!1,options:[{value:!0,title:n(i.autoOpenDevTools)},{value:!1,title:n(i.doNotAutoOpen)}]}),e.Settings.registerSettingExtension({category:"APPEARANCE",storageType:"Synced",title:n(i.disablePaused),settingName:"disable-paused-state-overlay",settingType:"boolean",defaultValue:!1}),o.Toolbar.registerToolbarItem({loadItem:async()=>(await s()).InspectorMain.NodeIndicator.instance(),order:2,location:"main-toolbar-left"}),o.Toolbar.registerToolbarItem({loadItem:async()=>(await s()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",experiment:"outermost-target-selector"}),o.Toolbar.registerToolbarItem({loadItem:async()=>(await s()).OutermostTargetSelector.OutermostTargetSelector.instance(),order:98,location:"main-toolbar-right",showLabel:void 0,condition:void 0,separator:void 0,actionId:void 0,experiment:"outermost-target-selector"});
