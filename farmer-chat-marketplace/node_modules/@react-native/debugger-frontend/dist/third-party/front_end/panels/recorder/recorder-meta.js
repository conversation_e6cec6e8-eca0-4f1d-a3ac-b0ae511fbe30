import*as e from"../../core/i18n/i18n.js";import*as r from"../../ui/legacy/legacy.js";const o={recorder:"Recorder",showRecorder:"Show Recorder",startStopRecording:"Start/Stop recording",createRecording:"Create a new recording",replayRecording:"Replay recording",toggleCode:"Toggle code view"},t=e.i18n.registerUIStrings("panels/recorder/recorder-meta.ts",o),n=e.i18n.getLazilyComputedLocalizedString.bind(void 0,t);let c;async function i(){return c||(c=await import("./recorder.js")),c}function a(e,r){return void 0===c?[]:r&&c.RecorderPanel.RecorderPanel.instance().isActionPossible(r)?e(c):[]}const d="chrome-recorder";r.ViewManager.defaultOptionsForTabs[d]=!0,r.ViewManager.registerViewExtension({location:"panel",id:d,commandPrompt:n(o.showRecorder),title:n(o.recorder),order:90,persistence:"closeable",loadView:async()=>(await i()).RecorderPanel.RecorderPanel.instance()}),r.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.create-recording",title:n(o.createRecording),loadActionDelegate:async()=>new((await i()).RecorderPanel.ActionDelegate)}),r.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.start-recording",title:n(o.startStopRecording),contextTypes:()=>a((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.start-recording"),loadActionDelegate:async()=>new((await i()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+E",platform:"windows,linux"},{shortcut:"Meta+E",platform:"mac"}]}),r.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.replay-recording",title:n(o.replayRecording),contextTypes:()=>a((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.replay-recording"),loadActionDelegate:async()=>new((await i()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+Enter",platform:"windows,linux"},{shortcut:"Meta+Enter",platform:"mac"}]}),r.ActionRegistration.registerActionExtension({category:"RECORDER",actionId:"chrome-recorder.toggle-code-view",title:n(o.toggleCode),contextTypes:()=>a((e=>[e.RecorderPanel.RecorderPanel]),"chrome-recorder.toggle-code-view"),loadActionDelegate:async()=>new((await i()).RecorderPanel.ActionDelegate),bindings:[{shortcut:"Ctrl+B",platform:"windows,linux"},{shortcut:"Meta+B",platform:"mac"}]});
