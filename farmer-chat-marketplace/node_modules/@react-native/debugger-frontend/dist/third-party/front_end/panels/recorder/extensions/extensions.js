import*as e from"../../../core/common/common.js";import*as i from"../../../models/extensions/extensions.js";let n=null;class t extends e.ObjectWrapper.ObjectWrapper{static instance(){return n||(n=new t),n}#e=new Map;constructor(){super(),this.attach()}attach(){const e=i.RecorderPluginManager.RecorderPluginManager.instance();e.addEventListener("pluginAdded",this.#i),e.addEventListener("pluginRemoved",this.#i),e.addEventListener("viewRegistered",this.#n);for(const i of e.views())this.#n({data:i})}detach(){const e=i.RecorderPluginManager.RecorderPluginManager.instance();e.removeEventListener("pluginAdded",this.#i),e.removeEventListener("pluginRemoved",this.#i),e.removeEventListener("viewRegistered",this.#n),this.#e.clear()}extensions(){return i.RecorderPluginManager.RecorderPluginManager.instance().plugins()}getView(e){const i=this.#e.get(e);if(!i)throw new Error("View not found");return i}#i=()=>{this.dispatchEventToListeners("extensionsUpdated",this.extensions())};#n=e=>{const i=e.data;this.#e.has(i.id)||this.#e.set(i.id,new s(i))}}class s{#t;#s;#r=!1;#o=!1;constructor(e){this.#t=e,this.#s=document.createElement("iframe"),this.#s.src=e.pagePath,this.#s.onload=this.#a}#a=()=>{this.#o=!0,this.#r&&this.#t.onShown()};show(){this.#r||(this.#r=!0,this.#o&&this.#t.onShown())}hide(){this.#r&&(this.#r=!1,this.#o=!1,this.#t.onHidden())}frame(){return this.#s}}var r=Object.freeze({__proto__:null,ExtensionManager:t});export{r as ExtensionManager};
