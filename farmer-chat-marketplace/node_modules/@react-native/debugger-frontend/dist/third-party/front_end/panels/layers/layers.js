import*as e from"../../ui/legacy/legacy.js";import*as t from"../../ui/visual_logging/visual_logging.js";import*as r from"../layer_viewer/layer_viewer.js";import*as i from"../../core/common/common.js";import*as a from"../../core/i18n/i18n.js";import*as s from"../../core/sdk/sdk.js";class n extends e.SplitWidget.SplitWidget{logTreeView;paintProfilerView;constructor(e){super(!0,!1),this.element.setAttribute("jslog",`${t.pane("layers.paint-profiler").track({resize:!0})}`),this.logTreeView=new r.PaintProfilerView.PaintProfilerCommandLogView,this.setSidebarWidget(this.logTreeView),this.paintProfilerView=new r.PaintProfilerView.PaintProfilerView(e),this.setMainWidget(this.paintProfilerView),this.paintProfilerView.addEventListener("WindowChanged",this.onWindowChanged,this),this.logTreeView.focus()}reset(){this.paintProfilerView.setSnapshotAndLog(null,[],null)}profile(e){function t(e,t){this.logTreeView.setCommandLog(t||[]),this.paintProfilerView.setSnapshotAndLog(e,t||[],null),e&&e.release()}e.commandLog().then((r=>t.call(this,e,r)))}setScale(e){this.paintProfilerView.setScale(e)}onWindowChanged(){this.logTreeView.updateWindow(this.paintProfilerView.selectionWindow())}}var l,o=Object.freeze({__proto__:null,LayerPaintProfilerView:n});class d extends s.SDKModel.SDKModel{layerTreeAgent;paintProfilerModel;layerTreeInternal;throttler;enabled;lastPaintRectByLayerId;constructor(e){super(e),this.layerTreeAgent=e.layerTreeAgent(),e.registerLayerTreeDispatcher(new c(this)),this.paintProfilerModel=e.model(s.PaintProfiler.PaintProfilerModel);const t=e.model(s.ResourceTreeModel.ResourceTreeModel);t&&t.addEventListener(s.ResourceTreeModel.Events.PrimaryPageChanged,this.onPrimaryPageChanged,this),this.layerTreeInternal=null,this.throttler=new i.Throttler.Throttler(20)}async disable(){this.enabled&&(this.enabled=!1,await this.layerTreeAgent.invoke_disable())}enable(){this.enabled||(this.enabled=!0,this.forceEnable())}async forceEnable(){this.lastPaintRectByLayerId=new Map,this.layerTreeInternal||(this.layerTreeInternal=new h(this)),await this.layerTreeAgent.invoke_enable()}layerTree(){return this.layerTreeInternal}async layerTreeChanged(e){this.enabled&&this.throttler.schedule(this.innerSetLayers.bind(this,e))}async innerSetLayers(e){const t=this.layerTreeInternal;await t.setLayers(e),this.lastPaintRectByLayerId||(this.lastPaintRectByLayerId=new Map);for(const e of this.lastPaintRectByLayerId.keys()){const r=this.lastPaintRectByLayerId.get(e),i=t.layerById(e);i&&i.setLastPaintRect(r)}this.lastPaintRectByLayerId=new Map,this.dispatchEventToListeners(l.LayerTreeChanged)}layerPainted(e,t){if(!this.enabled)return;const r=this.layerTreeInternal.layerById(e);if(!r)return this.lastPaintRectByLayerId||(this.lastPaintRectByLayerId=new Map),void this.lastPaintRectByLayerId.set(e,t);r.didPaint(t),this.dispatchEventToListeners(l.LayerPainted,r)}onPrimaryPageChanged(){this.layerTreeInternal=null,this.enabled&&this.forceEnable()}}s.SDKModel.SDKModel.register(d,{capabilities:2,autostart:!1}),function(e){e.LayerTreeChanged="LayerTreeChanged",e.LayerPainted="LayerPainted"}(l||(l={}));class h extends s.LayerTreeBase.LayerTreeBase{layerTreeModel;constructor(e){super(e.target()),this.layerTreeModel=e}async setLayers(e){if(!e)return void this.innerSetLayers(e);const t=new Set;for(let r=0;r<e.length;++r){const i=e[r].backendNodeId;i&&!this.backendNodeIdToNode().has(i)&&t.add(i)}await this.resolveBackendNodeIds(t),this.innerSetLayers(e)}innerSetLayers(e){if(this.setRoot(null),this.setContentRoot(null),!e)return;let t;const r=this.layersById;this.layersById=new Map;for(let i=0;i<e.length;++i){const a=e[i].layerId;let s=r.get(a);s?s.reset(e[i]):s=new y(this.layerTreeModel,e[i]),this.layersById.set(a,s);const n=e[i].backendNodeId;n&&s.setNode(this.backendNodeIdToNode().get(n)||null),!this.contentRoot()&&s.drawsContent()&&this.setContentRoot(s);const l=s.parentId();if(l){const e=this.layersById.get(l);if(!e)throw new Error(`Missing parent ${l} for layer ${a}`);e.addChild(s)}else t&&console.assert(!1,"Multiple root layers"),t=s}t&&(this.setRoot(t),t.calculateQuad(new WebKitCSSMatrix))}}class y{scrollRectsInternal;quadInternal;childrenInternal;image;parentInternal;layerPayload;layerTreeModel;nodeInternal;lastPaintRectInternal;paintCountInternal;stickyPositionConstraintInternal;constructor(e,t){this.layerTreeModel=e,this.reset(t)}id(){return this.layerPayload.layerId}parentId(){return this.layerPayload.parentLayerId||null}parent(){return this.parentInternal}isRoot(){return!this.parentId()}children(){return this.childrenInternal}addChild(e){const t=e;t.parentInternal&&console.assert(!1,"Child already has a parent"),this.childrenInternal.push(t),t.parentInternal=this}setNode(e){this.nodeInternal=e}node(){return this.nodeInternal||null}nodeForSelfOrAncestor(){let e=this;for(;e;e=e.parentInternal)if(e.nodeInternal)return e.nodeInternal;return null}offsetX(){return this.layerPayload.offsetX}offsetY(){return this.layerPayload.offsetY}width(){return this.layerPayload.width}height(){return this.layerPayload.height}transform(){return this.layerPayload.transform||null}quad(){return this.quadInternal}anchorPoint(){return[this.layerPayload.anchorX||0,this.layerPayload.anchorY||0,this.layerPayload.anchorZ||0]}invisible(){return this.layerPayload.invisible||!1}paintCount(){return this.paintCountInternal||this.layerPayload.paintCount}lastPaintRect(){return this.lastPaintRectInternal||null}setLastPaintRect(e){this.lastPaintRectInternal=e}scrollRects(){return this.scrollRectsInternal}stickyPositionConstraint(){return this.stickyPositionConstraintInternal||null}async requestCompositingReasons(){return(await this.layerTreeModel.layerTreeAgent.invoke_compositingReasons({layerId:this.id()})).compositingReasons||[]}async requestCompositingReasonIds(){return(await this.layerTreeModel.layerTreeAgent.invoke_compositingReasons({layerId:this.id()})).compositingReasonIds||[]}drawsContent(){return this.layerPayload.drawsContent}gpuMemoryUsage(){return this.drawsContent()?this.width()*this.height()*4:0}snapshots(){return[this.layerTreeModel.paintProfilerModel.makeSnapshot(this.id()).then((e=>e?{rect:{x:0,y:0,width:this.width(),height:this.height()},snapshot:e}:null))]}didPaint(e){this.lastPaintRectInternal=e,this.paintCountInternal=this.paintCount()+1,this.image=null}reset(e){this.nodeInternal=null,this.childrenInternal=[],this.parentInternal=null,this.paintCountInternal=0,this.layerPayload=e,this.image=null,this.scrollRectsInternal=this.layerPayload.scrollRects||[],this.stickyPositionConstraintInternal=this.layerPayload.stickyPositionConstraint?new s.LayerTreeBase.StickyPositionConstraint(this.layerTreeModel.layerTree(),this.layerPayload.stickyPositionConstraint):null}matrixFromArray(e){return new WebKitCSSMatrix("matrix3d("+e.map((function(e){return e.toFixed(9)})).join(",")+")")}calculateTransformToViewport(t){let r=(new WebKitCSSMatrix).translate(this.layerPayload.offsetX,this.layerPayload.offsetY);if(this.layerPayload.transform){const t=this.matrixFromArray(this.layerPayload.transform),i=new e.Geometry.Vector(this.layerPayload.width*this.anchorPoint()[0],this.layerPayload.height*this.anchorPoint()[1],this.anchorPoint()[2]),a=e.Geometry.multiplyVectorByMatrixAndNormalize(i,r),s=(new WebKitCSSMatrix).translate(-a.x,-a.y,-a.z);r=s.inverse().multiply(t.multiply(s.multiply(r)))}return r=t.multiply(r),r}createVertexArrayForRect(e,t){return[0,0,0,e,0,0,e,t,0,0,t,0]}calculateQuad(t){const r=this.calculateTransformToViewport(t);this.quadInternal=[];const i=this.createVertexArrayForRect(this.layerPayload.width,this.layerPayload.height);for(let t=0;t<4;++t){const a=e.Geometry.multiplyVectorByMatrixAndNormalize(new e.Geometry.Vector(i[3*t],i[3*t+1],i[3*t+2]),r);this.quadInternal.push(a.x,a.y)}this.childrenInternal.forEach((function(e){e.calculateQuad(r)}))}}class c{layerTreeModel;constructor(e){this.layerTreeModel=e}layerTreeDidChange({layers:e}){this.layerTreeModel.layerTreeChanged(e||null)}layerPainted({layerId:e,clip:t}){this.layerTreeModel.layerPainted(e,t)}}var u=Object.freeze({__proto__:null,LayerTreeModel:d,get Events(){return l},AgentLayerTree:h,AgentLayer:y});const P={details:"Details",profiler:"Profiler"},p=a.i18n.registerUIStrings("panels/layers/LayersPanel.ts",P),g=a.i18n.getLocalizedString.bind(void 0,p);let w;class f extends e.Panel.PanelWithSidebar{model;layerViewHost;layerTreeOutline;rightSplitWidget;layers3DView;tabbedPane;layerDetailsView;paintProfilerView;updateThrottler;layerBeingProfiled;constructor(){super("layers",225),this.model=null,s.TargetManager.TargetManager.instance().observeTargets(this,{scoped:!0}),this.layerViewHost=new r.LayerViewHost.LayerViewHost,this.layerTreeOutline=new r.LayerTreeOutline.LayerTreeOutline(this.layerViewHost),this.layerTreeOutline.addEventListener("PaintProfilerRequested",this.onPaintProfileRequested,this),this.panelSidebarElement().appendChild(this.layerTreeOutline.element),this.setDefaultFocusedElement(this.layerTreeOutline.element),this.rightSplitWidget=new e.SplitWidget.SplitWidget(!1,!0,"layer-details-split-view-state"),this.splitWidget().setMainWidget(this.rightSplitWidget),this.layers3DView=new r.Layers3DView.Layers3DView(this.layerViewHost),this.rightSplitWidget.setMainWidget(this.layers3DView),this.layers3DView.addEventListener("PaintProfilerRequested",this.onPaintProfileRequested,this),this.layers3DView.addEventListener("ScaleChanged",this.onScaleChanged,this),this.tabbedPane=new e.TabbedPane.TabbedPane,this.rightSplitWidget.setSidebarWidget(this.tabbedPane),this.layerDetailsView=new r.LayerDetailsView.LayerDetailsView(this.layerViewHost),this.layerDetailsView.addEventListener("PaintProfilerRequested",this.onPaintProfileRequested,this),this.tabbedPane.appendTab(m.Details,g(P.details),this.layerDetailsView),this.paintProfilerView=new n(this.showImage.bind(this)),this.tabbedPane.addEventListener(e.TabbedPane.Events.TabClosed,this.onTabClosed,this),this.updateThrottler=new i.Throttler.Throttler(100)}static instance(e){return w&&!e?.forceNew||(w=new f),w}focus(){this.layerTreeOutline.focus()}wasShown(){super.wasShown(),this.model&&this.model.enable()}willHide(){this.model&&this.model.disable(),super.willHide()}targetAdded(e){e===e.outermostTarget()&&(this.model=e.model(d),this.model&&(this.model.addEventListener(l.LayerTreeChanged,this.onLayerTreeUpdated,this),this.model.addEventListener(l.LayerPainted,this.onLayerPainted,this),this.isShowing()&&(this.model.enable(),this.update())))}targetRemoved(e){this.model&&this.model.target()===e&&(this.model.removeEventListener(l.LayerTreeChanged,this.onLayerTreeUpdated,this),this.model.removeEventListener(l.LayerPainted,this.onLayerPainted,this),this.model.disable(),this.model=null)}onLayerTreeUpdated(){this.updateThrottler.schedule(this.update.bind(this))}update(){if(this.model){this.layerViewHost.setLayerTree(this.model.layerTree());const e=this.model.target().model(s.ResourceTreeModel.ResourceTreeModel);if(e){const t=e.mainFrame;if(t){const e=t.url;this.element.setAttribute("test-current-url",e)}}}return Promise.resolve()}onLayerPainted({data:e}){if(!this.model)return;const t=this.layerViewHost.selection();t&&t.layer()===e&&this.layerDetailsView.update(),this.layers3DView.updateLayerSnapshot(e)}onPaintProfileRequested({data:e}){this.layers3DView.snapshotForSelection(e).then((t=>{t&&(this.layerBeingProfiled=e.layer(),this.tabbedPane.hasTab(m.Profiler)||this.tabbedPane.appendTab(m.Profiler,g(P.profiler),this.paintProfilerView,void 0,!0,!0),this.tabbedPane.selectTab(m.Profiler),this.paintProfilerView.profile(t.snapshot))}))}onTabClosed(e){e.data.tabId===m.Profiler&&this.layerBeingProfiled&&(this.paintProfilerView.reset(),this.layers3DView.showImageForLayer(this.layerBeingProfiled,void 0),this.layerBeingProfiled=null)}showImage(e){this.layerBeingProfiled&&this.layers3DView.showImageForLayer(this.layerBeingProfiled,e)}onScaleChanged(e){this.paintProfilerView.setScale(e.data)}}const m={Details:"details",Profiler:"profiler"};var T=Object.freeze({__proto__:null,LayersPanel:f,DetailsViewTabs:m});export{o as LayerPaintProfilerView,u as LayerTreeModel,T as LayersPanel};
