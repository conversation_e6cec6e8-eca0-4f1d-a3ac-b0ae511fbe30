import*as t from"../../core/i18n/i18n.js";import*as o from"../../ui/legacy/legacy.js";const i={resetView:"Reset view",switchToPanMode:"Switch to pan mode",switchToRotateMode:"Switch to rotate mode",zoomIn:"Zoom in",zoomOut:"Zoom out",panOrRotateUp:"Pan or rotate up",panOrRotateDown:"Pan or rotate down",panOrRotateLeft:"Pan or rotate left",panOrRotateRight:"Pan or rotate right"},e=t.i18n.registerUIStrings("panels/layer_viewer/layer_viewer-meta.ts",i),n=t.i18n.getLazilyComputedLocalizedString.bind(void 0,e);o.ActionRegistration.registerActionExtension({actionId:"layers.reset-view",category:"LAYERS",title:n(i.resetView),bindings:[{shortcut:"0"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.pan-mode",category:"LAYERS",title:n(i.switchToPanMode),bindings:[{shortcut:"x"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.rotate-mode",category:"LAYERS",title:n(i.switchToRotateMode),bindings:[{shortcut:"v"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.zoom-in",category:"LAYERS",title:n(i.zoomIn),bindings:[{shortcut:"Shift+Plus"},{shortcut:"NumpadPlus"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.zoom-out",category:"LAYERS",title:n(i.zoomOut),bindings:[{shortcut:"Shift+Minus"},{shortcut:"NumpadMinus"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.up",category:"LAYERS",title:n(i.panOrRotateUp),bindings:[{shortcut:"Up"},{shortcut:"w"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.down",category:"LAYERS",title:n(i.panOrRotateDown),bindings:[{shortcut:"Down"},{shortcut:"s"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.left",category:"LAYERS",title:n(i.panOrRotateLeft),bindings:[{shortcut:"Left"},{shortcut:"a"}]}),o.ActionRegistration.registerActionExtension({actionId:"layers.right",category:"LAYERS",title:n(i.panOrRotateRight),bindings:[{shortcut:"Right"},{shortcut:"d"}]});
