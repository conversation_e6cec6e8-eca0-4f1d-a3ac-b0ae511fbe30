import*as e from"../../core/common/common.js";import*as r from"../../core/i18n/i18n.js";import*as o from"../../core/sdk/sdk.js";import*as n from"../../ui/legacy/components/object_ui/object_ui.js";import*as t from"../../ui/legacy/legacy.js";const i={memoryInspector:"Memory inspector",showMemoryInspector:"Show Memory inspector"},a=r.i18n.registerUIStrings("panels/linear_memory_inspector/linear_memory_inspector-meta.ts",i),s=r.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let c;async function m(){return c||(c=await import("./linear_memory_inspector.js")),c}t.ViewManager.registerViewExtension({location:"drawer-view",id:"linear-memory-inspector",title:s(i.memoryInspector),commandPrompt:s(i.showMemoryInspector),order:100,persistence:"closeable",loadView:async()=>(await m()).LinearMemoryInspectorPane.LinearMemoryInspectorPane.instance()}),t.ContextMenu.registerProvider({loadProvider:async()=>(await m()).LinearMemoryInspectorController.LinearMemoryInspectorController.instance(),experiment:void 0,contextTypes:()=>[n.ObjectPropertiesSection.ObjectPropertyTreeElement]}),e.Revealer.registerRevealer({contextTypes:()=>[o.RemoteObject.LinearMemoryInspectable],destination:e.Revealer.RevealerDestination.MEMORY_INSPECTOR_PANEL,loadRevealer:async()=>(await m()).LinearMemoryInspectorController.LinearMemoryInspectorController.instance()});
