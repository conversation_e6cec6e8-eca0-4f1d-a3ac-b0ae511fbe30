import*as i from"../../core/i18n/i18n.js";import*as e from"../../ui/legacy/legacy.js";const o={webaudio:"WebAudio",audio:"audio",showWebaudio:"Show WebAudio"},a=i.i18n.registerUIStrings("panels/web_audio/web_audio-meta.ts",o),d=i.i18n.getLazilyComputedLocalizedString.bind(void 0,a);let t;e.ViewManager.registerViewExtension({location:"drawer-view",id:"web-audio",title:d(o.webaudio),commandPrompt:d(o.showWebaudio),persistence:"closeable",order:100,loadView:async()=>new((await async function(){return t||(t=await import("./web_audio.js")),t}()).WebAudioView.WebAudioView),tags:[d(o.audio)]});
