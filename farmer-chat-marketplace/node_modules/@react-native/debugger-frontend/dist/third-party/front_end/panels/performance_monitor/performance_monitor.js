import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as r from"../../core/i18n/i18n.js";import*as o from"../../core/platform/platform.js";import*as i from"../../core/sdk/sdk.js";import*as s from"../../ui/legacy/legacy.js";import*as n from"../../ui/legacy/theme_support/theme_support.js";import*as a from"../../ui/visual_logging/visual_logging.js";const l=new CSSStyleSheet;l.replaceSync(".perfmon-pane{overflow:hidden;--override-color-perf-monitor-cpu:var(--sys-color-yellow-bright);--override-color-perf-monitor-cpu-task-duration:var(--sys-color-neutral-bright);--override-color-perf-monitor-cpu-script-duration:var(--sys-color-yellow-bright);--override-color-perf-monitor-cpu-layout-duration:var(--sys-color-purple);--override-color-perf-monitor-cpu-recalc-style-duration:var(--sys-color-purple-bright);--override-color-perf-monitor-jsheap:var(--sys-color-purple-bright);--override-color-perf-monitor-jsheap-total-size:var(--ref-palette-purple70);--override-color-perf-monitor-jsheap-used-size:var(--sys-color-purple-bright);--override-color-perf-monitor-dom-nodes:var(--sys-color-green);--override-color-perf-monitor-js-event-listeners:var(--ref-palette-green80);--override-color-perf-monitor-documents:var(--sys-color-blue);--override-color-perf-monitor-document-frames:var(--sys-color-cyan-bright);--override-color-perf-monitor-layout-count:var(--sys-color-pink-bright);--override-color-perf-monitor-recalc-style-count:var(--sys-color-pink)}:host-context(.theme-with-dark-background) .perfmon-pane{--override-color-perf-monitor-jsheap-total-size:var(--ref-palette-purple50);--override-color-perf-monitor-js-event-listeners:var(--ref-palette-green50)}.perfmon-pane.suspended{opacity:40%;pointer-events:none}.perfmon-pane .perfmon-chart-suspend-overlay{display:none;font-size:26px;align-items:center;justify-content:center}.perfmon-pane.suspended .perfmon-chart-suspend-overlay{display:flex}.perfmon-control-pane{display:flex;flex-direction:column;padding:6px 0;overflow-x:hidden;overflow-y:auto}.perfmon-chart-container{display:flex;flex:1 1;border-left:1px solid var(--sys-color-divider);overflow-y:auto}.perfmon-chart-container canvas{width:100%}.perfmon-indicator{padding:3px 9px;margin:-1px 0;display:flex;flex-shrink:0;width:210px}.perfmon-indicator span{flex:0 0 135px}.perfmon-indicator-value{flex:0 0 55px;text-align:right;overflow:visible}.perfmon-indicator:not(.active) .perfmon-indicator-value{opacity:0%}\n/*# sourceURL=performanceMonitor.css */\n");const c={graphsDisplayingARealtimeViewOf:"Graphs displaying a real-time view of performance metrics",paused:"Paused",cpuUsage:"CPU usage",jsHeapSize:"JS heap size",domNodes:"DOM Nodes",jsEventListeners:"JS event listeners",documents:"Documents",documentFrames:"Document Frames",layoutsSec:"Layouts / sec",styleRecalcsSec:"Style recalcs / sec"},h=r.i18n.registerUIStrings("panels/performance_monitor/PerformanceMonitor.ts",c),m=r.i18n.getLocalizedString.bind(void 0,h);class d extends s.Widget.HBox{metricsBuffer;pixelsPerMs;pollIntervalMs;scaleHeight;graphHeight;gridColor;controlPane;canvas;animationId;width;height;model;startTimestamp;pollTimer;constructor(e=500){super(!0),this.element.setAttribute("jslog",`${a.panel("performance.monitor").track({resize:!0})}`),this.contentElement.classList.add("perfmon-pane"),this.metricsBuffer=[],this.pixelsPerMs=.01,this.pollIntervalMs=e,this.scaleHeight=16,this.graphHeight=90,this.gridColor=n.ThemeSupport.instance().getComputedValue("--divider-line"),this.controlPane=new p(this.contentElement);const t=this.contentElement.createChild("div","perfmon-chart-container");this.canvas=t.createChild("canvas"),this.canvas.tabIndex=-1,s.ARIAUtils.setLabel(this.canvas,m(c.graphsDisplayingARealtimeViewOf)),this.contentElement.createChild("div","perfmon-chart-suspend-overlay fill").createChild("div").textContent=m(c.paused),this.controlPane.addEventListener("MetricChanged",this.recalcChartHeight,this),i.TargetManager.TargetManager.instance().observeModels(i.PerformanceMetricsModel.PerformanceMetricsModel,this)}wasShown(){if(!this.model)return;this.registerCSSFiles([l]),this.controlPane.instantiateMetricData();n.ThemeSupport.instance().addEventListener(n.ThemeChangeEvent.eventName,(()=>{this.controlPane.instantiateMetricData(),this.draw()})),i.TargetManager.TargetManager.instance().addEventListener("SuspendStateChanged",this.suspendStateChanged,this),this.model.enable(),this.suspendStateChanged()}willHide(){this.model&&(i.TargetManager.TargetManager.instance().removeEventListener("SuspendStateChanged",this.suspendStateChanged,this),this.stopPolling(),this.model.disable())}modelAdded(e){e.target()===i.TargetManager.TargetManager.instance().primaryPageTarget()&&(this.model=e,this.isShowing()&&this.wasShown())}modelRemoved(e){this.model===e&&(this.isShowing()&&this.willHide(),this.model=null)}suspendStateChanged(){const e=i.TargetManager.TargetManager.instance().allTargetsSuspended();e?this.stopPolling():this.startPolling(),this.contentElement.classList.toggle("suspended",e)}startPolling(){this.startTimestamp=0,this.pollTimer=window.setInterval((()=>this.poll()),this.pollIntervalMs),this.onResize();const e=()=>{this.draw(),this.animationId=this.contentElement.window().requestAnimationFrame((()=>{e()}))};e()}stopPolling(){window.clearInterval(this.pollTimer),this.contentElement.window().cancelAnimationFrame(this.animationId),this.metricsBuffer=[]}async poll(){if(!this.model)return;const e=await this.model.requestMetrics(),t=e.timestamp,r=e.metrics;this.metricsBuffer.push({timestamp:t,metrics:r});const o=this.width/this.pixelsPerMs,i=Math.ceil(o/this.pollIntervalMs*2);this.metricsBuffer.length>2*i&&this.metricsBuffer.splice(0,this.metricsBuffer.length-i),this.controlPane.updateMetrics(r)}draw(){const e=this.canvas.getContext("2d");e.save(),e.scale(window.devicePixelRatio,window.devicePixelRatio),e.clearRect(0,0,this.width,this.height),e.save(),e.translate(0,this.scaleHeight);for(const t of this.controlPane.charts())this.controlPane.isActive(t.metrics[0].name)&&(this.drawChart(e,t,this.graphHeight),e.translate(0,this.graphHeight));e.restore(),this.drawHorizontalGrid(e),e.restore()}drawHorizontalGrid(e){const r=n.ThemeSupport.instance().getComputedValue("--color-background-inverted-opacity-2");e.font="10px "+t.Platform.fontFamily(),e.fillStyle=n.ThemeSupport.instance().getComputedValue("--color-background-inverted-opacity-50");const o=Date.now()/1e3;for(let t=Math.ceil(o);;--t){const i=this.width-(1e3*(o-t)-this.pollIntervalMs)*this.pixelsPerMs;if(i<-50)break;e.beginPath(),e.moveTo(i,0),e.lineTo(i,this.height),t>=0&&t%10==0&&e.fillText(new Date(1e3*t).toLocaleTimeString(),i+4,12),e.strokeStyle=t%10?r:this.gridColor,e.stroke()}}drawChart(r,o,i){r.save(),r.rect(0,0,this.width,i),r.clip();const s=1.05*this.calcMax(o),a=o.stacked?new Map:null,l=[];for(let e=o.metrics.length-1;e>=0;--e){const t=o.metrics[e];l.push({path:this.buildMetricPath(o,t,i-8,s,e?a:null),color:t.color})}const c=e.Color.parse(n.ThemeSupport.instance().getComputedValue("--sys-color-cdt-base-container"))?.asLegacyColor();if(c)for(const t of l.reverse()){const o=t.color;r.save();const i=e.Color.parse(o);i&&(r.fillStyle=c.blendWith(i.setAlpha(.2).asLegacyColor()).asString()||"",r.fill(t.path),r.strokeStyle=o,r.lineWidth=.5,r.stroke(t.path),r.restore())}r.fillStyle=n.ThemeSupport.instance().getComputedValue("--color-background-inverted-opacity-50"),r.font=`10px  ${t.Platform.fontFamily()}`,r.fillText(o.title,8,10),this.drawVerticalGrid(r,i-8,s,o),r.restore()}calcMax(e){if(e.max)return e.max;const t=this.width,r=performance.now()-this.pollIntervalMs-t/this.pixelsPerMs;let o=-1/0;for(const t of e.metrics)for(let e=this.metricsBuffer.length-1;e>=0;--e){const i=this.metricsBuffer[e],s=i.metrics.get(t.name);if(void 0!==s&&(o=Math.max(o,s)),i.timestamp<r)break}if(!this.metricsBuffer.length)return 10;const i=Math.pow(10,Math.floor(Math.log10(o)));o=Math.ceil(o/i/2)*i*2;return e.currentMax=.2*o+.8*(e.currentMax||o),e.currentMax}drawVerticalGrid(e,t,r,o){let i=Math.pow(10,Math.floor(Math.log10(r)));const s=Math.floor(r/i);1!==s&&s%2==1&&(i*=2);let a=Math.floor(r/i)*i;const l=r,c=t-18;e.fillStyle=n.ThemeSupport.instance().getComputedValue("--color-background-inverted-opacity-50"),e.strokeStyle=this.gridColor,e.beginPath();for(let t=0;t<2;++t){const t=h(a),r=g.formatNumber(a,o);e.moveTo(0,t),e.lineTo(4,t),e.moveTo(e.measureText(r).width+12,t),e.lineTo(this.width,t),e.fillText(r,8,h(a)+3),a/=2}function h(e){return Math.round(t-c*e/l)+.5}e.stroke(),e.beginPath(),e.moveTo(0,t+.5),e.lineTo(this.width,t+.5),e.strokeStyle=n.ThemeSupport.instance().getComputedValue("--color-background-inverted-opacity-2"),e.stroke()}buildMetricPath(e,t,r,i,s){const n=new Path2D,a=r-18;if(a<1)return n;const l=i,c=t.name,h=this.pixelsPerMs,m=performance.now()-this.pollIntervalMs-this.width/h,d=e.smooth;let p=0,u=0,f=0;this.metricsBuffer.length&&(p=(this.metricsBuffer[0].timestamp-m)*h,n.moveTo(p,g(0)),n.lineTo(this.width+5,g(0)),u=g(this.metricsBuffer[this.metricsBuffer.length-1].metrics.get(c)||0),f=this.width+5,n.lineTo(f,u));for(let e=this.metricsBuffer.length-1;e>=0;--e){const t=this.metricsBuffer[e],r=t.timestamp;let i=t.metrics.get(c)||0;s&&(i+=s.get(r)||0,i=o.NumberUtilities.clamp(i,0,1),s.set(r,i));const a=g(i);if(p=(r-m)*h,d){const e=(f+p)/2;n.bezierCurveTo(e,u,e,a,p,a)}else n.lineTo(p,u),n.lineTo(p,a);if(f=p,u=a,r<m)break}return n;function g(e){return Math.round(r-a*e/l)+.5}}onResize(){super.onResize(),this.width=this.canvas.offsetWidth,this.canvas.width=Math.round(this.width*window.devicePixelRatio),this.recalcChartHeight()}recalcChartHeight(){let e=this.scaleHeight;for(const t of this.controlPane.charts())this.controlPane.isActive(t.metrics[0].name)&&(e+=this.graphHeight);this.height=Math.ceil(e*window.devicePixelRatio),this.canvas.height=this.height,this.canvas.style.height=this.height/window.devicePixelRatio+"px"}}class p extends e.ObjectWrapper.ObjectWrapper{element;enabledChartsSetting;enabledCharts;chartsInfo=[];indicators=new Map;constructor(t){super(),this.element=t.createChild("div","perfmon-control-pane"),this.enabledChartsSetting=e.Settings.Settings.instance().createSetting("perfmon-active-indicators2",["TaskDuration","JSHeapTotalSize","Nodes"]),this.enabledCharts=new Set(this.enabledChartsSetting.get())}instantiateMetricData(){const e={color:void 0,format:void 0,currentMax:void 0,max:void 0,smooth:void 0,stacked:void 0},t=n.ThemeSupport.instance();this.chartsInfo=[{...e,title:m(c.cpuUsage),metrics:[{name:"TaskDuration",color:t.getComputedValue("--override-color-perf-monitor-cpu-task-duration",this.element)},{name:"ScriptDuration",color:t.getComputedValue("--override-color-perf-monitor-cpu-script-duration",this.element)},{name:"LayoutDuration",color:t.getComputedValue("--override-color-perf-monitor-cpu-layout-duration",this.element)},{name:"RecalcStyleDuration",color:t.getComputedValue("--override-color-perf-monitor-cpu-recalc-style-duration",this.element)}],format:"Percent",smooth:!0,stacked:!0,color:t.getComputedValue("--override-color-perf-monitor-cpu",this.element),max:1,currentMax:void 0},{...e,title:m(c.jsHeapSize),metrics:[{name:"JSHeapTotalSize",color:t.getComputedValue("--override-color-perf-monitor-jsheap-total-size",this.element)},{name:"JSHeapUsedSize",color:t.getComputedValue("--override-color-perf-monitor-jsheap-used-size",this.element)}],format:"Bytes",color:t.getComputedValue("--override-color-perf-monitor-jsheap")},{...e,title:m(c.domNodes),metrics:[{name:"Nodes",color:t.getComputedValue("--override-color-perf-monitor-dom-nodes",this.element)}]},{...e,title:m(c.jsEventListeners),metrics:[{name:"JSEventListeners",color:t.getComputedValue("--override-color-perf-monitor-js-event-listeners",this.element)}]},{...e,title:m(c.documents),metrics:[{name:"Documents",color:t.getComputedValue("--override-color-perf-monitor-documents",this.element)}]},{...e,title:m(c.documentFrames),metrics:[{name:"Frames",color:t.getComputedValue("--override-color-perf-monitor-document-frames",this.element)}]},{...e,title:m(c.layoutsSec),metrics:[{name:"LayoutCount",color:t.getComputedValue("--override-color-perf-monitor-layout-count",this.element)}]},{...e,title:m(c.styleRecalcsSec),metrics:[{name:"RecalcStyleCount",color:t.getComputedValue("--override-color-perf-monitor-recalc-style-count",this.element)}]}],this.element.removeChildren(),this.indicators=new Map;for(const e of this.chartsInfo){const t=e.metrics[0].name,r=this.enabledCharts.has(t),i=new g(this.element,e,r,this.onToggle.bind(this,t));i.element.setAttribute("jslog",`${a.toggle().track({click:!0,keydown:"Enter"}).context(o.StringUtilities.toKebabCase(t))}`),this.indicators.set(t,i)}}onToggle(e,t){t?this.enabledCharts.add(e):this.enabledCharts.delete(e),this.enabledChartsSetting.set(Array.from(this.enabledCharts)),this.dispatchEventToListeners("MetricChanged")}charts(){return this.chartsInfo}isActive(e){return this.enabledCharts.has(e)}updateMetrics(e){for(const t of this.indicators.keys()){const r=e.get(t);if(void 0!==r){const e=this.indicators.get(t);e&&e.setValue(r)}}}}let u,f;class g{info;element;swatchElement;valueElement;color;constructor(e,t,r,o){this.color=t.color||t.metrics[0].color,this.info=t,this.element=e.createChild("div","perfmon-indicator");const i=t.metrics[0].name;this.swatchElement=s.UIUtils.CheckboxLabel.create(t.title,r,void 0,i),this.element.appendChild(this.swatchElement),this.swatchElement.checkboxElement.addEventListener("change",(()=>{o(this.swatchElement.checkboxElement.checked),this.element.classList.toggle("active")})),this.valueElement=this.element.createChild("div","perfmon-indicator-value"),this.valueElement.style.color=this.color,this.element.classList.toggle("active",r)}static formatNumber(e,t){switch(u||(u=new Intl.NumberFormat("en-US",{maximumFractionDigits:1}),f=new Intl.NumberFormat("en-US",{maximumFractionDigits:1,style:"percent"})),t.format){case"Percent":return f.format(e);case"Bytes":return o.NumberUtilities.bytesToString(e);default:return u.format(e)}}setValue(e){this.valueElement.textContent=g.formatNumber(e,this.info)}}const v=new Intl.NumberFormat("en-US",{maximumFractionDigits:1});var w=Object.freeze({__proto__:null,PerformanceMonitorImpl:d,ControlPane:p,MetricIndicator:g,format:v});export{w as PerformanceMonitor};
