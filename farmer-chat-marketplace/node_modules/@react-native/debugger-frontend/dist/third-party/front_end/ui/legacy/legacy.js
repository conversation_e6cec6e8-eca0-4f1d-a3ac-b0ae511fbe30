import*as e from"../../core/common/common.js";import*as t from"../../core/i18n/i18n.js";import*as n from"../../core/platform/platform.js";import*as i from"../../core/root/root.js";import*as s from"../visual_logging/visual_logging.js";import*as r from"../../core/host/host.js";import*as o from"../../models/text_utils/text_utils.js";import*as a from"../components/buttons/buttons.js";import*as l from"../components/icon_button/icon_button.js";import*as c from"./theme_support/theme_support.js";import*as d from"../components/adorners/adorners.js";import"../../core/dom_extension/dom_extension.js";import*as h from"../components/helpers/helpers.js";import*as u from"../components/settings/settings.js";import*as p from"../lit-html/lit-html.js";let g;class m{flavorsInternal;eventDispatchers;constructor(){this.flavorsInternal=new Map,this.eventDispatchers=new Map}static instance(e={forceNew:null}){const{forceNew:t}=e;return g&&!t||(g=new m),g}static removeInstance(){g=void 0}setFlavor(e,t){(this.flavorsInternal.get(e)||null)!==t&&(t?this.flavorsInternal.set(e,t):this.flavorsInternal.delete(e),this.dispatchFlavorChange(e,t))}dispatchFlavorChange(e,t){for(const n of b)n.contextTypes().includes(e)&&n.loadListener().then((e=>e.flavorChanged(t)));const n=this.eventDispatchers.get(e);n&&n.dispatchEventToListeners("FlavorChanged",t)}addFlavorChangeListener(t,n,i){let s=this.eventDispatchers.get(t);s||(s=new e.ObjectWrapper.ObjectWrapper,this.eventDispatchers.set(t,s)),s.addEventListener("FlavorChanged",n,i)}removeFlavorChangeListener(e,t,n){const i=this.eventDispatchers.get(e);i&&(i.removeEventListener("FlavorChanged",t,n),i.hasEventListeners("FlavorChanged")||this.eventDispatchers.delete(e))}flavor(e){return this.flavorsInternal.get(e)||null}flavors(){return new Set(this.flavorsInternal.keys())}}const b=[];var f=Object.freeze({__proto__:null,Context:m,registerListener:function(e){b.push(e)}});const v={elements:"Elements",screenshot:"Screenshot",network:"Network",memory:"Memory",javascript_profiler:"JavaScript Profiler",console:"Console",performance:"Performance",mobile:"Mobile",help:"Help",layers:"Layers",navigation:"Navigation",drawer:"Drawer",global:"Global",resources:"Resources",background_services:"Background Services",settings:"Settings",debugger:"Debugger",sources:"Sources",rendering:"Rendering",recorder:"Recorder",changes:"Changes"},y=t.i18n.registerUIStrings("ui/legacy/ActionRegistration.ts",v),w=t.i18n.getLocalizedString.bind(void 0,y);class x extends e.ObjectWrapper.ObjectWrapper{enabledInternal=!0;toggledInternal=!1;actionRegistration;constructor(e){super(),this.actionRegistration=e}id(){return this.actionRegistration.actionId}async execute(){if(!this.actionRegistration.loadActionDelegate)return!1;const e=await this.actionRegistration.loadActionDelegate(),t=this.id();return e.handleAction(m.instance(),t)}icon(){return this.actionRegistration.iconClass}toggledIcon(){return this.actionRegistration.toggledIconClass}toggleWithRedColor(){return Boolean(this.actionRegistration.toggleWithRedColor)}setEnabled(e){this.enabledInternal!==e&&(this.enabledInternal=e,this.dispatchEventToListeners("Enabled",e))}enabled(){return this.enabledInternal}category(){return this.actionRegistration.category}tags(){if(this.actionRegistration.tags)return this.actionRegistration.tags.map((e=>e())).join("\0")}toggleable(){return Boolean(this.actionRegistration.toggleable)}title(){let e=this.actionRegistration.title?this.actionRegistration.title():t.i18n.lockedString("");const n=this.actionRegistration.options;if(n)for(const t of n)t.value!==this.toggledInternal&&(e=t.title());return e}toggled(){return this.toggledInternal}setToggled(e){console.assert(this.toggleable(),"Shouldn't be toggling an untoggleable action",this.id()),this.toggledInternal!==e&&(this.toggledInternal=e,this.dispatchEventToListeners("Toggled",e))}options(){return this.actionRegistration.options}contextTypes(){if(this.actionRegistration.contextTypes)return this.actionRegistration.contextTypes()}canInstantiate(){return Boolean(this.actionRegistration.loadActionDelegate)}bindings(){return this.actionRegistration.bindings}experiment(){return this.actionRegistration.experiment}setting(){return this.actionRegistration.setting}condition(){return this.actionRegistration.condition}order(){return this.actionRegistration.order}}const E=new Map;function I(){E.clear()}function S(){return Array.from(E.values()).filter((t=>{const n=t.setting();try{if(n&&!e.Settings.moduleSetting(n).get())return!1}catch(e){if(e.message.startsWith("No setting registered"))return!1}return i.Runtime.Runtime.isDescriptorEnabled({experiment:t.experiment(),condition:t.condition()},e.Settings.Settings.instance().getHostConfig())})).sort(((e,t)=>(e.order()||0)-(t.order()||0)))}var k=Object.freeze({__proto__:null,Action:x,registerActionExtension:function(e){const t=e.actionId;if(E.has(t))throw new Error(`Duplicate action ID '${t}'`);if(!n.StringUtilities.isExtendedKebabCase(t))throw new Error(`Invalid action ID '${t}'`);E.set(t,new x(e))},reset:I,getRegisteredActionExtensions:S,maybeRemoveActionExtension:function(e){return E.delete(e)},getLocalizedActionCategory:function(e){switch(e){case"ELEMENTS":return w(v.elements);case"SCREENSHOT":return w(v.screenshot);case"NETWORK":return w(v.network);case"MEMORY":return w(v.memory);case"JAVASCRIPT_PROFILER":return w(v.javascript_profiler);case"CONSOLE":return w(v.console);case"PERFORMANCE":return w(v.performance);case"MOBILE":return w(v.mobile);case"HELP":return w(v.help);case"LAYERS":return w(v.layers);case"NAVIGATION":return w(v.navigation);case"DRAWER":return w(v.drawer);case"GLOBAL":return w(v.global);case"RESOURCES":return w(v.resources);case"BACKGROUND_SERVICES":return w(v.background_services);case"SETTINGS":return w(v.settings);case"DEBUGGER":return w(v.debugger);case"SOURCES":return w(v.sources);case"RENDERING":return w(v.rendering);case"RECORDER":return w(v.recorder);case"CHANGES":return w(v.changes);case"":return t.i18n.lockedString("")}return t.i18n.lockedString(e)}});let C;class T{actionsById;constructor(){this.actionsById=new Map,this.registerActions()}static instance(e={forceNew:null}){const{forceNew:t}=e;return C&&!t||(C=new T),C}static removeInstance(){C=void 0}static reset(){T.removeInstance(),I()}registerActions(){for(const e of S())this.actionsById.set(e.id(),e),e.canInstantiate()||e.setEnabled(!1)}availableActions(){return this.applicableActions([...this.actionsById.keys()],m.instance())}actions(){return[...this.actionsById.values()]}applicableActions(e,t){const n=[];for(const s of e){const e=this.actionsById.get(s);e&&e.enabled()&&i(e,t.flavors())&&n.push(e)}return n;function i(e,t){const n=e.contextTypes();if(!n)return!0;for(let e=0;e<n.length;++e){const i=n[e];if(Boolean(i)&&t.has(i))return!0}return!1}}hasAction(e){return this.actionsById.has(e)}getAction(e){const t=this.actionsById.get(e);if(t)return t;throw new Error(`Cannot find registered action with ID '${e}'`)}}var L=Object.freeze({__proto__:null,ActionRegistry:T}),M={cssContent:".widget{box-shadow:var(--drop-shadow);background:var(--sys-color-cdt-base-container);justify-content:flex-start;align-items:stretch;display:flex}.dialog-close-button{position:absolute;right:9px;top:9px;z-index:1}\n/*# sourceURL=dialog.css */\n"},P={cssContent:":host{position:absolute!important;top:0;bottom:0;left:0;right:0;overflow:hidden;contain:strict;background-color:transparent}:host-context(.dimmed-pane){background-color:var(--color-background-opacity-50)}:host-context(.no-pointer-events){pointer-events:none}.widget{display:flex;background-color:transparent;pointer-events:auto;flex:none}.no-pointer-events{pointer-events:none}.arrow{background-image:var(--image-file-popoverArrows);width:19px;height:19px}.arrow-top{background-position:0 76px;margin-top:-19px;margin-left:-9px}.arrow-bottom{background-position:0 57px;margin-left:-9px}.arrow-left{background-position:0 38px;margin-left:-19px;margin-top:-9px}.arrow-right{background-position:0 19px;margin-top:-9px}.arrow-none{display:none}:host-context(.theme-with-dark-background) .arrow{filter:invert(80%)}\n/*# sourceURL=glassPane.css */\n"},D={cssContent:":root{--icon-action:var(--sys-color-primary-bright);--icon-arrow-main-thread:var(--sys-color-primary-bright);--icon-checkmark-green:var(--sys-color-green-bright);--icon-checkmark-purple:var(--sys-color-purple-bright);--icon-checkmark-red:var(--sys-color-error-bright);--icon-contrast-issue:var(--sys-color-error-bright);--icon-default:var(--sys-color-on-surface-subtle);--icon-default-hover:var(--sys-color-on-surface);--icon-disabled:var(--sys-color-state-disabled);--icon-error:var(--sys-color-error-bright);--icon-file-authored:var(--sys-color-orange-bright);--icon-file-default:var(--sys-color-on-surface-subtle);--icon-file-document:var(--sys-color-blue-bright);--icon-file-font:var(--sys-color-cyan-bright);--icon-file-media:var(--sys-color-green-bright);--icon-file-image:var(--sys-color-green-bright);--icon-file-script:var(--sys-color-orange-bright);--icon-file-styles:var(--sys-color-purple-bright);--icon-fold-marker:var(--sys-color-on-surface-subtle);--icon-folder-authored:var(--sys-color-orange);--icon-folder-primary:var(--sys-color-on-surface-subtle);--icon-folder-deployed:var(--sys-color-primary-bright);--icon-folder-workspace:var(--sys-color-orange);--icon-force-white:var(--sys-color-on-tonal-container);--icon-gap-focus-selected:var(--sys-color-tonal-container);--icon-gap-default:var(--sys-color-cdt-base-container);--icon-gap-inactive:var(--sys-color-neutral-container);--icon-gap-hover:var(--sys-color-state-hover-on-subtle);--icon-gap-toolbar:var(--sys-color-surface1);--icon-gap-toolbar-hover:var(--sys-color-state-header-hover);--icon-info:var(--sys-color-primary-bright);--icon-link:var(--sys-color-primary-bright);--icon-no-request:var(--sys-color-orange-bright);--icon-primary:var(--sys-color-primary-bright);--icon-record:var(--sys-color-error-bright);--icon-request-response:var(--sys-color-primary-bright);--icon-request:var(--sys-color-on-surface-subtle);--icon-security-lock:var(--sys-color-green-bright);--icon-css:var(--sys-color-purple-bright);--icon-css-hover:var(--sys-color-purple);--icon-status-code-ok:var(--sys-color-tertiary-bright);--icon-stop:var(--sys-color-error-bright);--icon-toggled:var(--sys-color-primary-bright);--icon-warning:var(--sys-color-orange-bright);--ui-text:var(--sys-color-on-surface-subtle);--text-disabled:var(--ref-palette-neutral60);--text-primary:var(--sys-color-on-surface);--text-secondary:var(--sys-color-token-subtle);--text-link:var(--sys-color-primary);--color-grid-stripe:var(--sys-color-surface1);--color-grid-default:var(--sys-color-surface);--color-grid-focus-selected:var(--sys-color-tonal-container);--color-grid-selected:var(--sys-color-surface-variant);--color-grid-hovered:var(--sys-color-state-hover-on-subtle)}\n/*# sourceURL=applicationColorTokens.css */\n"},A={cssContent:":host{padding:0;margin:0;display:inline-flex;flex-shrink:0;align-items:center!important;overflow:hidden;white-space:nowrap}input{height:12px;width:12px;flex-shrink:0;accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary);&:not(.small){margin:6px}}input.inside-datagrid{height:10px;width:10px}.dt-checkbox-text{overflow:hidden;text-overflow:ellipsis}.dt-checkbox-subtitle{color:var(--sys-color-token-subtle);white-space:break-spaces}@media (forced-colors: active){input.dt-checkbox-theme-preserve{forced-color-adjust:none}input.dt-checkbox-theme-preserve:active{background:HighlightText}input.dt-checkbox-theme-preserve:checked,\n  input.dt-checkbox-theme-preserve:active:checked{background:Highlight;border-color:Highlight}input.dt-checkbox-theme-preserve:hover:enabled{border-color:Highlight}input.dt-checkbox-theme-preserve:active::before,\n  input.dt-checkbox-theme-preserve:active::after{background-color:Highlight}input.dt-checkbox-theme-preserve:checked::before,\n  input.dt-checkbox-theme-preserve:checked::after,\n  input.dt-checkbox-theme-preserve:active:checked::before,\n  input.dt-checkbox-theme-preserve:active:checked::after{background-color:HighlightText}input.dt-checkbox-theme-preserve:hover:checked::before,\n  input.dt-checkbox-theme-preserve:hover:checked::after{background-color:Highlight!important}input.dt-checkbox-theme-preserve:hover:checked{background:HighlightText}}\n/*# sourceURL=checkboxTextLabel.css */\n"},R={cssContent:".widget{padding:20px;box-sizing:border-box;max-width:400px;overflow:hidden}.message{text-align:center}.message,\n.button{font-size:larger;white-space:pre;margin:10px 0}.button{text-align:center;margin-top:20px;display:flex;flex-direction:row-reverse;gap:var(--sys-size-6)}.button button{min-width:100px}.reason{color:var(--sys-color-error);margin-top:10px}.message span{white-space:normal;word-wrap:break-word;max-width:100%;display:inline-block;overflow:hidden;text-overflow:ellipsis;padding:5px;margin:0}\n/*# sourceURL=confirmDialog.css */\n"},B={cssContent:":root{--ref-typeface-weight-regular:400;--ref-typeface-weight-medium:500;--ref-typeface-weight-bold:700;--sys-elevation-level1:0 1px 2px 0 rgb(0 0 0/30%),0 1px 3px 1px rgb(0 0 0/15%);--sys-elevation-level2:0 1px 2px 0 rgb(0 0 0/30%),0 2px 6px 2px rgb(0 0 0/15%);--sys-elevation-level3:0 4px 8px 3px rgb(0 0 0/15%),0 1px 3px 0 rgb(0 0 0/30%);--sys-elevation-level4:0 6px 10px 4px rgb(0 0 0/15%),0 2px 3px 0 rgb(0 0 0/30%);--sys-elevation-level5:0 8px 12px 6px rgb(0 0 0/15%),0 4px 4px 0 rgb(0 0 0/30%);--sys-size-1:1px;--sys-size-2:0.125rem;--sys-size-3:0.25rem;--sys-size-4:0.375rem;--sys-size-5:0.5rem;--sys-size-6:0.75rem;--sys-size-7:0.875rem;--sys-size-8:1rem;--sys-size-9:1.25rem;--sys-size-10:1.375rem;--sys-size-11:1.5rem;--sys-size-12:1.75rem;--sys-size-13:2rem;--sys-size-14:2.5rem;--sys-size-15:2.75rem;--sys-size-16:3rem;--sys-size-17:3.5rem;--sys-size-18:4rem;--sys-size-19:6rem;--sys-size-20:7rem;--sys-size-21:8rem;--sys-size-22:9rem;--sys-size-23:10rem;--sys-size-24:11rem;--sys-size-25:12rem;--sys-size-26:13rem;--sys-size-27:14rem;--sys-size-28:15rem;--sys-size-29:16rem;--sys-size-30:18rem;--sys-size-31:20rem;--sys-size-32:24rem;--sys-size-33:28rem;--sys-size-34:32rem;--sys-size-35:36rem;--sys-size-36:42rem;--sys-size-37:48rem;--sys-size-38:56rem;--sys-size-39:64rem;--sys-size-40:72rem;--sys-size-41:80rem;--sys-shape-corner-extra-small:4px;--sys-shape-corner-small:8px;--sys-shape-corner-medium-small:12px;--sys-shape-corner-medium:16px;--sys-shape-corner-large:24px;--sys-shape-corner-full:9999px;--sys-typescale-headline1:var(--ref-typeface-weight-medium) var(--sys-typescale-headline1-size) /var(--sys-typescale-headline1-line-height) var(--default-font-family);--sys-typescale-headline2:var(--ref-typeface-weight-medium) var(--sys-typescale-headline2-size) /var(--sys-typescale-headline2-line-height) var(--default-font-family);--sys-typescale-headline3:var(--ref-typeface-weight-medium) var(--sys-typescale-headline3-size) /var(--sys-typescale-headline3-line-height) var(--default-font-family);--sys-typescale-headline4:var(--ref-typeface-weight-medium) var(--sys-typescale-headline4-size) /var(--sys-typescale-headline4-line-height) var(--default-font-family);--sys-typescale-headline5:var(--ref-typeface-weight-medium) var(--sys-typescale-headline5-size) /var(--sys-typescale-headline5-line-height) var(--default-font-family);--sys-typescale-body1-regular:var(--ref-typeface-weight-regular) var(--sys-typescale-body1-size) /var(--sys-typescale-body1-line-height) var(--default-font-family);--sys-typescale-body2-regular:var(--ref-typeface-weight-regular) var(--sys-typescale-body2-size) /var(--sys-typescale-body2-line-height) var(--default-font-family);--sys-typescale-body3-regular:var(--ref-typeface-weight-regular) var(--sys-typescale-body3-size) /var(--sys-typescale-body3-line-height) var(--default-font-family);--sys-typescale-body4-regular:var(--ref-typeface-weight-regular) var(--sys-typescale-body4-size) /var(--sys-typescale-body4-line-height) var(--default-font-family);--sys-typescale-body5-regular:var(--ref-typeface-weight-regular) var(--sys-typescale-body5-size) /var(--sys-typescale-body5-line-height) var(--default-font-family);--sys-typescale-body1-medium:var(--ref-typeface-weight-medium) var(--sys-typescale-body1-size) /var(--sys-typescale-body1-line-height) var(--default-font-family);--sys-typescale-body2-medium:var(--ref-typeface-weight-medium) var(--sys-typescale-body2-size) /var(--sys-typescale-body2-line-height) var(--default-font-family);--sys-typescale-body3-medium:var(--ref-typeface-weight-medium) var(--sys-typescale-body3-size) /var(--sys-typescale-body3-line-height) var(--default-font-family);--sys-typescale-body4-medium:var(--ref-typeface-weight-medium) var(--sys-typescale-body4-size) /var(--sys-typescale-body4-line-height) var(--default-font-family);--sys-typescale-body5-medium:var(--ref-typeface-weight-medium) var(--sys-typescale-body5-size) /var(--sys-typescale-body5-line-height) var(--default-font-family);--sys-typescale-body1-bold:var(--ref-typeface-weight-bold) var(--sys-typescale-body1-size) /var(--sys-typescale-body1-line-height) var(--default-font-family);--sys-typescale-body2-bold:var(--ref-typeface-weight-bold) var(--sys-typescale-body2-size) /var(--sys-typescale-body2-line-height) var(--default-font-family);--sys-typescale-body3-bold:var(--ref-typeface-weight-bold) var(--sys-typescale-body3-size) /var(--sys-typescale-body3-line-height) var(--default-font-family);--sys-typescale-body4-bold:var(--ref-typeface-weight-bold) var(--sys-typescale-body4-size) /var(--sys-typescale-body4-line-height) var(--default-font-family);--sys-typescale-body5-bold:var(--ref-typeface-weight-bold) var(--sys-typescale-body5-size) /var(--sys-typescale-body5-line-height) var(--default-font-family);--sys-typescale-monospace-regular:var(--ref-typeface-weight-regular) var(--sys-typescale-monospace-size) /var(--sys-typescale-monospace-line-height) var(--monospace-font-family);--sys-typescale-monospace-bold:var(--ref-typeface-weight-bold) var(--sys-typescale-monospace-size) /var(--sys-typescale-monospace-line-height) var(--monospace-font-family);--sys-typescale-headline1-size:24px;--sys-typescale-headline2-size:20px;--sys-typescale-headline3-size:18px;--sys-typescale-headline4-size:16px;--sys-typescale-headline5-size:14px;--sys-typescale-body1-size:16px;--sys-typescale-body2-size:14px;--sys-typescale-body3-size:13px;--sys-typescale-body4-size:12px;--sys-typescale-body5-size:11px;--sys-typescale-headline1-line-height:32px;--sys-typescale-headline2-line-height:24px;--sys-typescale-headline3-line-height:24px;--sys-typescale-headline4-line-height:24px;--sys-typescale-headline5-line-height:20px;--sys-typescale-body1-line-height:24px;--sys-typescale-body2-line-height:20px;--sys-typescale-body3-line-height:20px;--sys-typescale-body4-line-height:16px;--sys-typescale-body5-line-height:16px}\n/*# sourceURL=designTokens.css */\n"};const z=1e-5;class O{x;y;z;constructor(e,t,n){this.x=e,this.y=t,this.z=n}length(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)}normalize(){const e=this.length();e<=z||(this.x/=e,this.y/=e,this.z/=e)}}class F{x;y;constructor(e,t){this.x=e,this.y=t}distanceTo(e){return Math.sqrt(Math.pow(e.x-this.x,2)+Math.pow(e.y-this.y,2))}projectOn(e){return 0===e.x&&0===e.y?new F(0,0):e.scale((this.x*e.x+this.y*e.y)/(Math.pow(e.x,2)+Math.pow(e.y,2)))}scale(e){return new F(this.x*e,this.y*e)}toString(){return Math.round(100*this.x)/100+", "+Math.round(100*this.y)/100}}class H{controlPoints;constructor(e,t){this.controlPoints=[e,t]}static parse(e){const t=H.KeywordValues,n=e.toLowerCase().replace(/\s+/g,"");if(t.has(n))return H.parse(t.get(n));const i=n.match(/^cubic-bezier\(([^,]+),([^,]+),([^,]+),([^,]+)\)$/);if(i){const e=new F(parseFloat(i[1]),parseFloat(i[2])),t=new F(parseFloat(i[3]),parseFloat(i[4]));return new H(e,t)}return null}evaluateAt(e){function t(e,t,n){return 3*(1-n)*(1-n)*n*e+3*(1-n)*n*n*t+Math.pow(n,3)}const n=t(this.controlPoints[0].x,this.controlPoints[1].x,e),i=t(this.controlPoints[0].y,this.controlPoints[1].y,e);return new F(n,i)}asCSSText(){const e="cubic-bezier("+this.controlPoints.join(", ")+")",t=H.KeywordValues;for(const[n,i]of t)if(e===i&&"linear"!==n)return n;return e}static Regex=/((cubic-bezier\([^)]+\))|\b(linear(?![-\(])|ease-in-out|ease-in|ease-out|ease)\b)|(linear\([^)]+\))/g;static KeywordValues=new Map([["linear","cubic-bezier(0, 0, 1, 1)"],["ease","cubic-bezier(0.25, 0.1, 0.25, 1)"],["ease-in","cubic-bezier(0.42, 0, 1, 1)"],["ease-in-out","cubic-bezier(0.42, 0, 0.58, 1)"],["ease-out","cubic-bezier(0, 0, 0.58, 1)"]])}const W=new H(new F(0,0),new F(1,1));class N{alpha;beta;gamma;constructor(e,t,n){this.alpha=e,this.beta=t,this.gamma=n}static fromDeviceOrientationRotationMatrix(e){let t,n,i;return Math.abs(e.m33)<z?Math.abs(e.m13)<z?(t=Math.atan2(e.m12,e.m11),n=e.m23>0?Math.PI/2:-Math.PI/2,i=0):e.m13>0?(t=Math.atan2(-e.m21,e.m22),n=Math.asin(e.m23),i=-Math.PI/2):(t=Math.atan2(e.m21,-e.m22),n=-Math.asin(e.m23),n+=n>0||Math.abs(n)<z?-Math.PI:Math.PI,i=-Math.PI/2):e.m33>0?(t=Math.atan2(-e.m21,e.m22),n=Math.asin(e.m23),i=Math.atan2(-e.m13,e.m33)):(t=Math.atan2(e.m21,-e.m22),n=-Math.asin(e.m23),n+=n>0||Math.abs(n)<z?-Math.PI:Math.PI,i=Math.atan2(e.m13,-e.m33)),t<-z&&(t+=2*Math.PI),t=Number(U(t).toFixed(6)),n=Number(U(n).toFixed(6)),i=Number(U(i).toFixed(6)),new N(t,n,i)}}const j=function(e,t){return e.x*t.x+e.y*t.y+e.z*t.z},V=function(e,t){const n=e.x*t.m14+e.y*t.m24+e.z*t.m34+t.m44,i=(e.x*t.m11+e.y*t.m21+e.z*t.m31+t.m41)/n,s=(e.x*t.m12+e.y*t.m22+e.z*t.m32+t.m42)/n,r=(e.x*t.m13+e.y*t.m23+e.z*t.m33+t.m43)/n;return new O(i,s,r)},U=function(e){return 180*e/Math.PI};class _{width;height;constructor(e,t){this.width=e,this.height=t}clipTo(e){return e?new _(Math.min(this.width,e.width),Math.min(this.height,e.height)):this}scale(e){return new _(this.width*e,this.height*e)}isEqual(e){return null!==e&&this.width===e.width&&this.height===e.height}widthToMax(e){return new _(Math.max(this.width,"number"==typeof e?e:e.width),this.height)}addWidth(e){return new _(this.width+("number"==typeof e?e:e.width),this.height)}heightToMax(e){return new _(this.width,Math.max(this.height,"number"==typeof e?e:e.height))}addHeight(e){return new _(this.width,this.height+("number"==typeof e?e:e.height))}}class K{minimum;preferred;constructor(e,t){if(this.minimum=e||new _(0,0),this.preferred=t||this.minimum,this.minimum.width>this.preferred.width||this.minimum.height>this.preferred.height)throw new Error("Minimum size is greater than preferred.")}isEqual(e){return null!==e&&this.minimum.isEqual(e.minimum)&&this.preferred.isEqual(e.preferred)}widthToMax(e){return"number"==typeof e?new K(this.minimum.widthToMax(e),this.preferred.widthToMax(e)):new K(this.minimum.widthToMax(e.minimum),this.preferred.widthToMax(e.preferred))}addWidth(e){return"number"==typeof e?new K(this.minimum.addWidth(e),this.preferred.addWidth(e)):new K(this.minimum.addWidth(e.minimum),this.preferred.addWidth(e.preferred))}heightToMax(e){return"number"==typeof e?new K(this.minimum.heightToMax(e),this.preferred.heightToMax(e)):new K(this.minimum.heightToMax(e.minimum),this.preferred.heightToMax(e.preferred))}addHeight(e){return"number"==typeof e?new K(this.minimum.addHeight(e),this.preferred.addHeight(e)):new K(this.minimum.addHeight(e.minimum),this.preferred.addHeight(e.preferred))}}var q=Object.freeze({__proto__:null,Vector:O,Point:F,CubicBezier:H,LINEAR_BEZIER:W,EulerAngles:N,scalarProduct:j,crossProduct:function(e,t){const n=e.y*t.z-e.z*t.y,i=e.z*t.x-e.x*t.z,s=e.x*t.y-e.y*t.x;return new O(n,i,s)},subtract:function(e,t){const n=e.x-t.x,i=e.y-t.y,s=e.z-t.z;return new O(n,i,s)},multiplyVectorByMatrixAndNormalize:V,calculateAngle:function(e,t){const n=e.length(),i=t.length();if(n<=z||i<=z)return 0;const s=j(e,t)/n/i;return Math.abs(s)>1?0:U(Math.acos(s))},degreesToRadians:function(e){return e*Math.PI/180},degreesToGradians:function(e){return e/9*10},degreesToTurns:function(e){return e/360},radiansToDegrees:U,radiansToGradians:function(e){return 200*e/Math.PI},radiansToTurns:function(e){return e/(2*Math.PI)},gradiansToRadians:function(e){return e*Math.PI/200},turnsToRadians:function(e){return 2*e*Math.PI},boundsForTransformedPoints:function(e,t,n){n||(n={minX:1/0,maxX:-1/0,minY:1/0,maxY:-1/0}),t.length%3&&console.warn("Invalid size of points array");for(let i=0;i<t.length;i+=3){let s=new O(t[i],t[i+1],t[i+2]);s=V(s,e),n.minX=Math.min(n.minX,s.x),n.maxX=Math.max(n.maxX,s.x),n.minY=Math.min(n.minY,s.y),n.maxY=Math.max(n.maxY,s.y)}return n},Size:_,Constraints:K}),G={cssContent:":host{display:inline-flex;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;position:relative;vertical-align:sub;margin:2px;background-color:var(--sys-color-cdt-base-container);justify-content:center;width:28px}:host:hover{border:none;background-color:var(--sys-color-state-hover-on-subtle)}\n/*# sourceURL=inlineButton.css */\n"},$={cssContent:'*{box-sizing:border-box;min-width:0;min-height:0}:root{height:100%;overflow:hidden;--legacy-accent-color:#1a73e8;--legacy-accent-fg-color:#1a73e8;--legacy-accent-color-hover:#3b86e8;--legacy-accent-fg-color-hover:#1567d3;--legacy-active-control-bg-color:#5a5a5a;--legacy-focus-bg-color:hsl(214deg 40% 92%);--legacy-focus-ring-inactive-shadow-color:#e0e0e0;--legacy-input-validation-error:#db1600;--legacy-toolbar-hover-bg-color:#eaeaea;--legacy-selection-fg-color:#fff;--legacy-selection-bg-color:var(--legacy-accent-color);--legacy-selection-inactive-fg-color:#5a5a5a;--legacy-selection-inactive-bg-color:#dadada;--legacy-divider-border:1px solid var(--sys-color-divider);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-focus-ring-active-shadow:0 0 0 1px var(--legacy-accent-color);--legacy-item-selection-bg-color:#cfe8fc;--legacy-item-selection-inactive-bg-color:#e0e0e0;--monospace-font-size:10px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace;--sys-motion-duration-short4:200ms;--sys-motion-duration-medium2:300ms;--sys-motion-duration-long2:500ms;--sys-motion-easing-emphasized:cubic-bezier(0.2,0,0,1);--sys-motion-easing-emphasized-decelerate:cubic-bezier(0.05,0.7,0.1,1);--sys-motion-easing-emphasized-accelerate:cubic-bezier(0.2,0,0,1)}.theme-with-dark-background{color-scheme:dark;--legacy-accent-color:#0e639c;--legacy-accent-fg-color:#ccc;--legacy-accent-fg-color-hover:#fff;--legacy-accent-color-hover:rgb(17 119 187);--legacy-active-control-bg-color:#cdcdcd;--legacy-focus-bg-color:hsl(214deg 19% 27%);--legacy-focus-ring-inactive-shadow-color:#5a5a5a;--legacy-toolbar-hover-bg-color:#202020;--legacy-selection-fg-color:#cdcdcd;--legacy-selection-inactive-fg-color:#cdcdcd;--legacy-selection-inactive-bg-color:hsl(0deg 0% 28%);--legacy-focus-ring-inactive-shadow:0 0 0 1px var(--legacy-focus-ring-inactive-shadow-color);--legacy-item-selection-bg-color:hsl(207deg 88% 22%);--legacy-item-selection-inactive-bg-color:#454545}body{--default-font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;height:100%;width:100%;position:relative;overflow:hidden;margin:0;cursor:default;font-family:var(--default-font-family);font-size:12px;tab-size:4;user-select:none;color:var(--sys-color-on-surface);background:var(--sys-color-cdt-base-container)}.platform-linux{--default-font-family:"Google Sans Text","Google Sans",system-ui,sans-serif}.platform-mac{--default-font-family:system-ui,sans-serif}.platform-windows{--default-font-family:system-ui,sans-serif}:focus{outline-width:0}.platform-mac,\n:host-context(.platform-mac){--monospace-font-size:11px;--monospace-font-family:monospace;--source-code-font-size:11px;--source-code-font-family:monospace}.platform-windows,\n:host-context(.platform-windows){--monospace-font-size:12px;--monospace-font-family:monospace;--source-code-font-size:12px;--source-code-font-family:monospace}.platform-linux,\n:host-context(.platform-linux){--monospace-font-size:11px;--monospace-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace;--source-code-font-size:11px;--source-code-font-family:"Noto Sans Mono","DejaVu Sans Mono",monospace}.monospace{font-family:var(--monospace-font-family);font-size:var(--monospace-font-size)!important}.source-code{font-family:var(--source-code-font-family);font-size:var(--source-code-font-size)!important;white-space:pre-wrap}.source-code .devtools-link.text-button{max-width:100%;overflow:hidden;text-overflow:ellipsis}img{-webkit-user-drag:none}iframe,\na img{border:none}.fill{position:absolute;top:0;left:0;right:0;bottom:0}iframe.fill{width:100%;height:100%}.widget{position:relative;flex:auto;contain:style}.hbox{display:flex;flex-direction:row!important;position:relative}.vbox{display:flex;flex-direction:column!important;position:relative}.view-container > .toolbar{border-bottom:1px solid var(--sys-color-divider)}.flex-auto{flex:auto}.flex-none{flex:none}.flex-centered{display:flex;align-items:center;justify-content:center}.overflow-auto{overflow:auto;background-color:var(--sys-color-cdt-base-container)}iframe.widget{position:absolute;width:100%;height:100%;left:0;right:0;top:0;bottom:0}.hidden{display:none!important}.highlighted-search-result{border-radius:1px;background-color:var(--sys-color-yellow-container);outline:1px solid var(--sys-color-yellow-container)}.link{cursor:pointer;text-decoration:underline;color:var(--sys-color-primary);outline-offset:2px}button,\ninput,\nselect{font-family:inherit;font-size:inherit}select option,\nselect optgroup,\ninput{background-color:var(--sys-color-cdt-base-container)}input{color:inherit;&[type="checkbox"]{position:relative;&:hover::after,\n    &:active::before{content:"";height:24px;width:24px;border-radius:var(--sys-shape-corner-full);position:absolute;top:-6px;left:-6px}&:not(.-theme-preserve){accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}&:not(:disabled):hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:not(:disabled):active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:not(:disabled):focus-visible{outline:none;&::before{content:"";height:15px;width:15px;border-radius:5px;position:absolute;top:-3.5px;left:-3.5px;border:2px solid var(--sys-color-state-focus-ring)}}&.small:hover::after,\n    &.small:active::before{height:12px;width:12px;top:0;left:0;border-radius:2px}}}input::placeholder{--override-input-placeholder-color:rgb(0 0 0/54%);color:var(--override-input-placeholder-color)}.theme-with-dark-background input::placeholder,\n:host-context(.theme-with-dark-background) input::placeholder{--override-input-placeholder-color:rgb(230 230 230/54%)}.harmony-input:not([type]),\n.harmony-input[type="number"],\n.harmony-input[type="text"]{padding:3px 6px;height:24px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;&.error-input,\n  &:invalid{border-color:var(--sys-color-error)}&:not(.error-input):not(:invalid):focus{border-color:var(--sys-color-state-focus-ring)}&:not(.error-input):not(:invalid):hover:not(:focus){background:var(--sys-color-state-hover-on-subtle)}}.highlighted-search-result.current-search-result{--override-current-search-result-background-color:rgb(255 127 0/80%);border-radius:1px;padding:1px;margin:-1px;background-color:var(--override-current-search-result-background-color)}.dimmed{opacity:60%}.editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;padding-left:2px;margin-left:-2px;padding-right:2px;margin-right:-2px;margin-bottom:-1px;padding-bottom:1px;opacity:100%!important}.editing,\n.editing *{color:var(--sys-color-on-surface)!important;text-decoration:none!important}.chrome-select{appearance:none;user-select:none;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;color:var(--sys-color-on-surface);font:inherit;margin:0;outline:none;padding-right:20px;padding-left:6px;background-image:var(--image-file-arrow-drop-down-light);background-color:var(--sys-color-surface);background-position:right center;background-repeat:no-repeat;min-height:24px;min-width:80px}.chrome-select:disabled{opacity:38%}.theme-with-dark-background .chrome-select,\n:host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.chrome-select:enabled{&:hover{background-color:var(--sys-color-state-hover-on-subtle)}&:active{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px}}@media (forced-colors: active) and (prefers-color-scheme: light){.chrome-select{background-image:var(--image-file-arrow-drop-down-light)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-light)}}@media (forced-colors: active) and (prefers-color-scheme: dark){.chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}.theme-with-dark-background .chrome-select,\n  :host-context(.theme-with-dark-background) .chrome-select{background-image:var(--image-file-arrow-drop-down-dark)}}.chrome-select-label{margin:0 22px;flex:none}.chrome-select-label p p{margin-top:0;color:var(--sys-color-token-subtle)}.settings-select{margin:0}.chrome-select optgroup,\n.chrome-select option{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface)}.gray-info-message{text-align:center;font-style:italic;padding:6px;color:var(--sys-color-token-subtle);white-space:nowrap}span[is="dt-icon-label"]{flex:none}.full-widget-dimmed-banner a{color:inherit}.full-widget-dimmed-banner{color:var(--sys-color-token-subtle);background-color:var(--sys-color-cdt-base-container);display:flex;justify-content:center;align-items:center;text-align:center;padding:20px;position:absolute;top:0;right:0;bottom:0;left:0;font-size:13px;overflow:auto;z-index:500}.dot::before{content:var(--image-file-empty);width:6px;height:6px;border-radius:50%;outline:1px solid var(--icon-gap-default);left:9px;position:absolute;top:9px;z-index:1}.green::before{background-color:var(--sys-color-green-bright)}.purple::before{background-color:var(--sys-color-purple-bright)}.expandable-inline-button{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);cursor:pointer;border-radius:3px}.undisplayable-text,\n.expandable-inline-button{border:none;padding:1px 3px;margin:0 2px;font-size:11px;font-family:sans-serif;white-space:nowrap;display:inline-block}.undisplayable-text::after,\n.expandable-inline-button::after{content:attr(data-text)}.undisplayable-text{color:var(--sys-color-state-disabled);font-style:italic}.expandable-inline-button:hover,\n.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.expandable-inline-button:focus-visible{background-color:var(--sys-color-state-focus-highlight)}::selection{background-color:var(--sys-color-tonal-container)}.reload-warning{align-self:center;margin-left:10px}button.link{border:none;background:none;padding:3px}button.link:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:var(--sys-shape-corner-full)}.theme-with-dark-background button.link:focus-visible,\n:host-context(.theme-with-dark-background) button.link:focus-visible{--override-link-focus-background-color:rgb(230 230 230/8%)}@media (forced-colors: active){.dimmed,\n  .chrome-select:disabled{opacity:100%}.harmony-input:not([type]),\n  .harmony-input[type="number"],\n  .harmony-input[type="text"]{border:1px solid ButtonText}.harmony-input:not([type]):focus,\n  .harmony-input[type="number"]:focus,\n  .harmony-input[type="text"]:focus{border:1px solid Highlight}}input.custom-search-input::-webkit-search-cancel-button{appearance:none;width:16px;height:15px;margin-right:0;opacity:70%;mask-image:var(--image-file-cross-circle-filled);mask-position:center;mask-repeat:no-repeat;mask-size:99%;background-color:var(--icon-default)}input.custom-search-input::-webkit-search-cancel-button:hover{opacity:99%}.spinner::before{display:block;width:var(--dimension,24px);height:var(--dimension,24px);border:var(--override-spinner-size,3px) solid var(--override-spinner-color,var(--sys-color-token-subtle));border-radius:12px;clip:rect(0,var(--clip-size,15px),var(--clip-size,15px),0);content:"";position:absolute;animation:spinner-animation 1s linear infinite;box-sizing:border-box}@keyframes spinner-animation{from{transform:rotate(0)}to{transform:rotate(360deg)}}.adorner-container{display:inline-flex;vertical-align:middle}.adorner-container.hidden{display:none}.adorner-container devtools-adorner{margin-left:3px}:host-context(.theme-with-dark-background) devtools-adorner{--override-adorner-border-color:var(--sys-color-tonal-outline);--override-adorner-focus-border-color:var(--sys-color-state-focus-ring);--override-adorner-active-background-color:var(--sys-color-state-riple-neutral-on-subtle)}.panel{display:flex;overflow:hidden;position:absolute;top:0;left:0;right:0;bottom:0;z-index:0;background-color:var(--sys-color-cdt-base-container)}.panel-sidebar{overflow-x:hidden;background-color:var(--sys-color-cdt-base-container)}iframe.extension{flex:auto;width:100%;height:100%}iframe.panel.extension{display:block;height:100%}@media (forced-colors: active){:root{--legacy-accent-color:Highlight;--legacy-focus-ring-inactive-shadow-color:ButtonText}}\n/*# sourceURL=inspectorCommon.css */\n'};let X;class Y{actionRegistry;actionToShortcut;keyMap;activePrefixKey;activePrefixTimeout;consumePrefix;devToolsDefaultShortcutActions;disabledDefaultShortcutsForAction;keybindSetSetting;userShortcutsSetting;constructor(t){this.actionRegistry=t,this.actionToShortcut=new n.MapUtilities.Multimap,this.keyMap=new Q(0,0),this.activePrefixKey=null,this.activePrefixTimeout=null,this.consumePrefix=null,this.devToolsDefaultShortcutActions=new Set,this.disabledDefaultShortcutsForAction=new n.MapUtilities.Multimap,this.keybindSetSetting=e.Settings.Settings.instance().moduleSetting("active-keybind-set"),this.keybindSetSetting.addChangeListener((e=>{r.userMetrics.keybindSetSettingChanged(e.data),this.registerBindings()})),this.userShortcutsSetting=e.Settings.Settings.instance().moduleSetting("user-shortcuts"),this.userShortcutsSetting.addChangeListener(this.registerBindings,this),this.registerBindings()}static instance(e={forceNew:null,actionRegistry:null}){const{forceNew:t,actionRegistry:n}=e;if(!X||t){if(!n)throw new Error("Missing actionRegistry for shortcutRegistry");X=new Y(n)}return X}static removeInstance(){X=void 0}applicableActions(e,t={}){let n=[];const i=(this.activePrefixKey||this.keyMap).getNode(e);i&&(n=i.actions());const s=this.actionRegistry.applicableActions(n,m.instance());if(i)for(const e of Object.keys(t))if(i.actions().indexOf(e)>=0&&this.actionRegistry.hasAction(e)){const t=this.actionRegistry.getAction(e);s.push(t)}return s}shortcutsForAction(e){return[...this.actionToShortcut.get(e)]}actionsForDescriptors(e){let t=this.keyMap;for(const{key:n}of e){if(!t)return[];t=t.getNode(n)}return t?t.actions():[]}globalShortcutKeys(){const e=[];for(const t of this.keyMap.chords().values()){const n=t.actions();(this.actionRegistry.applicableActions(n,m.instance()).length||t.hasChords())&&e.push(t.key())}return e}keysForActions(e){const t=e.flatMap((e=>[...this.actionToShortcut.get(e)].flatMap((e=>e.descriptors.map((e=>e.key))))));return[...new Set(t)]}shortcutTitleForAction(e){for(const t of this.actionToShortcut.get(e))return t.title()}handleShortcut(e,t){this.handleKey(ie.makeKeyFromEvent(e),e.key,e,t)}actionHasDefaultShortcut(e){return this.devToolsDefaultShortcutActions.has(e)}getShortcutListener(e){const t=Object.keys(e).flatMap((e=>[...this.actionToShortcut.get(e)])),n=new Q(0,0);return t.forEach((e=>{n.addKeyMapping(e.descriptors.map((e=>e.key)),e.action)})),t=>{const i=ie.makeKeyFromEvent(t),s=this.activePrefixKey?n.getNode(this.activePrefixKey.key()):n;s&&s.getNode(i)&&this.handleShortcut(t,e)}}addShortcutListener(e,t){const n=this.getShortcutListener(t);return e.addEventListener("keydown",n),n}async handleKey(e,t,n,i){const o=e>>8,a=Boolean(i)||Boolean(this.activePrefixKey),l=this.keyMap.getNode(e),c=this.applicableActions(e,i).length>0||l&&l.hasChords();if((a||!function(){if(!n||!vi()||/^F\d+|Control|Shift|Alt|Meta|Escape|Win|U\+001B$/.test(t))return!1;if(!o)return!0;const i=se;if(r.Platform.isMac()){if(ie.makeKey("z",i.Meta)===e)return!0;if(ie.makeKey("z",i.Meta|i.Shift)===e)return!0}else{if(ie.makeKey("z",i.Ctrl)===e)return!0;if(ie.makeKey("y",i.Ctrl)===e)return!0;if(!r.Platform.isWin()&&ie.makeKey("z",i.Ctrl|i.Shift)===e)return!0}if((o&(i.Ctrl|i.Alt))==(i.Ctrl|i.Alt))return r.Platform.isWin();return!d(i.Ctrl)&&!d(i.Alt)&&!d(i.Meta)}())&&c&&!ie.isModifier(ie.keyCodeAndModifiersFromKey(e).keyCode)&&(n&&n.consume(!0),a||!ws.hasInstance())){if(this.activePrefixTimeout){clearTimeout(this.activePrefixTimeout);const e=await h.call(this,n);if(this.activePrefixKey=null,this.activePrefixTimeout=null,e)return;this.consumePrefix&&await this.consumePrefix()}l&&l.hasChords()?(this.activePrefixKey=l,this.consumePrefix=async()=>{this.activePrefixKey=null,this.activePrefixTimeout=null,await h.call(this,n)},this.activePrefixTimeout=window.setTimeout(this.consumePrefix,ee)):await h.call(this,n)}function d(e){return Boolean(o&e)}async function h(t){const n=this.applicableActions(e,i);if(!n.length)return!1;for(const e of n){let n;if(i&&i[e.id()]&&(n=await i[e.id()]()),i||(n=await e.execute()),n)return r.userMetrics.keyboardShortcutFired(e.id()),t&&s.logKeyDown(null,t,e.id()),!0}return!1}}registerUserShortcut(e){for(const t of this.disabledDefaultShortcutsForAction.get(e.action))if(t.descriptorsMatch(e.descriptors)&&t.hasKeybindSet(this.keybindSetSetting.get()))return void this.removeShortcut(t);for(const t of this.actionToShortcut.get(e.action))if(t.descriptorsMatch(e.descriptors))return;this.addShortcutToSetting(e)}removeShortcut(e){"DefaultShortcut"===e.type||"KeybindSetShortcut"===e.type?this.addShortcutToSetting(e.changeType("DisabledDefault")):this.removeShortcutFromSetting(e)}disabledDefaultsForAction(e){return this.disabledDefaultShortcutsForAction.get(e)}addShortcutToSetting(e){const t=this.userShortcutsSetting.get();t.push(e),this.userShortcutsSetting.set(t)}removeShortcutFromSetting(e){const t=this.userShortcutsSetting.get(),n=t.findIndex(e.equals,e);-1!==n&&(t.splice(n,1),this.userShortcutsSetting.set(t))}registerShortcut(e){this.actionToShortcut.set(e.action,e),this.keyMap.addKeyMapping(e.descriptors.map((e=>e.key)),e.action)}registerBindings(){this.actionToShortcut.clear(),this.keyMap.clear();const e=this.keybindSetSetting.get();this.disabledDefaultShortcutsForAction.clear(),this.devToolsDefaultShortcutActions.clear();const t=[],n=this.userShortcutsSetting.get();for(const e of n){const n=ie.createShortcutFromSettingObject(e);"DisabledDefault"===n.type?this.disabledDefaultShortcutsForAction.set(n.action,n):(J.has(n.action)&&t.push(...n.descriptors.map((e=>ie.keyCodeAndModifiersFromKey(e.key)))),this.registerShortcut(n))}for(const e of S()){const n=e.id(),r=e.bindings();for(let e=0;r&&e<r.length;++e){const o=r[e].keybindSets;if(!i(r[e].platform)||!s(o))continue;const a=r[e].shortcut.split(/\s+/).map(ie.makeDescriptorFromBindingShortcut);if(a.length>0){if(this.isDisabledDefault(a,n)){this.devToolsDefaultShortcutActions.add(n);continue}J.has(n)&&t.push(...a.map((e=>ie.keyCodeAndModifiersFromKey(e.key)))),o?(o.includes("devToolsDefault")&&this.devToolsDefaultShortcutActions.add(n),this.registerShortcut(new ie(a,n,"KeybindSetShortcut",new Set(o)))):(this.devToolsDefaultShortcutActions.add(n),this.registerShortcut(new ie(a,n,"DefaultShortcut")))}}}function i(e){if(!e)return!0;const t=e.split(",");let n=!1;const i=r.Platform.platform();for(let e=0;!n&&e<t.length;++e)n=t[e]===i;return n}function s(t){return!t||t.includes(e)}r.InspectorFrontendHost.InspectorFrontendHostInstance.setWhitelistedShortcuts(JSON.stringify(t))}isDisabledDefault(e,t){const n=this.disabledDefaultShortcutsForAction.get(t);for(const t of n)if(t.descriptorsMatch(e))return!0;return!1}}class Q{keyInternal;actionsInternal;chordsInternal;depth;constructor(e,t=0){this.keyInternal=e,this.actionsInternal=[],this.chordsInternal=new Map,this.depth=t}addAction(e){this.actionsInternal.push(e)}key(){return this.keyInternal}chords(){return this.chordsInternal}hasChords(){return this.chordsInternal.size>0}addKeyMapping(e,t){if(!(e.length<this.depth))if(e.length===this.depth)this.addAction(t);else{const n=e[this.depth];this.chordsInternal.has(n)||this.chordsInternal.set(n,new Q(n,this.depth+1)),this.chordsInternal.get(n).addKeyMapping(e,t)}}getNode(e){return this.chordsInternal.get(e)||null}actions(){return this.actionsInternal}clear(){this.actionsInternal=[],this.chordsInternal=new Map}}class Z{static instance=new Z}const J=new Set(["main.toggle-dock","debugger.toggle-breakpoints-active","debugger.toggle-pause","quick-open.show-command-menu","console.toggle"]),ee=1e3,te="devToolsDefault";var ne=Object.freeze({__proto__:null,ShortcutRegistry:Y,ShortcutTreeNode:Q,ForwardedShortcut:Z,ForwardedActions:J,KeyTimeout:ee,DefaultShortcutSetting:te});class ie{descriptors;action;type;keybindSets;constructor(e,t,n,i){this.descriptors=e,this.action=t,this.type=n,this.keybindSets=i||new Set}title(){return this.descriptors.map((e=>e.name)).join(" ")}isDefault(){return"DefaultShortcut"===this.type||"DisabledDefault"===this.type||"KeybindSetShortcut"===this.type&&this.keybindSets.has(te)}changeType(e){return new ie(this.descriptors,this.action,e)}changeKeys(e){return this.descriptors=e,this}descriptorsMatch(e){return e.length===this.descriptors.length&&e.every(((e,t)=>e.key===this.descriptors[t].key))}hasKeybindSet(e){return!this.keybindSets||this.keybindSets.has(e)}equals(e){return this.descriptorsMatch(e.descriptors)&&this.type===e.type&&this.action===e.action}static createShortcutFromSettingObject(e){return new ie(e.descriptors,e.action,e.type)}static makeKey(e,t){return"string"==typeof e&&(e=e.charCodeAt(0)-(/^[a-z]/.test(e)?32:0)),t=t||se.None,ie.makeKeyFromCodeAndModifiers(e,t)}static makeKeyFromEvent(e){let t=se.None;e.shiftKey&&(t|=se.Shift),e.ctrlKey&&(t|=se.Ctrl),e.altKey&&(t|=se.Alt),e.metaKey&&(t|=se.Meta);const n=e.keyCode||e.__keyCode;return ie.makeKeyFromCodeAndModifiers(n,t)}static makeKeyFromEventIgnoringModifiers(e){const t=e.keyCode||e.__keyCode;return ie.makeKeyFromCodeAndModifiers(t,se.None)}static eventHasCtrlEquivalentKey(e){return r.Platform.isMac()?e.metaKey&&!e.ctrlKey:e.ctrlKey&&!e.metaKey}static eventHasEitherCtrlOrMeta(e){return e.metaKey||e.ctrlKey}static hasNoModifiers(e){const t=e;return!(t.ctrlKey||t.shiftKey||t.altKey||t.metaKey)}static makeDescriptor(e,t){return{key:ie.makeKey("string"==typeof e?e:e.code,t),name:ie.shortcutToString(e,t)}}static makeDescriptorFromBindingShortcut(e){const[t,...n]=e.split(/\+(?!$)/).reverse();let i=0;for(const t of n){const n=se[t];console.assert(void 0!==n,`Only one key other than modifier is allowed in shortcut <${e}>`),i|=n}console.assert(t.length>0,`Modifiers-only shortcuts are not allowed (encountered <${e}>)`);const s=be[t]||fe[t];return s&&"shiftKey"in s&&s.shiftKey&&(i|=se.Shift),ie.makeDescriptor(s||t,i)}static shortcutToString(e,t){return"string"!=typeof e&&ie.isModifier(e.code)?ie.modifiersToString(t):ie.modifiersToString(t)+ie.keyName(e)}static keyName(e){return"string"==typeof e?e.toUpperCase():"string"==typeof e.name?e.name:e.name[r.Platform.platform()]||e.name.other||""}static makeKeyFromCodeAndModifiers(e,t){return 255&e|(t||0)<<8}static keyCodeAndModifiersFromKey(e){return{keyCode:255&e,modifiers:e>>8}}static isModifier(e){const{keyCode:t}=ie.keyCodeAndModifiersFromKey(e);return t===be.Shift.code||t===be.Ctrl.code||t===be.Alt.code||t===be.Meta.code}static modifiersToString(e){const t=r.Platform.isMac(),n=se,i=new Map([[n.Ctrl,t?"Ctrl ":"Ctrl + "],[n.Alt,t?"⌥ ":"Alt + "],[n.Shift,t?"⇧ ":"Shift + "],[n.Meta,t?"⌘ ":"Win + "]]);return[n.Meta,n.Ctrl,n.Alt,n.Shift].map((function(t){return(e||0)&t?i.get(t):""})).join("")}}const se={None:0,Shift:1,Ctrl:2,Alt:4,Meta:8,CtrlOrMeta:r.Platform.isMac()?8:2,ShiftOrOption:r.Platform.isMac()?4:1},re={code:37,name:"←"},oe={code:38,name:"↑"},ae={code:39,name:"→"},le={code:40,name:"↓"},ce={code:17,name:"Ctrl"},de={code:27,name:"Esc"},he={code:32,name:"Space"},ue={code:187,name:"+"},pe={code:192,name:"`"},ge={code:222,name:"'"},me={code:91,name:"Meta"},be={Backspace:{code:8,name:"↤"},Tab:{code:9,name:{mac:"⇥",other:"Tab"}},Enter:{code:13,name:{mac:"↩",other:"Enter"}},Shift:{code:16,name:{mac:"⇧",other:"Shift"}},Ctrl:ce,Control:ce,Alt:{code:18,name:"Alt"},Esc:de,Escape:de,Space:he," ":he,PageUp:{code:33,name:{mac:"⇞",other:"PageUp"}},PageDown:{code:34,name:{mac:"⇟",other:"PageDown"}},End:{code:35,name:{mac:"↗",other:"End"}},Home:{code:36,name:{mac:"↖",other:"Home"}},Left:re,Up:oe,Right:ae,Down:le,ArrowLeft:re,ArrowUp:oe,ArrowRight:ae,ArrowDown:le,Delete:{code:46,name:"Del"},Zero:{code:48,name:"0"},H:{code:72,name:"H"},N:{code:78,name:"N"},P:{code:80,name:"P"},Meta:me,F1:{code:112,name:"F1"},F2:{code:113,name:"F2"},F3:{code:114,name:"F3"},F4:{code:115,name:"F4"},F5:{code:116,name:"F5"},F6:{code:117,name:"F6"},F7:{code:118,name:"F7"},F8:{code:119,name:"F8"},F9:{code:120,name:"F9"},F10:{code:121,name:"F10"},F11:{code:122,name:"F11"},F12:{code:123,name:"F12"},Semicolon:{code:186,name:";"},NumpadPlus:{code:107,name:"Numpad +"},NumpadMinus:{code:109,name:"Numpad -"},Numpad0:{code:96,name:"Numpad 0"},Plus:ue,Equal:ue,Comma:{code:188,name:","},Minus:{code:189,name:"-"},Period:{code:190,name:"."},Slash:{code:191,name:"/"},QuestionMark:{code:191,name:"?"},Apostrophe:pe,Tilde:{code:192,name:"Tilde"},Backquote:pe,IntlBackslash:pe,LeftSquareBracket:{code:219,name:"["},RightSquareBracket:{code:221,name:"]"},Backslash:{code:220,name:"\\"},SingleQuote:ge,Quote:ge,CtrlOrMeta:r.Platform.isMac()?me:ce},fe={};!function(){for(const e in be){const t=be[e];if("object"==typeof t&&t.code){const n="string"==typeof t.name?t.name:e;fe[n]=t}}}();var ve=Object.freeze({__proto__:null,KeyboardShortcut:ie,Modifiers:se,Keys:be,KeyBindings:fe}),ye={cssContent:"::slotted(input.dt-radio-button){height:17px;width:17px;min-width:17px;border-radius:8px;vertical-align:middle;margin:0 5px 5px 0;accent-color:var(--sys-color-primary-bright);color:var(--sys-color-on-primary)}::slotted(input.dt-radio-button:focus){box-shadow:var(--legacy-focus-ring-active-shadow)}@media (forced-colors: active){::slotted(input.dt-radio-button){--gradient-start:ButtonFace;--gradient-end:ButtonFace}::slotted(input.dt-radio-button:checked){--gradient-start:Highlight;--gradient-end:Highlight}}\n/*# sourceURL=radioButton.css */\n"},we={cssContent:".dt-range-input{appearance:none;margin:0;padding:0;height:10px;width:88px;outline:none;background:none}.dt-range-input::-webkit-slider-thumb,\n.-theme-preserve{appearance:none;margin:0;padding:0;border:0;width:12px;height:12px;margin-top:-5px;border-radius:50%;background-color:var(--sys-color-primary)}.dt-range-input::-webkit-slider-runnable-track{appearance:none;margin:0;padding:0;width:100%;height:2px;background-color:var(--sys-color-surface-variant)}.dt-range-input:focus::-webkit-slider-thumb{box-shadow:0 0 0 2px var(--sys-color-inverse-primary)}.dt-range-input:disabled::-webkit-slider-thumb{background-color:var(--sys-color-state-disabled)}@media (forced-colors: active){.dt-range-input{forced-color-adjust:none}}\n/*# sourceURL=slider.css */\n"},xe={cssContent:"div{display:inline-flex;height:14px;align-items:center;vertical-align:middle;white-space:nowrap;padding:1px 4px;text-align:left;font-size:11px;line-height:normal;font-weight:bold;text-shadow:none;color:var(--sys-color-inverse-on-surface);border-radius:7px}div.verbose{background-color:var(--sys-color-token-attribute-value)}:host-context(.theme-with-dark-background) div.verbose{background-color:var(--sys-color-token-tag)}div.info{background-color:var(--sys-color-token-meta)}div.warning{background-color:var(--sys-color-token-attribute)}:host-context(.theme-with-dark-background) div.warning{background-color:var(--sys-color-token-attribute-value)}div.error{background-color:var(--sys-color-error-bright)}\n/*# sourceURL=smallBubble.css */\n"},Ee={cssContent:".text-button{margin:2px;height:24px;font-size:12px;border:1px solid var(--sys-color-tonal-outline);border-radius:12px;padding:0 12px;font-weight:500;color:var(--sys-color-primary);background-color:var(--sys-color-cdt-base-container);flex:none;white-space:nowrap}.text-button:disabled{opacity:38%}.text-button:not(:disabled):focus,\n.text-button:not(:disabled):hover,\n.text-button:not(:disabled):active{background-color:var(--sys-color-state-hover-on-subtle)}.text-button:not(:disabled):not(.primary-button):focus-visible{outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary);background-color:var(--sys-color-cdt-base-container)}.text-button:not(:disabled):not(.running):focus,\n.text-button:not(:disabled):not(.running):hover,\n.text-button:not(:disabled):not(.running):active{color:var(--sys-color-primary)}.text-button.link-style,\n.text-button.link-style:hover,\n.text-button.link-style:active{background:none;border:none;outline:none;border-radius:2px;margin:0;padding:0!important;font:inherit;cursor:pointer;height:unset}.text-button.primary-button,\n.text-button.primary-button:not(:disabled):focus{background-color:var(--sys-color-primary);border:none;color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):active{background-color:color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-ripple-primary) 32%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):hover{background-color:color-mix(in sRGB,var(--sys-color-primary),var(--sys-color-state-hover-on-prominent) 6%);color:var(--sys-color-on-primary)}.text-button.primary-button:not(:disabled):focus-visible{background-color:var(--sys-color-primary);outline-offset:2px;outline:2px solid var(--sys-color-state-focus-ring);color:var(--sys-color-on-primary)}@media (forced-colors: active){.text-button{background-color:ButtonFace;color:ButtonText;border-color:ButtonText}.text-button:disabled{forced-color-adjust:none;opacity:100%;background:ButtonFace;border-color:GrayText;color:GrayText}.text-button:not(:disabled):focus-visible{forced-color-adjust:none;background-color:ButtonFace;color:Highlight!important;border-color:Highlight;outline:2px solid ButtonText;box-shadow:var(--legacy-focus-ring-active-shadow)}.text-button:not(:disabled):hover,\n  .text-button:not(:disabled):active{forced-color-adjust:none;background-color:Highlight;color:HighlightText!important;box-shadow:var(--sys-color-primary)}.text-button.primary-button{forced-color-adjust:none;background-color:Highlight;color:HighlightText;border:1px solid Highlight}.text-button.primary-button:not(:disabled):focus-visible{background-color:Highlight;color:HighlightText!important;border-color:ButtonText}.text-button.primary-button:not(:disabled):hover,\n  .text-button.primary-button:not(:disabled):active{background-color:HighlightText;color:Highlight!important;border-color:Highlight}}\n/*# sourceURL=textButton.css */\n"},Ie={cssContent:":root{--color-primary-old:rgb(26 115 232);--color-primary-variant:rgb(66 133 244);--color-background:rgb(***********);--color-background-inverted:rgb(0 0 0);--color-background-inverted-opacity-0:rgb(0 0 0/0%);--color-background-inverted-opacity-2:rgb(0 0 0/2%);--color-background-inverted-opacity-30:rgb(0 0 0/30%);--color-background-inverted-opacity-50:rgb(0 0 0/50%);--color-background-opacity-50:rgb(***********/50%);--color-background-opacity-80:rgb(***********/80%);--color-background-elevation-0:rgb(248 249 249);--color-background-elevation-1:rgb(241 243 244);--color-background-elevation-2:rgb(222 225 230);--color-background-elevation-dark-only:var(--color-background);--color-background-highlight:rgb(218 220 224);--divider-line:rgb(0 0 0/10%);--color-background-hover-overlay:rgb(56 121 217/10%);--color-selection-highlight:rgb(56 121 217/30%);--color-selection-highlight-border:rgb(16 81 177);--color-match-highlight:rgb(56 121 217/20%);--color-text-primary:rgb(32 33 36);--color-text-secondary:rgb(95 99 104);--color-text-secondary-selected:rgb(***********);--color-text-disabled:rgb(***********);--color-details-hairline:rgb(***********);--color-details-hairline-light:rgb(***********);--color-accent-red:rgb(217 48 37);--color-red:rgb(238 68 47);--color-accent-green:rgb(24 128 56);--color-accent-green-background:rgb(24 128 56/10%);--color-green:rgb(99 172 190);--color-link:var(--color-primary-old);--color-syntax-1:rgb(200 0 0);--color-syntax-2:rgb(136 18 128);--color-syntax-3:rgb(26 26 166);--color-syntax-4:rgb(153 69 0);--color-syntax-5:rgb(***********);--color-syntax-6:rgb(35 110 37);--color-syntax-7:rgb(48 57 66);--color-syntax-8:rgb(***********);--drop-shadow:0 0 0 1px rgb(0 0 0/5%),0 2px 4px rgb(0 0 0/20%),0 2px 6px rgb(0 0 0/10%);--drop-shadow-depth-1:0 1px 2px rgb(60 64 67/30%),0 1px 3px 1px rgb(60 64 67/15%);--drop-shadow-depth-2:0 1px 2px rgb(60 64 67/30%),0 2px 6px 2px rgb(60 64 67/15%);--drop-shadow-depth-3:0 4px 8px 3px rgb(60 64 67/15%),0 1px 3px rgb(60 64 67/30%);--drop-shadow-depth-4:0 6px 10px 4px rgb(60 64 67/15%),0 2px 3px rgb(60 64 67/30%);--drop-shadow-depth-5:0 8px 12px 6px rgb(60 64 67/15%),0 4px 4px rgb(60 64 67/30%);--box-shadow-outline-color:rgb(0 0 0/50%);--color-scrollbar-mac:rgb(***********/60%);--color-scrollbar-mac-hover:rgb(64 64 64/60%);--color-scrollbar-other:rgb(0 0 0/50%);--color-scrollbar-other-hover:rgb(0 0 0/50%);--lighthouse-red:rgb(255 78 67);--lighthouse-orange:rgb(255 164 0);--lighthouse-green:rgb(12 206 106);--issue-color-red:rgb(235 57 65);--issue-color-yellow:rgb(242 153 0);--issue-color-blue:rgb(26 115 232);--input-outline:rgb(***********);--color-error-text:#f00;--color-error-border:hsl(0deg 100% 92%);--color-error-background:hsl(0deg 100% 97%);--color-image-preview-background:rgb(***********);--color-input-outline:rgb(218 220 224);--color-input-outline-active:rgb(26 115 232);--color-input-outline-error:rgb(217 48 37);--color-input-outline-disabled:rgba(***********/20%);--color-input-text-disabled:rgba(***********/50%);--color-button-outline-focus:rgb(26 115 232/50%);--color-button-primary-background-hovering:rgb(77 134 225/100%);--color-button-primary-background-pressed:rgb(88 132 205);--color-button-primary-text:rgb(***********);--color-button-primary-text-hover:rgb(218 220 224);--color-button-secondary-background-hovering:rgb(26 115 232/10%);--color-button-secondary-background-pressed:rgb(26 92 178/25%);--color-button-secondary-border:rgb(218 220 224);--color-iconbutton-hover:rgb(0 0 0/10%);--color-iconbutton-pressed:rgb(0 0 0/15%);--color-ic-file-document:rgb(39 116 240);--color-ic-file-image:rgb(46 184 83);--color-ic-file-font:rgb(18 192 226);--color-ic-file-script:rgb(240 179 0);--color-ic-file-stylesheet:rgb(174 82 255);--color-ic-file-webbundle:rgb(***********);--color-ic-file-default:rgb(***********);--color-token-variable:inherit;--color-token-property:inherit;--color-token-type:rgb(0 136 119);--color-token-definition:var(--color-token-attribute-value);--color-token-variable-special:rgb(0 85 170);--color-token-builtin:rgb(50 0 170);--color-token-keyword:rgb(171 13 144);--color-token-number:rgb(50 0 255);--color-token-string:rgb(170 17 17);--color-token-string-special:rgb(200 0 0);--color-token-atom:rgb(34 17 153);--color-token-tag:rgb(136 18 128);--color-token-attribute:rgb(153 69 0);--color-token-attribute-value:rgb(26 26 168);--color-token-comment:rgb(0 117 0);--color-token-meta:rgb(85 85 85);--color-token-deleted:rgb(221 68 68);--color-token-inserted:rgb(34 153 34);--color-secondary-cursor:#c0c0c0;--color-line-number:hsl(0deg 0% 46%);--color-matching-bracket-underline:rgb(0 0 0/50%);--color-matching-bracket-background:rgb(0 0 0/7%);--color-nonmatching-bracket-underline:rgb(255 0 0/50%);--color-nonmatching-bracket-background:rgb(255 0 0/7%);--color-editor-selection:#cfe8fc;--color-editor-selection-blurred:#e0e0e0;--color-trailing-whitespace:rgb(255 0 0/5%);--color-selected-option:#fff;--color-selected-option-background:#1a73e8;--color-selected-option-outline:rgb(***********/50%);--color-highlighted-line:rgb(255 255 0/50%);--color-completion-hover:rgb(56 121 217/10%);--color-search-match-border:rgb(***********);--color-selected-search-match:var(--sys-color-on-surface);--color-selected-search-match-background:rgb(241 234 0);--color-continue-to-location:var(--ref-palette-blue90);--color-continue-to-location-hover:var(--ref-palette-blue80);--color-continue-to-location-hover-border:var(--ref-palette-blue70);--color-continue-to-location-async:var(--ref-palette-green90);--color-continue-to-location-async-hover:var(--ref-palette-green80);--color-continue-to-location-async-hover-border:var(--ref-palette-green70);--color-evaluated-expression:var(--ref-palette-yellow95);--color-evaluated-expression-border:var(--ref-palette-yellow70);--color-variable-values:var(--ref-palette-orange90);--color-non-breakable-line:var(--ref-palette-neutral80);--color-on-surface:rgb(32 33 36);--color-on-surface-variant:rgb(110 110 110);--color-primary:rgb(11 87 208);--color-primary-bright:rgb(27 110 243);--color-on-primary:rgb(***********);--color-orange:rgb(169 91 18);--color-orange-bright:rgb(232 143 33);--color-tertiary:rgb(20 108 46);--color-tertiary-bright:rgb(25 134 57);--color-error:rgb(179 38 30);--color-error-bright:rgb(220 54 46);--color-purple:rgb(140 30 211);--color-purple-bright:rgb(167 67 238);--color-turqoise:rgb(0 103 127);--color-turqoise-bright:rgb(0 152 196);--color-yellow:rgb(121 89 0);--color-yellow-bright:rgb(186 130 14);--sys-color-on-surface:var(--ref-palette-neutral10);--sys-color-on-surface-subtle:var(--ref-palette-neutral30);--sys-color-on-surface-secondary:var(--ref-palette-neutral30);--sys-color-on-surface-primary:var(--ref-palette-primary10);--sys-color-surface:var(--ref-palette-neutral99);--sys-color-surface-variant:var(--ref-palette-neutral-variant90);--sys-color-tonal-container:var(--ref-palette-primary90);--sys-color-on-tonal-container:var(--ref-palette-primary10);--sys-color-tertiary-container:var(--ref-palette-tertiary90);--sys-color-on-tertiary-container:var(--ref-palette-tertiary10);--sys-color-error-container:var(--ref-palette-error90);--sys-color-on-error-container:var(--ref-palette-error10);--sys-color-neutral-container:var(--ref-palette-neutral95);--sys-color-omnibox-container:var(--sys-color-surface4);--sys-color-primary:var(--ref-palette-primary40);--sys-color-on-primary:var(--ref-palette-primary100);--sys-color-secondary:var(--ref-palette-secondary40);--sys-color-on-secondary:var(--ref-palette-secondary100);--sys-color-tertiary:var(--ref-palette-tertiary40);--sys-color-on-tertiary:var(--ref-palette-tertiary100);--sys-color-error:var(--ref-palette-error40);--sys-color-on-error:var(--ref-palette-error100);--sys-color-base:var(--ref-palette-neutral98);--sys-color-base-container:var(--sys-color-surface4);--sys-color-base-container-elevated:var(--ref-palette-neutral100);--sys-color-on-base:var(--ref-palette-neutral10);--sys-color-on-base-divider:var(--ref-palette-primary90);--sys-color-inverse-surface:var(--ref-palette-neutral20);--sys-color-inverse-primary:var(--ref-palette-primary80);--sys-color-inverse-on-surface:var(--ref-palette-neutral95);--sys-color-outline:var(--ref-palette-neutral-variant50);--sys-color-tonal-outline:var(--ref-palette-primary80);--sys-color-neutral-outline:var(--ref-palette-neutral80);--sys-color-yellow-outline:var(--ref-palette-yellow70);--sys-color-error-outline:var(--ref-palette-error80);--sys-color-divider:var(--ref-palette-primary90);--sys-color-divider-on-tonal-container:var(--ref-palette-primary80);--sys-color-divider-prominent:var(--ref-palette-primary70);--sys-color-state-hover-on-prominent:color-mix(in sRGB,var(--ref-palette-neutral99) 10%,transparent);--sys-color-state-hover-on-subtle:color-mix(in sRGB,var(--ref-palette-neutral10) 6%,transparent);--sys-color-state-hover-dim-blend-protection:rgb(6 46 111/18%);--sys-color-state-hover-bright-blend-protection:rgb(31 31 31/6%);--sys-color-state-ripple-neutral-on-prominent:color-mix(in sRGB,var(--ref-palette-neutral99) 16%,transparent);--sys-color-state-ripple-neutral-on-subtle:color-mix(in sRGB,var(--ref-palette-neutral10) 8%,transparent);--sys-color-state-ripple-primary:color-mix(in sRGB,var(--ref-palette-primary70) 32%,transparent);--sys-color-state-focus-ring:var(--ref-palette-primary40);--sys-color-state-focus-select:var(--ref-palette-primary80);--sys-color-state-focus-highlight:rgb(31 31 31/6%);--sys-color-state-disabled:rgb(31 31 31/38%);--sys-color-state-disabled-container:rgb(31 31 31/12%);--sys-color-state-header-hover:var(--ref-palette-primary80);--sys-color-state-on-header-hover:var(--ref-palette-primary20);--sys-color-surface5:color-mix(in sRGB,var(--ref-palette-primary40) 14%,var(--ref-palette-neutral99));--sys-color-surface4:color-mix(in sRGB,var(--ref-palette-primary40) 12%,var(--ref-palette-neutral99));--sys-color-surface3:color-mix(in sRGB,var(--ref-palette-primary40) 11%,var(--ref-palette-neutral99));--sys-color-surface2:color-mix(in sRGB,var(--ref-palette-primary40) 8%,var(--ref-palette-neutral99));--sys-color-surface1:color-mix(in sRGB,var(--ref-palette-primary40) 5%,var(--ref-palette-neutral99));--sys-color-primary-bright:var(--ref-palette-primary50);--sys-color-blue-bright:var(--ref-palette-blue50);--sys-color-green-bright:var(--ref-palette-green60);--sys-color-error-bright:var(--ref-palette-error50);--sys-color-orange-bright:var(--ref-palette-orange60);--sys-color-yellow-bright:var(--ref-palette-yellow60);--sys-color-cyan-bright:var(--ref-palette-cyan50);--sys-color-purple-bright:var(--ref-palette-purple50);--sys-color-neutral-bright:var(--ref-palette-neutral70);--sys-color-pink-bright:var(--ref-palette-pink60);--sys-color-blue:var(--ref-palette-blue40);--sys-color-on-blue:var(--ref-palette-blue100);--sys-color-green:var(--ref-palette-green40);--sys-color-on-green:var(--ref-palette-green100);--sys-color-orange:var(--ref-palette-orange40);--sys-color-on-orange:var(--ref-palette-orange100);--sys-color-yellow:var(--ref-palette-yellow40);--sys-color-on-yellow:var(--ref-palette-yellow100);--sys-color-cyan:var(--ref-palette-cyan40);--sys-color-on-cyan:var(--ref-palette-cyan100);--sys-color-purple:var(--ref-palette-purple40);--sys-color-on-purple:var(--ref-palette-purple100);--sys-color-pink:var(--ref-palette-pink40);--sys-color-on-pink:var(--ref-palette-pink100);--sys-color-yellow-container:var(--ref-palette-yellow90);--sys-color-on-yellow-container:var(--ref-palette-yellow10);--sys-color-cdt-base:var(--sys-color-base-container);--sys-color-cdt-base-container:var(--ref-palette-neutral99);--sys-color-surface-yellow:rgb(254 246 213/100%);--sys-color-surface-yellow-high:rgb(253 240 185/100%);--sys-color-surface-error:rgb(252 235 235/100%);--sys-color-surface-green:rgb(219 243 226/100%);--sys-color-on-surface-yellow:var(--ref-palette-yellow20);--sys-color-on-surface-error:var(--ref-palette-error30);--sys-color-on-surface-green:var(--ref-palette-green20);--sys-color-token-variable:var(--sys-color-on-surface);--sys-color-token-property:var(--sys-color-on-surface);--sys-color-token-property-special:var(--ref-palette-error50);--sys-color-token-type:var(--ref-palette-green50);--sys-color-token-definition:var(--ref-palette-blue30);--sys-color-token-variable-special:var(--ref-palette-blue30);--sys-color-token-builtin:var(--ref-palette-blue20);--sys-color-token-keyword:var(--ref-palette-pink40);--sys-color-token-number:var(--ref-palette-blue40);--sys-color-token-string:var(--ref-palette-error40);--sys-color-token-string-special:var(--ref-palette-error50);--sys-color-token-atom:var(--ref-palette-blue20);--sys-color-token-tag:var(--ref-palette-pink30);--sys-color-token-attribute:var(--ref-palette-orange40);--sys-color-token-attribute-value:var(--ref-palette-blue30);--sys-color-token-comment:var(--ref-palette-green40);--sys-color-token-meta:var(--ref-palette-neutral60);--sys-color-token-deleted:var(--ref-palette-error50);--sys-color-token-inserted:var(--ref-palette-green60);--sys-color-token-pseudo-element:var(--ref-palette-blue40);--sys-color-token-subtle:var(--ref-palette-neutral60);--app-color-strokestyle:rgb(11 87 208/10%);--app-color-selected-progress-bar:var(--ref-palette-primary80);--app-border-selected-progress-bar:var(--ref-palette-primary70);--app-color-loading:var(--ref-palette-blue70);--app-color-loading-children:var(--ref-palette-blue80);--app-color-scripting:var(--ref-palette-yellow70);--app-color-scripting-children:var(--ref-palette-yellow80);--app-color-rendering:var(--ref-palette-purple70);--app-color-rendering-children:var(--ref-palette-purple80);--app-color-painting:var(--ref-palette-green70);--app-color-painting-children:var(--ref-palette-green80);--app-color-messaging:var(--ref-palette-cyan70);--app-color-messaging-children:var(--ref-palette-cyan80);--app-color-task:var(--ref-palette-neutral80);--app-color-task-children:var(--ref-palette-neutral90);--app-color-system:var(--ref-palette-neutral80);--app-color-system-children:var(--ref-palette-neutral90);--app-color-idle:var(--ref-palette-neutral90);--app-color-idle-children:var(--ref-palette-neutral100);--app-color-async:var(--ref-palette-error60);--app-color-async-children:var(--ref-palette-error70);--app-color-other:var(--ref-palette-neutral87);--app-color-doc:var(--ref-palette-blue60);--app-color-css:var(--ref-palette-purple60);--app-color-image:var(--ref-palette-green80);--app-color-media:var(--ref-palette-green60);--app-color-font:var(--ref-palette-cyan60);--app-color-wasm:var(--ref-palette-indigo60);--app-color-active-breadcrumb:var(--ref-palette-blue40);--sys-color-gradient-primary:var(--ref-palette-primary90);--sys-color-gradient-tertiary:var(--ref-palette-tertiary95);--app-color-coverage-used:var(--ref-palette-neutral80);--app-color-coverage-unused:var(--sys-color-error-bright);--app-color-toolbar-background:var(--sys-color-surface4);--app-color-menu-background:var(--sys-color-surface);--app-color-navigation-drawer-label-selected:var(--sys-color-on-surface);--app-color-navigation-drawer-background-selected:var(--ref-palette-primary95);--app-color-performance-bad:var(--color-error-bright);--app-color-performance-ok:var(--color-orange-bright);--app-color-performance-good:var(--sys-color-green-bright);--app-color-performance-sidebar-label:var(--ref-palette-purple80)}:root.baseline-default{--sys-color-surface5:color-mix(in sRGB,#6991d6 14%,var(--ref-palette-neutral100));--sys-color-surface4:color-mix(in sRGB,#6991d6 12%,var(--ref-palette-neutral100));--sys-color-surface3:color-mix(in sRGB,#6991d6 11%,var(--ref-palette-neutral100));--sys-color-surface2:color-mix(in sRGB,#6991d6 8%,var(--ref-palette-neutral100));--sys-color-surface1:color-mix(in sRGB,#6991d6 5%,var(--ref-palette-neutral100));--app-color-navigation-drawer-label-selected:var(--sys-color-primary);&.theme-with-dark-background{--app-color-navigation-drawer-label-selected:var(--sys-color-surface)}}:root.baseline-grayscale{--sys-color-divider:var(--ref-palette-neutral90);--sys-color-surface5:color-mix(in sRGB,var(--ref-palette-neutral40) 14%,var(--ref-palette-neutral100));--sys-color-surface4:color-mix(in sRGB,var(--ref-palette-neutral40) 12%,var(--ref-palette-neutral100));--sys-color-surface3:color-mix(in sRGB,var(--ref-palette-neutral40) 11%,var(--ref-palette-neutral100));--sys-color-surface2:color-mix(in sRGB,var(--ref-palette-neutral40) 8%,var(--ref-palette-neutral100));--sys-color-surface1:color-mix(in sRGB,var(--ref-palette-neutral40) 5%,var(--ref-palette-neutral100))}:root.baseline-default,\n:root.baseline-grayscale{--sys-color-base:var(--ref-palette-neutral100);--sys-color-surface:var(--ref-palette-neutral100);--sys-color-cdt-base-container:var(--ref-palette-neutral100);&.theme-with-dark-background{--sys-color-surface5:color-mix(in sRGB,#d1e1ff 14%,var(--ref-palette-neutral10));--sys-color-surface4:color-mix(in sRGB,#d1e1ff 12%,var(--ref-palette-neutral10));--sys-color-surface3:color-mix(in sRGB,#d1e1ff 11%,var(--ref-palette-neutral10));--sys-color-surface2:color-mix(in sRGB,#d1e1ff 8%,var(--ref-palette-neutral10));--sys-color-surface1:color-mix(in sRGB,#d1e1ff 5%,var(--ref-palette-neutral10));--sys-color-divider:var(--ref-palette-neutral40);--sys-color-base:var(--ref-palette-neutral25);--sys-color-surface:var(--ref-palette-neutral10);--sys-color-cdt-base-container:var(--ref-palette-neutral15)}}.theme-with-dark-background{--color-primary-old:rgb(***********);--color-primary-variant:rgb(102 157 246);--color-background:rgb(32 33 36);--color-background-inverted:rgb(***********);--color-background-inverted-opacity-2:rgb(***********/2%);--color-background-inverted-opacity-30:rgb(***********/30%);--color-background-inverted-opacity-50:rgb(***********/50%);--color-background-opacity-50:rgb(32 33 36/50%);--color-background-opacity-80:rgb(32 33 36/80%);--color-background-elevation-0:rgb(32 32 35);--color-background-elevation-1:rgb(41 42 45);--color-background-elevation-2:rgb(53 54 58);--color-background-elevation-dark-only:var(--color-background-elevation-1);--color-background-highlight:rgb(69 69 69);--divider-line:rgb(***********/10%);--color-background-hover-overlay:rgb(56 121 217/10%);--color-selection-highlight:rgb(251 202 70/20%);--color-selection-highlight-border:rgb(251 202 70);--color-match-highlight:rgb(56 121 217/35%);--color-text-primary:rgb(***********);--color-text-secondary:rgb(***********);--color-text-secondary-selected:rgb(***********);--color-text-disabled:rgb(***********);--color-details-hairline:rgb(73 76 80);--color-details-hairline-light:rgb(54 57 59);--color-accent-red:rgb(***********);--color-red:rgb(237 78 76);--color-accent-green:rgb(***********);--color-accent-green-background:rgb(***********/20%);--color-link:var(--color-primary-old);--color-syntax-1:rgb(53 212 199);--color-syntax-2:rgb(93 176 215);--color-syntax-2-rgb:93 176 215;--color-syntax-3:rgb(***********);--color-syntax-4:rgb(***********);--color-syntax-5:rgb(***********);--color-syntax-6:rgb(***********);--color-syntax-7:rgb(***********);--color-syntax-8:rgb(93 176 215);--drop-shadow:0 0 0 1px rgb(***********/20%),0 2px 4px 2px rgb(0 0 0/20%),0 2px 6px 2px rgb(0 0 0/10%);--drop-shadow-depth-1:0 1px 2px rgb(0 0 0/30%),0 1px 3px 1px rgb(0 0 0/15%);--drop-shadow-depth-2:0 1px 2px rgb(0 0 0/30%),0 2px 6px 2px rgb(0 0 0/15%);--drop-shadow-depth-3:0 4px 8px 3px rgb(0 0 0/15%),0 1px 3px rgb(0 0 0/30%);--drop-shadow-depth-4:0 6px 10px 4px rgb(0 0 0/15%),0 2px 3px rgb(0 0 0/30%);--drop-shadow-depth-5:0 8px 12px 6px rgb(0 0 0/15%),0 4px 4px rgb(0 0 0/30%);--box-shadow-outline-color:rgb(0 0 0/50%);--color-scrollbar-mac:rgb(51 51 51);--color-scrollbar-mac-hover:rgb(75 76 79);--color-scrollbar-other:rgb(51 51 51);--color-scrollbar-other-hover:rgb(75 76 79);--color-error-text:hsl(0deg 100% 75%);--color-error-border:rgb(92 0 0);--color-error-background:hsl(0deg 100% 8%);--color-input-outline:rgb(60 64 67);--color-input-outline-active:rgb(***********);--color-input-outline-error:rgb(***********);--color-input-outline-disabled:rgba(***********/20%);--color-input-text-disabled:rgba(***********/70%);--color-button-outline-focus:rgb(***********/75%);--color-button-primary-background-hovering:rgb(174 203 250/100%);--color-button-primary-background-pressed:rgb(210 227 252/100%);--color-button-primary-text:rgb(0 0 0);--color-button-primary-text-hover:rgb(60 61 65);--color-button-secondary-background-hovering:rgb(***********/15%);--color-button-secondary-background-pressed:rgb(***********/23%);--color-button-secondary-border:rgb(60 61 65);--color-iconbutton-hover:rgb(***********/12%);--color-iconbutton-pressed:rgb(***********/20%);--color-ic-file-document:rgb(39 116 240);--color-ic-file-image:rgb(30 142 62);--color-ic-file-font:rgb(18 181 203);--color-ic-file-script:rgb(234 134 0);--color-ic-file-stylesheet:rgb(161 66 244);--color-ic-file-webbundle:rgb(***********);--color-ic-file-default:rgb(***********);--color-token-variable:rgb(***********);--color-token-property:rgb(210 192 87);--color-token-type:var(--color-token-tag);--color-token-definition:var(--color-token-tag);--color-token-builtin:rgb(***********);--color-token-variable-special:rgb(0 85 170);--color-token-keyword:rgb(***********);--color-token-string:rgb(242 139 84);--color-token-string-special:var(--color-token-string);--color-token-atom:rgb(***********);--color-token-number:var(--color-token-atom);--color-token-comment:var(--color-syntax-6);--color-token-tag:rgb(93 176 215);--color-token-attribute:rgb(***********);--color-token-attribute-value:rgb(***********);--color-token-meta:rgb(221 251 85);--color-secondary-cursor:rgb(63 63 63);--color-line-number:rgb(***********);--color-matching-bracket-underline:rgb(***********);--color-matching-bracket-background:initial;--color-nonmatching-bracket-underline:rgb(255 26 26);--color-nonmatching-bracket-background:initial;--color-editor-selection:hsl(207deg 88% 22%);--color-editor-selection-blurred:#454545;--color-trailing-whitespace:rgb(255 0 0/5%);--color-selected-option:#fff;--color-selected-option-background:#0e639c;--color-selected-option-outline:rgb(0 0 0/50%);--color-highlighted-line:hsl(133deg 100% 30%/50%);--color-completion-hover:rgb(56 121 217/10%);--color-search-match-border:rgb(***********);--color-selected-search-match:#eee;--color-selected-search-match-background:hsl(133deg 100% 30%);--color-continue-to-location:var(--ref-palette-yellow30);--color-continue-to-location-hover:var(--ref-palette-yellow40);--color-continue-to-location-hover-border:var(--ref-palette-yellow50);--color-continue-to-location-async:var(--ref-palette-green30);--color-continue-to-location-async-hover:var(--ref-palette-green4);--color-continue-to-location-async-hover-border:var(--ref-palette-green50);--color-evaluated-expression:var(--ref-palette-yellow30);--color-evaluated-expression-border:var(--ref-palette-yellow40);--color-variable-values:var(--sys-color-surface-error);--color-non-breakable-line:var(--ref-palette-neutral30);--color-on-surface:rgb(***********);--color-on-surface-variant:rgb(145 145 145);--color-primary:rgb(168 199 250);--color-primary-bright:rgb(124 172 248);--color-on-primary:rgb(***********);--color-orange:rgb(255 184 113);--color-orange-bright:rgb(238 152 54);--color-tertiary:rgb(109 213 140);--color-tertiary-bright:rgb(55 190 95);--color-error:rgb(242 184 181);--color-error-bright:rgb(228 105 98);--color-purple:rgb(226 182 255);--color-purple-bright:rgb(209 144 255);--color-turqoise:rgb(92 213 251);--color-turqoise-bright:rgb(56 185 222);--color-yellow:rgb(251 188 5);--color-yellow-bright:rgb(217 162 0);--sys-color-on-surface:var(--ref-palette-neutral90);--sys-color-on-surface-subtle:var(--ref-palette-neutral80);--sys-color-on-surface-secondary:var(--ref-palette-neutral80);--sys-color-on-surface-primary:var(--ref-palette-primary90);--sys-color-surface:var(--ref-palette-neutral10);--sys-color-surface-variant:var(--ref-palette-neutral-variant30);--sys-color-tonal-container:var(--ref-palette-secondary30);--sys-color-on-tonal-container:var(--ref-palette-secondary90);--sys-color-tertiary-container:var(--ref-palette-tertiary30);--sys-color-on-tertiary-container:var(--ref-palette-tertiary90);--sys-color-error-container:var(--ref-palette-error30);--sys-color-on-error-container:var(--ref-palette-error90);--sys-color-neutral-container:var(--ref-palette-neutral25);--sys-color-omnibox-container:var(--ref-palette-neutral15);--sys-color-primary:var(--ref-palette-primary80);--sys-color-on-primary:var(--ref-palette-primary20);--sys-color-secondary:var(--ref-palette-secondary80);--sys-color-on-secondary:var(--ref-palette-secondary20);--sys-color-tertiary:var(--ref-palette-tertiary80);--sys-color-on-tertiary:var(--ref-palette-tertiary20);--sys-color-error:var(--ref-palette-error80);--sys-color-on-error:var(--ref-palette-error20);--sys-color-base:var(--ref-palette-secondary25);--sys-color-base-container:var(--ref-palette-neutral15);--sys-color-base-container-elevated:var(--ref-palette-neutral25);--sys-color-on-base:var(--ref-palette-neutral90);--sys-color-on-base-divider:var(--ref-palette-neutral40);--sys-color-inverse-surface:var(--ref-palette-neutral90);--sys-color-inverse-primary:var(--ref-palette-primary40);--sys-color-inverse-on-surface:var(--ref-palette-neutral10);--sys-color-outline:var(--ref-palette-neutral-variant60);--sys-color-tonal-outline:var(--ref-palette-secondary50);--sys-color-neutral-outline:var(--ref-palette-neutral50);--sys-color-yellow-outline:var(--ref-palette-yellow40);--sys-color-error-outline:var(--ref-palette-error80);--sys-color-divider:var(--ref-palette-secondary35);--sys-color-divider-on-tonal-container:var(--ref-palette-neutral40);--sys-color-divider-prominent:var(--ref-palette-neutral50);--sys-color-state-hover-on-prominent:color-mix(in sRGB,var(--ref-palette-neutral10) 6%,transparent);--sys-color-state-hover-on-subtle:color-mix(in sRGB,var(--ref-palette-neutral99) 10%,transparent);--sys-color-state-hover-dim-blend-protection:rgb(31 31 31/10%);--sys-color-state-hover-bright-blend-protection:rgb(31 31 31/16%);--sys-color-state-ripple-neutral-on-prominent:color-mix(in sRGB,var(--ref-palette-neutral10) 12%,transparent);--sys-color-state-ripple-neutral-on-subtle:color-mix(in sRGB,var(--ref-palette-neutral99) 16%,transparent);--sys-color-state-ripple-primary:color-mix(in sRGB,var(--ref-palette-primary60) 32%,transparent);--sys-color-state-focus-ring:var(--ref-palette-primary80);--sys-color-state-focus-select:var(--ref-palette-secondary50);--sys-color-state-focus-highlight:rgb(253 252 251/10%);--sys-color-state-disabled:rgb(227 227 227/38%);--sys-color-state-disabled-container:rgb(227 227 227/12%);--sys-color-state-header-hover:var(--ref-palette-secondary30);--sys-color-state-on-header-hover:var(--ref-palette-secondary90);--sys-color-surface5:color-mix(in sRGB,var(--ref-palette-primary80) 14%,var(--ref-palette-neutral10));--sys-color-surface4:color-mix(in sRGB,var(--ref-palette-primary80) 12%,var(--ref-palette-neutral10));--sys-color-surface3:color-mix(in sRGB,var(--ref-palette-primary80) 11%,var(--ref-palette-neutral10));--sys-color-surface2:color-mix(in sRGB,var(--ref-palette-primary80) 8%,var(--ref-palette-neutral10));--sys-color-surface1:color-mix(in sRGB,var(--ref-palette-primary80) 5%,var(--ref-palette-neutral10));--sys-color-primary-bright:var(--ref-palette-primary70);--sys-color-blue-bright:var(--ref-palette-blue70);--sys-color-green-bright:var(--ref-palette-green70);--sys-color-error-bright:var(--ref-palette-error60);--sys-color-orange-bright:var(--ref-palette-orange70);--sys-color-yellow-bright:var(--ref-palette-yellow70);--sys-color-cyan-bright:var(--ref-palette-cyan70);--sys-color-purple-bright:var(--ref-palette-purple70);--sys-color-neutral-bright:var(--ref-palette-neutral50);--sys-color-pink-bright:var(--ref-palette-pink70);--sys-color-blue:var(--ref-palette-blue80);--sys-color-on-blue:var(--ref-palette-blue20);--sys-color-green:var(--ref-palette-green80);--sys-color-on-green:var(--ref-palette-green20);--sys-color-orange:var(--ref-palette-orange80);--sys-color-on-orange:var(--ref-palette-orange20);--sys-color-yellow:var(--ref-palette-yellow80);--sys-color-on-yellow:var(--ref-palette-yellow20);--sys-color-cyan:var(--ref-palette-cyan80);--sys-color-on-cyan:var(--ref-palette-cyan20);--sys-color-purple:var(--ref-palette-purple80);--sys-color-on-purple:var(--ref-palette-purple20);--sys-color-pink:var(--ref-palette-pink80);--sys-color-on-pink:var(--ref-palette-pink20);--sys-color-yellow-container:var(--ref-palette-yellow30);--sys-color-on-yellow-container:var(--ref-palette-yellow90);--sys-color-cdt-base:var(--sys-color-base);--sys-color-cdt-base-container:var(--sys-color-base-container);--sys-color-surface-yellow:rgb(65 60 38/100%);--sys-color-surface-yellow-high:rgb(76 68 37/100%);--sys-color-surface-error:rgb(78 53 52/100%);--sys-color-surface-green:rgb(43 70 51/100%);--sys-color-on-surface-yellow:var(--ref-palette-yellow90);--sys-color-on-surface-error:var(--ref-palette-error90);--sys-color-on-surface-green:var(--ref-palette-green90);--sys-color-token-variable:var(--ref-palette-neutral80);--sys-color-token-property:var(--ref-palette-yellow70);--sys-color-token-property-special:var(--ref-palette-cyan80);--sys-color-token-type:var(--ref-palette-blue70);--sys-color-token-definition:var(--ref-palette-blue70);--sys-color-token-variable-special:var(--ref-palette-blue40);--sys-color-token-builtin:var(--ref-palette-blue80);--sys-color-token-keyword:var(--ref-palette-purple60);--sys-color-token-number:var(--ref-palette-green90);--sys-color-token-string:var(--ref-palette-orange70);--sys-color-token-string-special:var(--ref-palette-orange70);--sys-color-token-atom:var(--ref-palette-green90);--sys-color-token-tag:var(--ref-palette-blue70);--sys-color-token-attribute:var(--ref-palette-blue80);--sys-color-token-attribute-value:var(--ref-palette-orange70);--sys-color-token-comment:var(--ref-palette-neutral70);--sys-color-token-meta:var(--ref-palette-neutral60);--sys-color-token-deleted:var(--ref-palette-error50);--sys-color-token-inserted:var(--ref-palette-green60);--sys-color-token-pseudo-element:var(--ref-palette-pink70);--sys-color-token-subtle:var(--ref-palette-neutral60);--app-color-strokestyle:rgb(168 199 250/10%);--app-color-selected-progress-bar:var(--ref-palette-secondary40);--app-border-selected-progress-bar:var(--ref-palette-secondary50);--app-color-loading:var(--ref-palette-blue60);--app-color-loading-children:var(--ref-palette-blue50);--app-color-scripting:var(--ref-palette-yellow70);--app-color-scripting-children:var(--ref-palette-yellow60);--app-color-rendering:var(--ref-palette-purple60);--app-color-rendering-children:var(--ref-palette-purple50);--app-color-painting:var(--ref-palette-green70);--app-color-painting-children:var(--ref-palette-green60);--app-color-task:var(--ref-palette-neutral80);--app-color-task-children:var(--ref-palette-neutral70);--app-color-system:var(--ref-palette-neutral50);--app-color-system-children:var(--ref-palette-neutral40);--app-color-idle:var(--ref-palette-neutral30);--app-color-idle-children:var(--ref-palette-neutral20);--app-color-async:var(--ref-palette-error60);--app-color-async-children:var(--ref-palette-error50);--app-color-other:var(--ref-palette-neutral60);--app-color-doc:var(--ref-palette-blue60);--app-color-css:var(--ref-palette-purple60);--app-color-image:var(--ref-palette-green80);--app-color-media:var(--ref-palette-green60);--app-color-font:var(--ref-palette-cyan60);--app-color-wasm:var(--ref-palette-indigo60);--app-color-active-breadcrumb:var(--ref-palette-blue40);--sys-color-gradient-primary:var(--ref-palette-primary30);--sys-color-gradient-tertiary:var(--ref-palette-tertiary30);--app-color-coverage-used:var(--ref-palette-neutral40);--app-color-coverage-unused:var(--sys-color-error-bright);--app-color-toolbar-background:var(--sys-color-base);--app-color-menu-background:var(--sys-color-surface3);--app-color-navigation-drawer-label-selected:var(--sys-color-surface);--app-color-navigation-drawer-background-selected:var(--ref-palette-primary70)}\n/*# sourceURL=themeColors.css */\n"},Se={cssContent:":root{--ref-palette-primary0:var(--color-ref-primary0,rgb(0 0 0/100%));--ref-palette-primary10:var(--color-ref-primary10,rgb(4 30 73/100%));--ref-palette-primary20:var(--color-ref-primary20,rgb(6 46 111/100%));--ref-palette-primary30:var(--color-ref-primary30,rgb(8 66 160/100%));--ref-palette-primary40:var(--color-ref-primary40,rgb(11 87 208/100%));--ref-palette-primary50:var(--color-ref-primary50,rgb(27 110 243/100%));--ref-palette-primary60:var(--color-ref-primary60,rgb(76 141 246/100%));--ref-palette-primary70:var(--color-ref-primary70,rgb(124 172 248/100%));--ref-palette-primary80:var(--color-ref-primary80,rgb(168 199 250/100%));--ref-palette-primary90:var(--color-ref-primary90,rgb(211 227 253/100%));--ref-palette-primary95:var(--color-ref-primary95,rgb(236 243 254/100%));--ref-palette-primary99:var(--color-ref-primary99,rgb(250 251 255/100%));--ref-palette-primary100:var(--color-ref-primary100,rgb(***********/100%));--ref-palette-secondary0:var(--color-ref-secondary0,rgb(0 0 0/100%));--ref-palette-secondary10:var(--color-ref-secondary10,rgb(0 29 53/100%));--ref-palette-secondary15:var(--color-ref-secondary15,rgb(0 40 69/100%));--ref-palette-secondary20:var(--color-ref-secondary20,rgb(0 51 85/100%));--ref-palette-secondary25:var(--color-ref-secondary25,rgb(0 63 102/100%));--ref-palette-secondary30:var(--color-ref-secondary30,rgb(0 74 119/100%));--ref-palette-secondary35:var(--color-ref-secondary35,rgb(0 87 137/100%));--ref-palette-secondary40:var(--color-ref-secondary40,rgb(0 99 155/100%));--ref-palette-secondary50:var(--color-ref-secondary50,rgb(4 125 183/100%));--ref-palette-secondary60:var(--color-ref-secondary60,rgb(57 152 211/100%));--ref-palette-secondary70:var(--color-ref-secondary70,rgb(90 179 240/100%));--ref-palette-secondary80:var(--color-ref-secondary80,rgb(127 207 255/100%));--ref-palette-secondary90:var(--color-ref-secondary90,rgb(223 243 255/100%));--ref-palette-secondary95:var(--color-ref-secondary95,rgb(223 243 255/100%));--ref-palette-secondary99:var(--color-ref-secondary99,rgb(247 252 255/100%));--ref-palette-secondary100:var(--color-ref-secondary100,rgb(***********/100%));--ref-palette-tertiary0:var(--color-ref-tertiary0,rgb(0 0 0/100%));--ref-palette-tertiary10:var(--color-ref-tertiary10,rgb(7 39 17/100%));--ref-palette-tertiary20:var(--color-ref-tertiary20,rgb(10 56 24/100%));--ref-palette-tertiary30:var(--color-ref-tertiary30,rgb(15 82 35/100%));--ref-palette-tertiary40:var(--color-ref-tertiary40,rgb(20 108 46/100%));--ref-palette-tertiary50:var(--color-ref-tertiary50,rgb(25 134 57/100%));--ref-palette-tertiary60:var(--color-ref-tertiary60,rgb(30 164 70/100%));--ref-palette-tertiary70:var(--color-ref-tertiary70,rgb(55 190 95/100%));--ref-palette-tertiary80:var(--color-ref-tertiary80,rgb(109 213 140/100%));--ref-palette-tertiary90:var(--color-ref-tertiary90,rgb(196 238 208/100%));--ref-palette-tertiary95:var(--color-ref-tertiary95,rgb(231 248 237/100%));--ref-palette-tertiary99:var(--color-ref-tertiary99,rgb(242 255 238/100%));--ref-palette-tertiary100:var(--color-ref-tertiary100,rgb(***********/100%));--ref-palette-error0:var(--color-ref-error0,rgb(0 0 0/100%));--ref-palette-error10:var(--color-ref-error10,rgb(65 14 11/100%));--ref-palette-error20:var(--color-ref-error20,rgb(96 20 16/100%));--ref-palette-error30:var(--color-ref-error30,rgb(140 29 24/100%));--ref-palette-error40:var(--color-ref-error40,rgb(179 38 30/100%));--ref-palette-error50:var(--color-ref-error50,rgb(220 54 46/100%));--ref-palette-error60:var(--color-ref-error60,rgb(228 105 98/100%));--ref-palette-error70:var(--color-ref-error70,rgb(236 146 142/100%));--ref-palette-error80:var(--color-ref-error80,rgb(242 184 181/100%));--ref-palette-error90:var(--color-ref-error90,rgb(249 222 220/100%));--ref-palette-error95:var(--color-ref-error95,rgb(231 248 237/100%));--ref-palette-error99:var(--color-ref-error99,rgb(242 255 238/100%));--ref-palette-error100:var(--color-ref-error100,rgb(***********/100%));--ref-palette-neutral0:var(--color-ref-neutral0,rgb(0 0 0/100%));--ref-palette-neutral10:var(--color-ref-neutral10,rgb(31 31 31/100%));--ref-palette-neutral15:var(--color-ref-neutral15,rgb(40 40 40/100%));--ref-palette-neutral20:var(--color-ref-neutral20,rgb(48 48 48/100%));--ref-palette-neutral25:var(--color-ref-neutral25,rgb(60 60 60/100%));--ref-palette-neutral30:var(--color-ref-neutral30,rgb(71 71 71/100%));--ref-palette-neutral40:var(--color-ref-neutral40,rgb(94 94 94/100%));--ref-palette-neutral50:var(--color-ref-neutral50,rgb(117 117 117/100%));--ref-palette-neutral60:var(--color-ref-neutral60,rgb(***********/100%));--ref-palette-neutral70:var(--color-ref-neutral70,rgb(***********/100%));--ref-palette-neutral80:var(--color-ref-neutral80,rgb(199 199 199/100%));--ref-palette-neutral90:var(--color-ref-neutral90,rgb(227 227 227/100%));--ref-palette-neutral94:var(--color-ref-neutral94,rgb(239 237 237/100%));--ref-palette-neutral95:var(--color-ref-neutral95,rgb(242 242 242/100%));--ref-palette-neutral98:var(--color-ref-neutral98,rgb(250 249 248/100%));--ref-palette-neutral99:var(--color-ref-neutral99,rgb(253 252 251/100%));--ref-palette-neutral100:var(--color-ref-neutral100,rgb(***********/100%));--ref-palette-neutral-variant0:var(--color-ref-neutral-variant0,rgb(0 0 0/100%));--ref-palette-neutral-variant10:var(--color-ref-neutral-variant10,rgb(25 29 28/100%));--ref-palette-neutral-variant20:var(--color-ref-neutral-variant20,rgb(45 49 47/100%));--ref-palette-neutral-variant30:var(--color-ref-neutral-variant30,rgb(68 71 70/100%));--ref-palette-neutral-variant40:var(--color-ref-neutral-variant40,rgb(92 95 94/100%));--ref-palette-neutral-variant50:var(--color-ref-neutral-variant50,rgb(116 119 117/100%));--ref-palette-neutral-variant60:var(--color-ref-neutral-variant60,rgb(142 145 143/100%));--ref-palette-neutral-variant70:var(--color-ref-neutral-variant70,rgb(169 172 170/100%));--ref-palette-neutral-variant80:var(--color-ref-neutral-variant80,rgb(196 199 197/100%));--ref-palette-neutral-variant90:var(--color-ref-neutral-variant90,rgb(225 227 225/100%));--ref-palette-neutral-variant95:var(--color-ref-neutral-variant95,rgb(239 242 239/100%));--ref-palette-neutral-variant99:var(--color-ref-neutral-variant99,rgb(250 253 251/100%));--ref-palette-neutral-variant100:var(--color-ref-neutral-variant100,rgb(***********/100%));--ref-palette-blue0:rgb(0 0 0/100%);--ref-palette-blue10:rgb(4 30 73/100%);--ref-palette-blue20:rgb(6 46 111/100%);--ref-palette-blue30:rgb(8 66 160/100%);--ref-palette-blue40:rgb(11 87 208/100%);--ref-palette-blue50:rgb(27 110 243/100%);--ref-palette-blue60:rgb(76 141 246/100%);--ref-palette-blue70:rgb(124 172 248/100%);--ref-palette-blue80:rgb(168 199 250/100%);--ref-palette-blue90:rgb(211 227 253/100%);--ref-palette-blue95:rgb(236 243 254/100%);--ref-palette-blue99:rgb(250 251 255/100%);--ref-palette-blue100:rgb(***********/100%);--ref-palette-green0:rgb(0 0 0/100%);--ref-palette-green10:rgb(7 39 17/100%);--ref-palette-green20:rgb(10 56 24/100%);--ref-palette-green30:rgb(15 82 35/100%);--ref-palette-green40:rgb(20 108 46/100%);--ref-palette-green50:rgb(25 134 57/100%);--ref-palette-green60:rgb(30 164 70/100%);--ref-palette-green70:rgb(55 190 95/100%);--ref-palette-green80:rgb(109 213 140/100%);--ref-palette-green90:rgb(196 238 208/100%);--ref-palette-green95:rgb(231 248 237/100%);--ref-palette-green99:rgb(242 255 238/100%);--ref-palette-green100:rgb(***********/100%);--ref-palette-orange0:rgb(0 0 0/100%);--ref-palette-orange10:rgb(53 16 2/100%);--ref-palette-orange20:rgb(85 32 5/100%);--ref-palette-orange30:rgb(121 50 11/100%);--ref-palette-orange40:rgb(159 67 18/100%);--ref-palette-orange50:rgb(198 85 26/100%);--ref-palette-orange60:rgb(232 110 48/100%);--ref-palette-orange70:rgb(254 141 89/100%);--ref-palette-orange80:rgb(254 183 150/100%);--ref-palette-orange90:rgb(255 220 204/100%);--ref-palette-orange95:rgb(255 236 230/100%);--ref-palette-orange99:rgb(255 251 255/100%);--ref-palette-orange100:rgb(***********/100%);--ref-palette-yellow0:rgb(0 0 0/100%);--ref-palette-yellow10:rgb(36 26 0/100%);--ref-palette-yellow20:rgb(62 47 0/100%);--ref-palette-yellow30:rgb(92 67 0/100%);--ref-palette-yellow40:rgb(151 103 0/100%);--ref-palette-yellow50:rgb(202 138 4/100%);--ref-palette-yellow60:rgb(234 179 8/100%);--ref-palette-yellow70:rgb(250 204 21/100%);--ref-palette-yellow80:rgb(253 224 71/100%);--ref-palette-yellow90:rgb(253 243 170/100%);--ref-palette-yellow95:rgb(252 248 210/100%);--ref-palette-yellow99:rgb(255 251 235/100%);--ref-palette-yellow100:rgb(***********/100%);--ref-palette-cyan0:rgb(0 0 0/100%);--ref-palette-cyan10:rgb(0 31 40/100%);--ref-palette-cyan20:rgb(0 53 67/100%);--ref-palette-cyan30:rgb(0 78 96/100%);--ref-palette-cyan40:rgb(0 103 127/100%);--ref-palette-cyan50:rgb(0 130 159/100%);--ref-palette-cyan60:rgb(0 157 193/100%);--ref-palette-cyan70:rgb(56 185 222/100%);--ref-palette-cyan80:rgb(92 213 251/100%);--ref-palette-cyan90:rgb(183 234 255/100%);--ref-palette-cyan95:rgb(221 245 255/100%);--ref-palette-cyan99:rgb(249 253 255/100%);--ref-palette-cyan100:rgb(***********/100%);--ref-palette-purple0:rgb(0 0 0/100%);--ref-palette-purple10:rgb(47 0 77/100%);--ref-palette-purple20:rgb(77 0 122/100%);--ref-palette-purple30:rgb(110 0 171/100%);--ref-palette-purple40:rgb(140 30 211/100%);--ref-palette-purple50:rgb(167 67 238/100%);--ref-palette-purple60:rgb(191 103 255/100%);--ref-palette-purple70:rgb(209 144 255/100%);--ref-palette-purple80:rgb(226 182 255/100%);--ref-palette-purple90:rgb(243 218 255/100%);--ref-palette-purple95:rgb(251 236 255/100%);--ref-palette-purple99:rgb(255 251 255/100%);--ref-palette-purple100:rgb(***********/100%);--ref-palette-pink0:rgb(0 0 0/100%);--ref-palette-pink10:rgb(62 0 29/100%);--ref-palette-pink20:rgb(101 0 51/100%);--ref-palette-pink30:rgb(142 0 75/100%);--ref-palette-pink40:rgb(185 0 99/100%);--ref-palette-pink50:rgb(223 35 125/100%);--ref-palette-pink60:rgb(255 72 150/100%);--ref-palette-pink70:rgb(255 132 175/100%);--ref-palette-pink80:rgb(255 177 200/100%);--ref-palette-pink90:rgb(255 217 226/100%);--ref-palette-pink95:rgb(255 236 240/100%);--ref-palette-pink99:rgb(255 251 255/100%);--ref-palette-pink100:rgb(***********/100%);--ref-palette-indigo0:rgb(0 0 0/100%);--ref-palette-indigo10:rgb(23 0 101/100%);--ref-palette-indigo20:rgb(41 0 159/100%);--ref-palette-indigo30:rgb(63 28 203/100%);--ref-palette-indigo40:rgb(88 64 227/100%);--ref-palette-indigo50:rgb(113 93 253/100%);--ref-palette-indigo60:rgb(141 127 255/100%);--ref-palette-indigo70:rgb(170 160 255/100%);--ref-palette-indigo80:rgb(199 191 255/100%);--ref-palette-indigo90:rgb(228 223 255/100%);--ref-palette-indigo95:rgb(243 238 255/100%);--ref-palette-indigo99:rgb(255 251 255/100%);--ref-palette-indigo100:rgb(***********/100%)}\n/*# sourceURL=tokens.css */\n"};const ke={close:"Close",dockToRight:"Dock to right",dockToBottom:"Dock to bottom",dockToLeft:"Dock to left",undockIntoSeparateWindow:"Undock into separate window",devtoolsUndocked:"DevTools is undocked",devToolsDockedTo:"DevTools is docked to {PH1}"},Ce=t.i18n.registerUIStrings("ui/legacy/DockController.ts",ke),Te=t.i18n.getLocalizedString.bind(void 0,Ce);let Le;class Me extends e.ObjectWrapper.ObjectWrapper{canDockInternal;closeButton;currentDockStateSetting;lastDockStateSetting;dockSideInternal=void 0;titles;savedFocus;constructor(t){if(super(),this.canDockInternal=t,this.closeButton=new Jn(Te(ke.close),"cross"),this.closeButton.element.setAttribute("jslog",`${s.close().track({click:!0})}`),this.closeButton.element.classList.add("close-devtools"),this.closeButton.addEventListener("Click",r.InspectorFrontendHost.InspectorFrontendHostInstance.closeWindow.bind(r.InspectorFrontendHost.InspectorFrontendHostInstance)),this.currentDockStateSetting=e.Settings.Settings.instance().moduleSetting("currentDockState"),this.lastDockStateSetting=e.Settings.Settings.instance().createSetting("last-dock-state","bottom"),!t)return this.dockSideInternal="undocked",void this.closeButton.setVisible(!1);this.currentDockStateSetting.addChangeListener(this.dockSideChanged,this),-1===Pe.indexOf(this.currentDockStateSetting.get())&&this.currentDockStateSetting.set("right"),-1===Pe.indexOf(this.lastDockStateSetting.get())&&this.currentDockStateSetting.set("bottom")}static instance(e={forceNew:null,canDock:!1}){const{forceNew:t,canDock:n}=e;return Le&&!t||(Le=new Me(n)),Le}initialize(){this.canDockInternal&&(this.titles=[Te(ke.dockToRight),Te(ke.dockToBottom),Te(ke.dockToLeft),Te(ke.undockIntoSeparateWindow)],this.dockSideChanged())}dockSideChanged(){this.setDockSide(this.currentDockStateSetting.get()),setTimeout(this.announceDockLocation.bind(this),2e3)}dockSide(){return this.dockSideInternal}canDock(){return this.canDockInternal}isVertical(){return"right"===this.dockSideInternal||"left"===this.dockSideInternal}setDockSide(e){if(-1===Pe.indexOf(e)&&(e=Pe[0]),this.dockSideInternal===e)return;void 0!==this.dockSideInternal&&document.body.classList.remove(this.dockSideInternal),document.body.classList.add(e),this.dockSideInternal&&this.lastDockStateSetting.set(this.dockSideInternal),this.savedFocus=n.DOMUtilities.deepActiveElement(document);const t={from:this.dockSideInternal,to:e};this.dispatchEventToListeners("BeforeDockSideChanged",t),console.timeStamp("DockController.setIsDocked"),this.dockSideInternal=e,this.currentDockStateSetting.set(e),r.InspectorFrontendHost.InspectorFrontendHostInstance.setIsDocked("undocked"!==e,this.setIsDockedResponse.bind(this,t)),this.closeButton.setVisible("undocked"!==this.dockSideInternal),this.dispatchEventToListeners("DockSideChanged",t)}setIsDockedResponse(e){this.dispatchEventToListeners("AfterDockSideChanged",e),this.savedFocus&&(this.savedFocus.focus(),this.savedFocus=null)}toggleDockSide(){if(this.lastDockStateSetting.get()===this.currentDockStateSetting.get()){const e=Pe.indexOf(this.currentDockStateSetting.get())||0;this.lastDockStateSetting.set(Pe[(e+1)%Pe.length])}this.setDockSide(this.lastDockStateSetting.get())}announceDockLocation(){"undocked"===this.dockSideInternal?mr(Te(ke.devtoolsUndocked)):mr(Te(ke.devToolsDockedTo,{PH1:this.dockSideInternal||""}))}}const Pe=["right","bottom","left","undocked"];let De;class Ae{static instance(e={forceNew:null}){const{forceNew:t}=e;return De&&!t||(De=new Ae),De}item(){return Me.instance().closeButton}}var Re=Object.freeze({__proto__:null,DockController:Me,ToggleDockActionDelegate:class{handleAction(e,t){return Me.instance().toggleDockSide(),!0}},CloseButtonProvider:Ae}),Be={cssContent:".infobar{color:var(--sys-color-on-surface);display:flex;flex:auto;flex-direction:column;position:relative;padding:6px;min-width:fit-content}.infobar:focus{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:-2px}.infobar-warning{background-color:var(--sys-color-surface-yellow);color:var(--sys-color-on-surface-yellow)}.infobar-error{--override-infobar-error-background:var(--sys-color-surface-error);--override-infobar-error-text:var(--sys-color-on-surface-error);background-color:var(--override-infobar-error-background);color:var(--override-infobar-error-text)}.infobar-main-row{display:flex;flex-direction:row;justify-content:flex-start;min-height:25px}.infobar-info-container{display:flex;align-items:center;flex-grow:1;flex-wrap:wrap}.infobar-info-message{display:flex;margin:5px 0}.infobar-info-text{display:flex;align-items:center;margin:0 4px}.infobar-details-rows{padding:5px 5px 0}.infobar-details-row{display:flex;flex-direction:column;line-height:18px;padding-bottom:6px}.infobar-close-container{display:flex;flex-shrink:0;align-items:center}.infobar-close-container > .infobar-button.link-style{margin:4px}.infobar-selectable{user-select:text}.infobar-button{color:var(--sys-color-token-subtle);padding:0 4px}.info-icon{mask-image:var(--image-file-info);background-color:var(--icon-info)}.warning-icon{mask-image:var(--image-file-warning);background-color:var(--icon-warning)}.error-icon{mask-image:var(--image-file-cross-circle);background-color:var(--icon-error)}.issue-icon{mask-image:var(--image-file-issue-text-filled);background-color:var(--icon-info)}.icon{mask-size:20px 20px;width:20px;height:20px;flex-shrink:0}.devtools-link.text-button:hover,\n.devtools-link.text-button:focus,\n.devtools-link.text-button:active{background-color:transparent;box-shadow:none}\n/*# sourceURL=infobar.css */\n"};const ze={dontShowAgain:"Don't show again",showMore:"Show more",close:"Close"},Oe=t.i18n.registerUIStrings("ui/legacy/Infobar.ts",ze),Fe=t.i18n.getLocalizedString.bind(void 0,Oe);class He{element;shadowRoot;contentElement;mainRow;detailsRows;hasDetails;detailsMessage;infoContainer;infoMessage;infoText;actionContainer;disableSetting;closeContainer;toggleElement;closeButton;closeCallback;#e=null;parentView;constructor(e,t,n,i,r=!0,o){if(this.element=document.createElement("div"),o&&this.element.setAttribute("jslog",`${s.dialog(o).track({resize:!0,keydown:"Enter|Escape"})}`),this.element.classList.add("flex-none"),this.shadowRoot=ds(this.element,{cssFile:Be,delegatesFocus:void 0}),this.contentElement=this.shadowRoot.createChild("div","infobar infobar-"+e),this.mainRow=this.contentElement.createChild("div","infobar-main-row"),this.detailsRows=this.contentElement.createChild("div","infobar-details-rows hidden"),this.hasDetails=!1,this.detailsMessage="",this.infoContainer=this.mainRow.createChild("div","infobar-info-container"),this.infoMessage=this.infoContainer.createChild("div","infobar-info-message"),this.infoMessage.createChild("div",e+"-icon icon"),this.infoText=this.infoMessage.createChild("div","infobar-info-text"),this.infoText.textContent=t,ks(this.infoText),this.actionContainer=this.infoContainer.createChild("div","infobar-info-actions"),n){this.contentElement.setAttribute("role","group");for(const e of n){const t=this.actionCallbackFactory(e);let n="infobar-button";e.highlight&&(n+=" primary-button");const i=Vi(e.text,t,{className:n,jslogContext:e.jslogContext});e.highlight&&!this.#e&&(this.#e=i),this.actionContainer.appendChild(i)}}if(this.disableSetting=i||null,i){const e=Vi(Fe(ze.dontShowAgain),this.onDisable.bind(this),{className:"infobar-button"});this.actionContainer.appendChild(e)}this.closeContainer=this.mainRow.createChild("div","infobar-close-container"),this.toggleElement=Vi(Fe(ze.showMore),this.onToggleDetails.bind(this),{className:"hidden show-more",jslogContext:"show-more",variant:"text"}),this.toggleElement.setAttribute("role","link"),this.closeContainer.appendChild(this.toggleElement),this.closeButton=this.closeContainer.createChild("div","close-button","dt-close-button"),this.closeButton.hidden=!r,this.closeButton.setTabbable(!0),ar(this.closeButton,Fe(ze.close)),self.onInvokeElement(this.closeButton,this.dispose.bind(this)),"issue"!==e&&(this.contentElement.tabIndex=0),or(this.contentElement,t),this.contentElement.addEventListener("keydown",(e=>e.keyCode===be.Esc.code?(this.dispose(),void e.consume()):e.target===this.contentElement&&"Enter"===e.key&&this.hasDetails?(this.onToggleDetails(),void e.consume()):void 0)),this.closeCallback=null}static create(e,t,n,i,s){return i&&i.get()?null:new He(e,t,n,i,void 0,s)}dispose(){this.element.remove(),this.onResize(),this.closeCallback&&this.closeCallback.call(null)}setText(e){this.infoText.textContent=e,this.onResize()}setCloseCallback(e){this.closeCallback=e}setParentView(e){this.parentView=e}actionCallbackFactory(e){return e.delegate?e.dismiss?(()=>{e.delegate&&e.delegate(),this.dispose()}).bind(this):e.delegate:e.dismiss?this.dispose.bind(this):()=>{}}onResize(){this.parentView&&this.parentView.doResize()}onDisable(){this.disableSetting&&this.disableSetting.set(!0),this.dispose()}onToggleDetails(){this.detailsRows.classList.remove("hidden"),this.toggleElement.remove(),this.onResize(),mr("string"==typeof this.detailsMessage?this.detailsMessage:this.detailsMessage.textContent||""),this.#e?this.#e.focus():this.closeButton.focus()}createDetailsRowMessage(e){this.hasDetails=!0,this.detailsMessage=e,this.toggleElement.classList.remove("hidden");const t=this.detailsRows.createChild("div","infobar-details-row").createChild("span","infobar-row-message");return"string"==typeof e?t.textContent=e:t.appendChild(e),t}}var We=Object.freeze({__proto__:null,Infobar:He}),Ne={cssContent:".tabbed-pane-header-tab{height:26px;margin:0;border:none;border-left:2px solid transparent;border-right:2px solid transparent;&.selected{border-width:0 2px}& > .tabbed-pane-header-tab-icon > devtools-icon{width:14px;height:14px;color:var(--icon-warning)}}.tabbed-pane-header-contents{margin-left:0;min-width:min-content}.tabbed-pane-left-toolbar{margin-right:0!important}\n/*# sourceURL=inspectorViewTabbedPane.css */\n"};class je extends e.ObjectWrapper.ObjectWrapper{isEnabledInternal;elementsInternal;installDragOnMouseDownBound;cursorInternal;startX;startY;constructor(){super(),this.isEnabledInternal=!0,this.elementsInternal=new Set,this.installDragOnMouseDownBound=this.installDragOnMouseDown.bind(this),this.cursorInternal="nwse-resize"}isEnabled(){return this.isEnabledInternal}setEnabled(e){this.isEnabledInternal=e,this.updateElementCursors()}elements(){return[...this.elementsInternal]}addElement(e){this.elementsInternal.has(e)||(this.elementsInternal.add(e),e.addEventListener("pointerdown",this.installDragOnMouseDownBound,!1),this.updateElementCursor(e))}removeElement(e){this.elementsInternal.has(e)&&(this.elementsInternal.delete(e),e.removeEventListener("pointerdown",this.installDragOnMouseDownBound,!1),e.style.removeProperty("cursor"))}updateElementCursors(){this.elementsInternal.forEach(this.updateElementCursor.bind(this))}updateElementCursor(e){this.isEnabledInternal?(e.style.setProperty("cursor",this.cursor()),e.style.setProperty("touch-action","none")):(e.style.removeProperty("cursor"),e.style.removeProperty("touch-action"))}cursor(){return this.cursorInternal}setCursor(e){this.cursorInternal=e,this.updateElementCursors()}installDragOnMouseDown(e){const t=e.target;if(!this.elementsInternal.has(t))return!1;bi(t,this.dragStart.bind(this),(e=>{this.drag(e)}),this.dragEnd.bind(this),this.cursor(),e)}dragStart(e){return!!this.isEnabledInternal&&(this.startX=e.pageX,this.startY=e.pageY,this.sendDragStart(this.startX,this.startY),!0)}sendDragStart(e,t){this.dispatchEventToListeners("ResizeStart",{startX:e,currentX:e,startY:t,currentY:t})}drag(e){return this.isEnabledInternal?(this.sendDragMove(this.startX,e.pageX,this.startY,e.pageY,e.shiftKey),e.preventDefault(),!1):(this.dragEnd(e),!0)}sendDragMove(e,t,n,i,s){this.dispatchEventToListeners("ResizeUpdateXY",{startX:e,currentX:t,startY:n,currentY:i,shiftKey:s})}dragEnd(e){this.dispatchEventToListeners("ResizeEnd"),delete this.startX,delete this.startY}}class Ve extends je{isVerticalInternal;constructor(){super(),this.isVerticalInternal=!0}isVertical(){return this.isVerticalInternal}setVertical(e){this.isVerticalInternal=e,this.updateElementCursors()}cursor(){return this.isVerticalInternal?"ns-resize":"ew-resize"}sendDragStart(e,t){const n=this.isVerticalInternal?t:e;this.dispatchEventToListeners("ResizeStart",{startPosition:n,currentPosition:n})}sendDragMove(e,t,n,i,s){this.isVerticalInternal?this.dispatchEventToListeners("ResizeUpdatePosition",{startPosition:n,currentPosition:i,shiftKey:s}):this.dispatchEventToListeners("ResizeUpdatePosition",{startPosition:e,currentPosition:t,shiftKey:s})}}var Ue=Object.freeze({__proto__:null,ResizerWidget:je,SimpleResizerWidget:Ve}),_e={cssContent:".shadow-split-widget{display:flex;overflow:hidden}.shadow-split-widget-contents{display:flex;position:relative;flex-direction:column;contain:layout size style}.shadow-split-widget-sidebar{flex:none}.shadow-split-widget-main,\n.shadow-split-widget-sidebar.maximized{flex:auto}.shadow-split-widget.hbox > .shadow-split-widget-resizer{position:absolute;top:0;bottom:0;width:6px;z-index:4000}.shadow-split-widget.vbox > .shadow-split-widget-resizer{position:absolute;left:0;right:0;height:6px;z-index:4000}.shadow-split-widget.vbox > .shadow-split-widget-sidebar.no-default-splitter{border:0!important}.shadow-split-widget.vbox > .shadow-split-widget-sidebar:not(.maximized){border:0;border-top:1px solid var(--sys-color-divider)}.shadow-split-widget.hbox > .shadow-split-widget-sidebar:not(.maximized){border:0;border-left:1px solid var(--sys-color-divider)}.shadow-split-widget.vbox > .shadow-split-widget-sidebar:first-child:not(.maximized){border:0;border-bottom:1px solid var(--sys-color-divider)}.shadow-split-widget.hbox > .shadow-split-widget-sidebar:first-child:not(.maximized){border:0;border-right:1px solid var(--sys-color-divider)}:host-context(.disable-resizer-for-elements-hack) .shadow-split-widget-resizer{pointer-events:none}\n/*# sourceURL=splitWidget.css */\n"};class Ke extends HTMLElement{static get observedAttributes(){return["flex","padding","padding-top","padding-bottom","padding-left","padding-right","margin","margin-top","margin-bottom","margin-left","margin-right","overflow","overflow-x","overflow-y","font-size","color","background","background-color","border","border-top","border-bottom","border-left","border-right","max-width","max-height"]}attributeChangedCallback(e,t,n){if("flex"!==e)if(null===n){if(this.style.removeProperty(e),e.startsWith("padding-")||e.startsWith("margin-")||e.startsWith("border-")||e.startsWith("background-")||e.startsWith("overflow-")){const t=e.substring(0,e.indexOf("-")),n=this.getAttribute(t);null!==n&&this.style.setProperty(t,n)}}else this.style.setProperty(e,n);else null===n?this.style.removeProperty("flex"):"initial"===n||"auto"===n||"none"===n||-1!==n.indexOf(" ")?this.style.setProperty("flex",n):this.style.setProperty("flex","0 0 "+n)}}class qe extends Ke{constructor(e){super(),this.style.setProperty("display","flex"),this.style.setProperty("flex-direction",e),this.style.setProperty("justify-content","flex-start")}static get observedAttributes(){return super.observedAttributes.concat(["x-start","x-center","x-stretch","x-baseline","justify-content"])}attributeChangedCallback(e,t,n){"x-start"!==e&&"x-center"!==e&&"x-stretch"!==e&&"x-baseline"!==e?super.attributeChangedCallback(e,t,n):null===n?this.style.removeProperty("align-items"):this.style.setProperty("align-items","x-start"===e?"flex-start":e.substr(2))}}customElements.define("x-vbox",class extends qe{constructor(){super("column")}}),customElements.define("x-hbox",class extends qe{constructor(){super("row")}}),customElements.define("x-cbox",class extends Ke{constructor(){super(),this.style.setProperty("display","flex"),this.style.setProperty("flex-direction","column"),this.style.setProperty("justify-content","center"),this.style.setProperty("align-items","center")}}),customElements.define("x-div",class extends Ke{constructor(){super(),this.style.setProperty("display","block")}}),customElements.define("x-span",class extends Ke{constructor(){super(),this.style.setProperty("display","inline")}}),customElements.define("x-text",class extends Ke{constructor(){super(),this.style.setProperty("display","inline"),this.style.setProperty("white-space","pre")}});var Ge=Object.freeze({__proto__:null,XElement:Ke});let $e=null;const Xe=new WeakMap;class Ye extends Ke{visible;shadowRootInternal;defaultFocusedElement;elementsToRestoreScrollPositionsFor;onShownCallback;onHiddenCallback;onResizedCallback;constructor(){super(),this.style.setProperty("display","flex"),this.style.setProperty("flex-direction","column"),this.style.setProperty("align-items","stretch"),this.style.setProperty("justify-content","flex-start"),this.style.setProperty("contain","layout style"),this.visible=!1,this.defaultFocusedElement=null,this.elementsToRestoreScrollPositionsFor=[],$e||($e=new ResizeObserver((e=>{for(const t of e){const e=t.target;e.visible&&e.onResizedCallback&&e.onResizedCallback.call(null)}}))),$e.observe(this),this.setElementsToRestoreScrollPositionsFor([this])}isShowing(){return this.visible}setOnShown(e){this.onShownCallback=e}setOnHidden(e){this.onHiddenCallback=e}setOnResized(e){this.onResizedCallback=e}setElementsToRestoreScrollPositionsFor(e){for(const e of this.elementsToRestoreScrollPositionsFor)e.removeEventListener("scroll",Ye.storeScrollPosition,{capture:!1});this.elementsToRestoreScrollPositionsFor=e;for(const e of this.elementsToRestoreScrollPositionsFor)e.addEventListener("scroll",Ye.storeScrollPosition,{passive:!0,capture:!1})}restoreScrollPositions(){for(const e of this.elementsToRestoreScrollPositionsFor){const t=Xe.get(e);t&&(e.scrollTop=t.scrollTop,e.scrollLeft=t.scrollLeft)}}static storeScrollPosition(e){const t=e.currentTarget;Xe.set(t,{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop})}setDefaultFocusedElement(e){if(e&&!this.isSelfOrAncestor(e))throw new Error("Default focus must be descendant");this.defaultFocusedElement=e}focus(){if(!this.visible)return;let e;if(this.defaultFocusedElement&&this.isSelfOrAncestor(this.defaultFocusedElement))e=this.defaultFocusedElement;else if(-1!==this.tabIndex)e=this;else{let t=this.traverseNextNode(this);for(;t;){if(t instanceof Ye&&t.visible){e=t;break}t=t.traverseNextNode(this)}}e&&!e.hasFocus()&&(e===this?HTMLElement.prototype.focus.call(this):e.focus())}connectedCallback(){this.visible=!0,this.restoreScrollPositions(),this.onShownCallback&&this.onShownCallback.call(null)}disconnectedCallback(){this.visible=!1,this.onHiddenCallback&&this.onHiddenCallback.call(null)}}customElements.define("x-widget",Ye);var Qe=Object.freeze({__proto__:null,XWidget:Ye});const Ze=Element.prototype.appendChild,Je=Element.prototype.insertBefore,et=Element.prototype.removeChild,tt=Element.prototype.removeChildren;function nt(e,t){if(!e)throw new Error(t)}const it=new WeakMap,st=new WeakMap;function rt(e,t){const n=(it.get(t)||0)+(st.get(t)?1:0);for(let t=e;t;t=t.parentElementOrShadowHost()){const e=it.get(t);e&&it.set(t,e-n)}}class ot{element;contentElement;shadowRoot;isWebComponent;visibleInternal;isRoot;isShowingInternal;childrenInternal;hideOnDetach;notificationDepth;invalidationsSuspended;defaultFocusedChild;parentWidgetInternal;defaultFocusedElement;cachedConstraints;constraintsInternal;invalidationsRequested;externallyManaged;constructor(e,t){this.contentElement=document.createElement("div"),this.contentElement.classList.add("widget"),e?(this.element=document.createElement("div"),this.element.classList.add("vbox"),this.element.classList.add("flex-auto"),this.shadowRoot=ds(this.element,{cssFile:void 0,delegatesFocus:t}),this.shadowRoot.appendChild(this.contentElement)):this.element=this.contentElement,this.isWebComponent=e,st.set(this.element,this),this.visibleInternal=!1,this.isRoot=!1,this.isShowingInternal=!1,this.childrenInternal=[],this.hideOnDetach=!1,this.notificationDepth=0,this.invalidationsSuspended=0,this.defaultFocusedChild=null,this.parentWidgetInternal=null}static get(e){return st.get(e)}markAsRoot(){nt(!this.element.parentElement,"Attempt to mark as root attached node"),this.isRoot=!0}parentWidget(){return this.parentWidgetInternal}children(){return this.childrenInternal}childWasDetached(e){}isShowing(){return this.isShowingInternal}shouldHideOnDetach(){if(!this.element.parentElement)return!1;if(this.hideOnDetach)return!0;for(const e of this.childrenInternal)if(e.shouldHideOnDetach())return!0;return!1}setHideOnDetach(){this.hideOnDetach=!0}inNotification(){return Boolean(this.notificationDepth)||Boolean(this.parentWidgetInternal&&this.parentWidgetInternal.inNotification())}parentIsShowing(){return!!this.isRoot||null!==this.parentWidgetInternal&&this.parentWidgetInternal.isShowing()}callOnVisibleChildren(e){const t=this.childrenInternal.slice();for(let n=0;n<t.length;++n)t[n].parentWidgetInternal===this&&t[n].visibleInternal&&e.call(t[n])}processWillShow(){this.callOnVisibleChildren(this.processWillShow),this.isShowingInternal=!0}processWasShown(){this.inNotification()||(this.restoreScrollPositions(),this.notify(this.wasShown),this.callOnVisibleChildren(this.processWasShown))}processWillHide(){this.inNotification()||(this.storeScrollPositions(),this.callOnVisibleChildren(this.processWillHide),this.notify(this.willHide),this.isShowingInternal=!1)}processWasHidden(){this.callOnVisibleChildren(this.processWasHidden)}processOnResize(){this.inNotification()||this.isShowing()&&(this.notify(this.onResize),this.callOnVisibleChildren(this.processOnResize))}notify(e){++this.notificationDepth;try{e.call(this)}finally{--this.notificationDepth}}wasShown(){}willHide(){}onResize(){}onLayout(){}onDetach(){}async ownerViewDisposed(){}show(e,t){if(nt(e,"Attempt to attach widget with no parent element"),!this.isRoot){let t,n=e;for(;!t;){if(!n)throw new Error("Attempt to attach widget to orphan node");t=st.get(n),n=n.parentElementOrShadowHost()}this.attach(t)}this.showWidgetInternal(e,t)}attach(e){e!==this.parentWidgetInternal&&(this.parentWidgetInternal&&this.detach(),this.parentWidgetInternal=e,this.parentWidgetInternal.childrenInternal.push(this),this.isRoot=!1)}showWidget(){if(!this.visibleInternal){if(!this.element.parentElement)throw new Error("Attempt to show widget that is not hidden using hideWidget().");this.showWidgetInternal(this.element.parentElement,this.element.nextSibling)}}showWidgetInternal(e,t){let n=e;for(;n&&!st.get(n);)n=n.parentElementOrShadowHost();this.isRoot?nt(!n,"Attempt to show root widget under another widget"):nt(n&&st.get(n)===this.parentWidgetInternal,"Attempt to show under node belonging to alien widget");const i=this.visibleInternal;i&&this.element.parentElement===e||(this.visibleInternal=!0,!i&&this.parentIsShowing()&&this.processWillShow(),this.element.classList.remove("hidden"),this.element.parentElement!==e&&(this.externallyManaged||function(e,t){const n=(it.get(t)||0)+(st.get(t)?1:0);for(let t=e;t;t=t.parentElementOrShadowHost())it.set(t,(it.get(t)||0)+n)}(e,this.element),t?Je.call(e,this.element,t):Ze.call(e,this.element)),!i&&this.parentIsShowing()&&this.processWasShown(),this.parentWidgetInternal&&this.hasNonZeroConstraints()?this.parentWidgetInternal.invalidateConstraints():this.processOnResize())}hideWidget(){this.visibleInternal&&this.hideWidgetInternal(!1)}hideWidgetInternal(e){this.visibleInternal=!1;const{parentElement:t}=this.element;this.parentIsShowing()&&this.processWillHide(),e?(t&&(rt(t,this.element),et.call(t,this.element)),this.onDetach()):this.element.classList.add("hidden"),this.parentIsShowing()&&this.processWasHidden(),this.parentWidgetInternal&&this.hasNonZeroConstraints()&&this.parentWidgetInternal.invalidateConstraints()}detach(e){if(!this.parentWidgetInternal&&!this.isRoot)return;const t=e||!this.shouldHideOnDetach();if(this.visibleInternal)this.hideWidgetInternal(t);else if(t){const{parentElement:e}=this.element;e&&(rt(e,this.element),et.call(e,this.element))}if(this.parentWidgetInternal){const e=this.parentWidgetInternal.childrenInternal.indexOf(this);nt(e>=0,"Attempt to remove non-child widget"),this.parentWidgetInternal.childrenInternal.splice(e,1),this.parentWidgetInternal.defaultFocusedChild===this&&(this.parentWidgetInternal.defaultFocusedChild=null),this.parentWidgetInternal.childWasDetached(this),this.parentWidgetInternal=null}else nt(this.isRoot,"Removing non-root widget from DOM")}detachChildWidgets(){const e=this.childrenInternal.slice();for(let t=0;t<e.length;++t)e[t].detach()}elementsToRestoreScrollPositionsFor(){return[this.element]}storeScrollPositions(){const e=this.elementsToRestoreScrollPositionsFor();for(const t of e)at.set(t,{scrollLeft:t.scrollLeft,scrollTop:t.scrollTop})}restoreScrollPositions(){const e=this.elementsToRestoreScrollPositionsFor();for(const t of e){const e=at.get(t);e&&(t.scrollLeft=e.scrollLeft,t.scrollTop=e.scrollTop)}}doResize(){this.isShowing()&&(this.inNotification()||this.callOnVisibleChildren(this.processOnResize))}doLayout(){this.isShowing()&&(this.notify(this.onLayout),this.doResize())}registerRequiredCSS(e){this.isWebComponent?c.ThemeSupport.instance().appendStyle(this.shadowRoot,e):c.ThemeSupport.instance().appendStyle(this.element,e)}registerCSSFiles(e){let t;t=this.isWebComponent&&void 0!==this.shadowRoot?this.shadowRoot:h.GetRootNode.getRootNode(this.contentElement),t.adoptedStyleSheets=t.adoptedStyleSheets.concat(e)}printWidgetHierarchy(){const e=[];this.collectWidgetHierarchy("",e),console.log(e.join("\n"))}collectWidgetHierarchy(e,t){t.push(e+"["+this.element.className+"]"+(this.childrenInternal.length?" {":""));for(let n=0;n<this.childrenInternal.length;++n)this.childrenInternal[n].collectWidgetHierarchy(e+"    ",t);this.childrenInternal.length&&t.push(e+"}")}setDefaultFocusedElement(e){this.defaultFocusedElement=e}setDefaultFocusedChild(e){nt(e.parentWidgetInternal===this,"Attempt to set non-child widget as default focused."),this.defaultFocusedChild=e}focus(){if(!this.isShowing())return;const e=this.defaultFocusedElement;if(e)e.hasFocus()||e.focus();else if(this.defaultFocusedChild&&this.defaultFocusedChild.visibleInternal)this.defaultFocusedChild.focus();else{for(const e of this.childrenInternal)if(e.visibleInternal)return void e.focus();let e=this.contentElement.traverseNextNode(this.contentElement);for(;e;){if(e instanceof Ye)return void e.focus();e=e.traverseNextNode(this.contentElement)}}}hasFocus(){return this.element.hasFocus()}calculateConstraints(){return new K}constraints(){return void 0!==this.constraintsInternal?this.constraintsInternal:(void 0===this.cachedConstraints&&(this.cachedConstraints=this.calculateConstraints()),this.cachedConstraints)}setMinimumAndPreferredSizes(e,t,n,i){this.constraintsInternal=new K(new _(e,t),new _(n,i)),this.invalidateConstraints()}setMinimumSize(e,t){this.constraintsInternal=new K(new _(e,t)),this.invalidateConstraints()}hasNonZeroConstraints(){const e=this.constraints();return Boolean(e.minimum.width||e.minimum.height||e.preferred.width||e.preferred.height)}suspendInvalidations(){++this.invalidationsSuspended}resumeInvalidations(){--this.invalidationsSuspended,!this.invalidationsSuspended&&this.invalidationsRequested&&this.invalidateConstraints()}invalidateConstraints(){if(this.invalidationsSuspended)return void(this.invalidationsRequested=!0);this.invalidationsRequested=!1;const e=this.cachedConstraints;delete this.cachedConstraints;!this.constraints().isEqual(e||null)&&this.parentWidgetInternal?this.parentWidgetInternal.invalidateConstraints():this.doLayout()}markAsExternallyManaged(){nt(!this.parentWidgetInternal,"Attempt to mark widget as externally managed after insertion to the DOM"),this.externallyManaged=!0}}const at=new WeakMap;class lt extends ot{constructor(e,t){super(e,t),this.contentElement.classList.add("vbox")}calculateConstraints(){let e=new K;return this.callOnVisibleChildren((function(){const t=this.constraints();e=e.widthToMax(t),e=e.addHeight(t)})),e}}class ct extends ot{constructor(e){super(e),this.contentElement.classList.add("hbox")}calculateConstraints(){let e=new K;return this.callOnVisibleChildren((function(){const t=this.constraints();e=e.addWidth(t),e=e.heightToMax(t)})),e}}class dt{widget;previous;constructor(e){this.widget=e,this.previous=n.DOMUtilities.deepActiveElement(e.element.ownerDocument),e.focus()}restore(){this.widget&&(this.widget.hasFocus()&&this.previous&&this.previous.focus(),this.previous=null,this.widget=null)}}Element.prototype.appendChild=function(e){if(st.get(e)&&e.parentElement!==this)throw new Error("Attempt to add widget via regular DOM operation.");return Ze.call(this,e)},Element.prototype.insertBefore=function(e,t){if(st.get(e)&&e.parentElement!==this)throw new Error("Attempt to add widget via regular DOM operation.");return Je.call(this,e,t)},Element.prototype.removeChild=function(e){if(it.get(e)||st.get(e))throw new Error("Attempt to remove element containing widget via regular DOM operation");return et.call(this,e)},Element.prototype.removeChildren=function(){if(it.get(this))throw new Error("Attempt to remove element containing widget via regular DOM operation");return tt.call(this)};var ht=Object.freeze({__proto__:null,Widget:ot,VBox:lt,HBox:ct,VBoxWithResizeCallback:class extends lt{resizeCallback;constructor(e){super(),this.resizeCallback=e}onResize(){this.resizeCallback()}},WidgetFocusRestorer:dt});let ut;class pt extends e.ObjectWrapper.ObjectWrapper{frontendHost;zoomFactorInternal;constructor(e,t){super(),this.frontendHost=t,this.zoomFactorInternal=this.frontendHost.zoomFactor(),e.addEventListener("resize",this.onWindowResize.bind(this),!0)}static instance(e={forceNew:null,win:null,frontendHost:null}){const{forceNew:t,win:n,frontendHost:i}=e;if(!ut||t){if(!n||!i)throw new Error(`Unable to create zoom manager: window and frontendHost must be provided: ${(new Error).stack}`);ut=new pt(n,i)}return ut}static removeInstance(){ut=void 0}zoomFactor(){return this.zoomFactorInternal}cssToDIP(e){return e*this.zoomFactorInternal}dipToCSS(e){return e/this.zoomFactorInternal}onWindowResize(){const e=this.zoomFactorInternal;this.zoomFactorInternal=this.frontendHost.zoomFactor(),e!==this.zoomFactorInternal&&this.dispatchEventToListeners("ZoomChanged",{from:e,to:this.zoomFactorInternal})}}var gt=Object.freeze({__proto__:null,ZoomManager:pt});class mt extends(e.ObjectWrapper.eventMixin(ot)){sidebarElementInternal;mainElement;resizerElementInternal;resizerElementSize;resizerWidget;defaultSidebarWidth;defaultSidebarHeight;constraintsInDip;resizeStartSizeDIP;setting;totalSizeCSS;totalSizeOtherDimensionCSS;mainWidgetInternal;sidebarWidgetInternal;animationFrameHandle;animationCallback;showSidebarButtonTitle;hideSidebarButtonTitle;shownSidebarString;hiddenSidebarString;showHideSidebarButton;isVerticalInternal;sidebarMinimized;detaching;sidebarSizeDIP;savedSidebarSizeDIP;secondIsSidebar;shouldSaveShowMode;savedVerticalMainSize;savedHorizontalMainSize;showModeInternal;savedShowMode;constructor(t,n,i,s,r,o){super(!0),this.element.classList.add("split-widget"),this.registerRequiredCSS(_e),this.contentElement.classList.add("shadow-split-widget"),this.sidebarElementInternal=this.contentElement.createChild("div","shadow-split-widget-contents shadow-split-widget-sidebar vbox"),this.mainElement=this.contentElement.createChild("div","shadow-split-widget-contents shadow-split-widget-main vbox"),this.mainElement.createChild("slot").name="insertion-point-main",this.sidebarElementInternal.createChild("slot").name="insertion-point-sidebar",this.resizerElementInternal=this.contentElement.createChild("div","shadow-split-widget-resizer"),this.resizerElementSize=null,this.resizerWidget=new Ve,this.resizerWidget.setEnabled(!0),this.resizerWidget.addEventListener("ResizeStart",this.onResizeStart,this),this.resizerWidget.addEventListener("ResizeUpdatePosition",this.onResizeUpdate,this),this.resizerWidget.addEventListener("ResizeEnd",this.onResizeEnd,this),this.defaultSidebarWidth=s||200,this.defaultSidebarHeight=r||this.defaultSidebarWidth,this.constraintsInDip=Boolean(o),this.resizeStartSizeDIP=0,this.setting=i?e.Settings.Settings.instance().createSetting(i,{}):null,this.totalSizeCSS=0,this.totalSizeOtherDimensionCSS=0,this.mainWidgetInternal=null,this.sidebarWidgetInternal=null,this.animationFrameHandle=0,this.animationCallback=null,this.showSidebarButtonTitle=e.UIString.LocalizedEmptyString,this.hideSidebarButtonTitle=e.UIString.LocalizedEmptyString,this.shownSidebarString=e.UIString.LocalizedEmptyString,this.hiddenSidebarString=e.UIString.LocalizedEmptyString,this.showHideSidebarButton=null,this.isVerticalInternal=!1,this.sidebarMinimized=!1,this.detaching=!1,this.sidebarSizeDIP=-1,this.savedSidebarSizeDIP=this.sidebarSizeDIP,this.secondIsSidebar=!1,this.shouldSaveShowMode=!1,this.savedVerticalMainSize=null,this.savedHorizontalMainSize=null,this.setSecondIsSidebar(n),this.innerSetVertical(t),this.showModeInternal="Both",this.savedShowMode=this.showModeInternal,this.installResizer(this.resizerElementInternal)}isVertical(){return this.isVerticalInternal}setVertical(e){this.isVerticalInternal!==e&&(this.innerSetVertical(e),this.isShowing()&&this.updateLayout())}innerSetVertical(e){this.contentElement.classList.toggle("vbox",!e),this.contentElement.classList.toggle("hbox",e),this.isVerticalInternal=e,this.resizerElementSize=null,this.sidebarSizeDIP=-1,this.restoreSidebarSizeFromSettings(),this.shouldSaveShowMode&&this.restoreAndApplyShowModeFromSettings(),this.updateShowHideSidebarButton(),this.resizerWidget.setVertical(!e),this.invalidateConstraints()}updateLayout(e){this.totalSizeCSS=0,this.totalSizeOtherDimensionCSS=0,this.mainElement.style.removeProperty("width"),this.mainElement.style.removeProperty("height"),this.sidebarElementInternal.style.removeProperty("width"),this.sidebarElementInternal.style.removeProperty("height"),this.innerSetSidebarSizeDIP(this.preferredSidebarSizeDIP(),Boolean(e))}setMainWidget(e){this.mainWidgetInternal!==e&&(this.suspendInvalidations(),this.mainWidgetInternal&&this.mainWidgetInternal.detach(),this.mainWidgetInternal=e,e&&(e.element.slot="insertion-point-main","OnlyMain"!==this.showModeInternal&&"Both"!==this.showModeInternal||e.show(this.element)),this.resumeInvalidations())}setSidebarWidget(e){this.sidebarWidgetInternal!==e&&(this.suspendInvalidations(),this.sidebarWidgetInternal&&this.sidebarWidgetInternal.detach(),this.sidebarWidgetInternal=e,e&&(e.element.slot="insertion-point-sidebar","OnlySidebar"!==this.showModeInternal&&"Both"!==this.showModeInternal||e.show(this.element)),this.resumeInvalidations())}mainWidget(){return this.mainWidgetInternal}sidebarWidget(){return this.sidebarWidgetInternal}sidebarElement(){return this.sidebarElementInternal}childWasDetached(e){this.detaching||(this.mainWidgetInternal===e&&(this.mainWidgetInternal=null),this.sidebarWidgetInternal===e&&(this.sidebarWidgetInternal=null),this.invalidateConstraints())}isSidebarSecond(){return this.secondIsSidebar}enableShowModeSaving(){this.shouldSaveShowMode=!0,this.restoreAndApplyShowModeFromSettings()}showMode(){return this.showModeInternal}setSecondIsSidebar(e){e!==this.secondIsSidebar&&(this.secondIsSidebar=e,this.mainWidgetInternal&&this.mainWidgetInternal.shouldHideOnDetach()?this.sidebarWidgetInternal&&this.sidebarWidgetInternal.shouldHideOnDetach()?(console.error("Could not swap split widget side. Both children widgets contain iframes."),this.secondIsSidebar=!e):e?this.contentElement.insertBefore(this.sidebarElementInternal,this.resizerElementInternal):this.contentElement.insertBefore(this.sidebarElementInternal,this.mainElement):e?this.contentElement.insertBefore(this.mainElement,this.sidebarElementInternal):this.contentElement.insertBefore(this.mainElement,this.resizerElementInternal))}sidebarSide(){return"Both"!==this.showModeInternal?null:this.isVerticalInternal?this.secondIsSidebar?"right":"left":this.secondIsSidebar?"bottom":"top"}resizerElement(){return this.resizerElementInternal}hideMain(e){this.showOnly(this.sidebarWidgetInternal,this.mainWidgetInternal,this.sidebarElementInternal,this.mainElement,e),this.updateShowMode("OnlySidebar")}hideSidebar(e){this.showOnly(this.mainWidgetInternal,this.sidebarWidgetInternal,this.mainElement,this.sidebarElementInternal,e),this.updateShowMode("OnlyMain")}setSidebarMinimized(e){this.sidebarMinimized=e,this.invalidateConstraints()}isSidebarMinimized(){return this.sidebarMinimized}showOnly(e,t,n,i,s){function r(){e&&(e===this.mainWidgetInternal?this.mainWidgetInternal.show(this.element,this.sidebarWidgetInternal?this.sidebarWidgetInternal.element:null):this.sidebarWidgetInternal&&this.sidebarWidgetInternal.show(this.element)),t&&(this.detaching=!0,t.detach(),this.detaching=!1),this.resizerElementInternal.classList.add("hidden"),n.classList.remove("hidden"),n.classList.add("maximized"),i.classList.add("hidden"),i.classList.remove("maximized"),this.removeAllLayoutProperties(),this.doResize(),this.showFinishedForTest()}this.cancelAnimation(),s?this.animate(!0,r.bind(this)):r.call(this),this.sidebarSizeDIP=-1,this.setResizable(!1)}showFinishedForTest(){}removeAllLayoutProperties(){this.sidebarElementInternal.style.removeProperty("flexBasis"),this.mainElement.style.removeProperty("width"),this.mainElement.style.removeProperty("height"),this.sidebarElementInternal.style.removeProperty("width"),this.sidebarElementInternal.style.removeProperty("height"),this.resizerElementInternal.style.removeProperty("left"),this.resizerElementInternal.style.removeProperty("right"),this.resizerElementInternal.style.removeProperty("top"),this.resizerElementInternal.style.removeProperty("bottom"),this.resizerElementInternal.style.removeProperty("margin-left"),this.resizerElementInternal.style.removeProperty("margin-right"),this.resizerElementInternal.style.removeProperty("margin-top"),this.resizerElementInternal.style.removeProperty("margin-bottom")}showBoth(e){"Both"===this.showModeInternal&&(e=!1),this.cancelAnimation(),this.mainElement.classList.remove("maximized","hidden"),this.sidebarElementInternal.classList.remove("maximized","hidden"),this.resizerElementInternal.classList.remove("hidden"),this.setResizable(!0),this.suspendInvalidations(),this.sidebarWidgetInternal&&this.sidebarWidgetInternal.show(this.element),this.mainWidgetInternal&&this.mainWidgetInternal.show(this.element,this.sidebarWidgetInternal?this.sidebarWidgetInternal.element:null),this.resumeInvalidations(),this.setSecondIsSidebar(this.secondIsSidebar),this.sidebarSizeDIP=-1,this.updateShowMode("Both"),this.updateLayout(e)}setResizable(e){this.resizerWidget.setEnabled(e)}forceSetSidebarWidth(e){this.defaultSidebarWidth=e,this.savedSidebarSizeDIP=e,this.updateLayout()}isResizable(){return this.resizerWidget.isEnabled()}setSidebarSize(e){const t=pt.instance().cssToDIP(e);this.savedSidebarSizeDIP=t,this.saveSetting(),this.innerSetSidebarSizeDIP(t,!1,!0)}sidebarSize(){const e=Math.max(0,this.sidebarSizeDIP);return pt.instance().dipToCSS(e)}totalSizeDIP(){return this.totalSizeCSS||(this.totalSizeCSS=this.isVerticalInternal?this.contentElement.offsetWidth:this.contentElement.offsetHeight,this.totalSizeOtherDimensionCSS=this.isVerticalInternal?this.contentElement.offsetHeight:this.contentElement.offsetWidth),pt.instance().cssToDIP(this.totalSizeCSS)}updateShowMode(e){this.showModeInternal=e,this.saveShowModeToSettings(),this.updateShowHideSidebarButton(),this.dispatchEventToListeners("ShowModeChanged",e),this.invalidateConstraints()}innerSetSidebarSizeDIP(e,t,n){if("Both"!==this.showModeInternal||!this.isShowing())return;if(e=this.applyConstraints(e,n),this.sidebarSizeDIP===e)return;this.resizerElementSize||(this.resizerElementSize=this.isVerticalInternal?this.resizerElementInternal.offsetWidth:this.resizerElementInternal.offsetHeight),this.removeAllLayoutProperties();const i=Math.round(pt.instance().dipToCSS(e)),s=i+"px",r=this.totalSizeCSS-i+"px";this.sidebarElementInternal.style.flexBasis=s,this.isVerticalInternal?(this.sidebarElementInternal.style.width=s,this.mainElement.style.width=r,this.sidebarElementInternal.style.height=this.totalSizeOtherDimensionCSS+"px",this.mainElement.style.height=this.totalSizeOtherDimensionCSS+"px"):(this.sidebarElementInternal.style.height=s,this.mainElement.style.height=r,this.sidebarElementInternal.style.width=this.totalSizeOtherDimensionCSS+"px",this.mainElement.style.width=this.totalSizeOtherDimensionCSS+"px"),this.isVerticalInternal?this.secondIsSidebar?(this.resizerElementInternal.style.right=s,this.resizerElementInternal.style.marginRight=-this.resizerElementSize/2+"px"):(this.resizerElementInternal.style.left=s,this.resizerElementInternal.style.marginLeft=-this.resizerElementSize/2+"px"):this.secondIsSidebar?(this.resizerElementInternal.style.bottom=s,this.resizerElementInternal.style.marginBottom=-this.resizerElementSize/2+"px"):(this.resizerElementInternal.style.top=s,this.resizerElementInternal.style.marginTop=-this.resizerElementSize/2+"px"),this.sidebarSizeDIP=e,t?this.animate(!1):(this.doResize(),this.dispatchEventToListeners("SidebarSizeChanged",this.sidebarSize()))}animate(e,t){let n;this.animationCallback=t||null,n=this.isVerticalInternal?this.secondIsSidebar?"margin-right":"margin-left":this.secondIsSidebar?"margin-bottom":"margin-top";const i=e?"0":"-"+pt.instance().dipToCSS(this.sidebarSizeDIP)+"px",s=e?"-"+pt.instance().dipToCSS(this.sidebarSizeDIP)+"px":"0";this.contentElement.style.setProperty(n,i),this.contentElement.style.setProperty("overflow","hidden"),e||(ft(this.mainElement.offsetWidth),ft(this.sidebarElementInternal.offsetWidth)),!e&&this.sidebarWidgetInternal&&this.sidebarWidgetInternal.doResize(),this.contentElement.style.setProperty("transition",n+" 50ms linear");const r=function(){if(this.animationFrameHandle=0,o){if(!(window.performance.now()<o+50))return this.cancelAnimation(),this.mainWidgetInternal&&this.mainWidgetInternal.doResize(),void this.dispatchEventToListeners("SidebarSizeChanged",this.sidebarSize());this.mainWidgetInternal&&this.mainWidgetInternal.doResize()}else this.contentElement.style.setProperty(n,s),o=window.performance.now();this.animationFrameHandle=this.contentElement.window().requestAnimationFrame(r)}.bind(this);let o=null;this.animationFrameHandle=this.contentElement.window().requestAnimationFrame(r)}cancelAnimation(){this.contentElement.style.removeProperty("margin-top"),this.contentElement.style.removeProperty("margin-right"),this.contentElement.style.removeProperty("margin-bottom"),this.contentElement.style.removeProperty("margin-left"),this.contentElement.style.removeProperty("transition"),this.contentElement.style.removeProperty("overflow"),this.animationFrameHandle&&(this.contentElement.window().cancelAnimationFrame(this.animationFrameHandle),this.animationFrameHandle=0),this.animationCallback&&(this.animationCallback(),this.animationCallback=null)}applyConstraints(e,t){const i=this.totalSizeDIP(),s=this.constraintsInDip?1:pt.instance().zoomFactor();let r=this.sidebarWidgetInternal?this.sidebarWidgetInternal.constraints():new K,o=this.isVertical()?r.minimum.width:r.minimum.height;o||(o=bt),o*=s,this.sidebarMinimized&&(e=o);let a=this.isVertical()?r.preferred.width:r.preferred.height;a||(a=bt),a*=s,e<a&&(a=Math.max(e,o)),a+=s,r=this.mainWidgetInternal?this.mainWidgetInternal.constraints():new K;let l=this.isVertical()?r.minimum.width:r.minimum.height;l||(l=bt),l*=s;let c=this.isVertical()?r.preferred.width:r.preferred.height;c||(c=bt),c*=s;const d=this.isVertical()?this.savedVerticalMainSize:this.savedHorizontalMainSize;null!==d&&(c=Math.min(c,d*s)),t&&(c=l);const h=c+a;if(h<=i)return n.NumberUtilities.clamp(e,a,i-c);if(l+o<=i){return e=a-(h-i)*a/h,n.NumberUtilities.clamp(e,o,i-l)}return Math.max(0,i-l)}wasShown(){this.forceUpdateLayout(),pt.instance().addEventListener("ZoomChanged",this.onZoomChanged,this)}willHide(){pt.instance().removeEventListener("ZoomChanged",this.onZoomChanged,this)}onResize(){this.updateLayout()}onLayout(){this.updateLayout()}calculateConstraints(){if("OnlyMain"===this.showModeInternal)return this.mainWidgetInternal?this.mainWidgetInternal.constraints():new K;if("OnlySidebar"===this.showModeInternal)return this.sidebarWidgetInternal?this.sidebarWidgetInternal.constraints():new K;let e=this.mainWidgetInternal?this.mainWidgetInternal.constraints():new K,t=this.sidebarWidgetInternal?this.sidebarWidgetInternal.constraints():new K;const n=bt;return this.isVerticalInternal?(e=e.widthToMax(n).addWidth(1),t=t.widthToMax(n),e.addWidth(t).heightToMax(t)):(e=e.heightToMax(n).addHeight(1),t=t.heightToMax(n),e.widthToMax(t).addHeight(t))}onResizeStart(){this.resizeStartSizeDIP=this.sidebarSizeDIP}onResizeUpdate(e){const t=e.data.currentPosition-e.data.startPosition,n=pt.instance().cssToDIP(t),i=this.secondIsSidebar?this.resizeStartSizeDIP-n:this.resizeStartSizeDIP+n,s=this.applyConstraints(i,!0);this.savedSidebarSizeDIP=s,this.saveSetting(),this.innerSetSidebarSizeDIP(s,!1,!0),this.isVertical()?this.savedVerticalMainSize=this.totalSizeDIP()-this.sidebarSizeDIP:this.savedHorizontalMainSize=this.totalSizeDIP()-this.sidebarSizeDIP}onResizeEnd(){this.resizeStartSizeDIP=0}hideDefaultResizer(e){this.resizerElementInternal.classList.toggle("hidden",Boolean(e)),this.uninstallResizer(this.resizerElementInternal),this.sidebarElementInternal.classList.toggle("no-default-splitter",Boolean(e))}installResizer(e){this.resizerWidget.addElement(e)}uninstallResizer(e){this.resizerWidget.removeElement(e)}hasCustomResizer(){const e=this.resizerWidget.elements();return e.length>1||1===e.length&&e[0]!==this.resizerElementInternal}toggleResizer(e,t){t?this.installResizer(e):this.uninstallResizer(e)}settingForOrientation(){const e=this.setting?this.setting.get():{};return this.isVerticalInternal?e.vertical:e.horizontal}preferredSidebarSizeDIP(){let e=this.savedSidebarSizeDIP;return e||(e=this.isVerticalInternal?this.defaultSidebarWidth:this.defaultSidebarHeight,0<e&&e<1&&(e*=this.totalSizeDIP())),e}restoreSidebarSizeFromSettings(){const e=this.settingForOrientation();this.savedSidebarSizeDIP=e?e.size:0}restoreAndApplyShowModeFromSettings(){const e=this.settingForOrientation();switch(this.savedShowMode=e&&e.showMode?e.showMode:this.showModeInternal,this.showModeInternal=this.savedShowMode,this.savedShowMode){case"Both":this.showBoth();break;case"OnlyMain":this.hideSidebar();break;case"OnlySidebar":this.hideMain()}}saveShowModeToSettings(){this.savedShowMode=this.showModeInternal,this.saveSetting()}saveSetting(){if(!this.setting)return;const e=this.setting.get(),t=(this.isVerticalInternal?e.vertical:e.horizontal)||{};t.size=this.savedSidebarSizeDIP,this.shouldSaveShowMode&&(t.showMode=this.savedShowMode),this.isVerticalInternal?e.vertical=t:e.horizontal=t,this.setting.set(e)}forceUpdateLayout(){this.sidebarSizeDIP=-1,this.updateLayout()}onZoomChanged(){this.forceUpdateLayout()}createShowHideSidebarButton(e,t,n,i,r){return this.showSidebarButtonTitle=e,this.hideSidebarButtonTitle=t,this.shownSidebarString=n,this.hiddenSidebarString=i,this.showHideSidebarButton=new Jn("",""),this.showHideSidebarButton.addEventListener("Click",(function(){this.toggleSidebar()}),this),r&&this.showHideSidebarButton.element.setAttribute("jslog",`${s.toggleSubpane().track({click:!0}).context(r)}`),this.updateShowHideSidebarButton(),this.showHideSidebarButton}toggleSidebar(){"Both"!==this.showModeInternal?(this.showBoth(!0),mr(this.shownSidebarString)):(this.hideSidebar(!0),mr(this.hiddenSidebarString))}updateShowHideSidebarButton(){if(!this.showHideSidebarButton)return;const e="OnlyMain"===this.showModeInternal;let t="";t=e?this.isVertical()?this.isSidebarSecond()?"right-panel-open":"left-panel-open":this.isSidebarSecond()?"bottom-panel-open":"top-panel-open":this.isVertical()?this.isSidebarSecond()?"right-panel-close":"left-panel-close":this.isSidebarSecond()?"bottom-panel-close":"top-panel-close",this.showHideSidebarButton.setGlyph(t),this.showHideSidebarButton.setTitle(e?this.showSidebarButtonTitle:this.hideSidebarButtonTitle)}}const bt=20,ft=function(e){};var vt=Object.freeze({__proto__:null,SplitWidget:mt}),yt={cssContent:'.tabbed-pane{flex:auto;overflow:hidden}.tabbed-pane-content{position:relative;overflow:auto;flex:auto;display:flex;flex-direction:column}.tabbed-pane-content.has-no-tabs{background-color:var(--sys-color-cdt-base-container)}.tabbed-pane-placeholder{font-size:14px;text-align:center;width:fit-content;margin:40px auto 0;text-shadow:var(--color-background-opacity-80) 0 1px 0;line-height:28px}.tabbed-pane-placeholder-row{margin-inline:10px;& span{display:inline-block;padding-inline:10px}&:has(.workspace){max-width:400px;border:2px dashed var(--sys-color-neutral-outline);margin-block-start:20px;padding:10px;margin-inline:20px}& button{cursor:pointer;color:var(--text-link);background:transparent;border:none;padding:0;text-decoration:underline;margin-inline:5px;&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring);outline-offset:2px;border-radius:2px}}}.tabbed-pane-header{display:flex;flex:0 0 27px;border-bottom:1px solid var(--sys-color-divider);overflow:visible;width:100%;background-color:var(--app-color-toolbar-background);& > *{cursor:initial}}.tabbed-pane-header-contents{flex:auto;pointer-events:none;margin-left:0;position:relative;cursor:default}.tabbed-pane-header-contents > *{pointer-events:initial}.tabbed-pane-header-tab-icon{min-width:14px;display:flex;align-items:center;margin-right:2px}.tabbed-pane-header-tab-icon devtools-icon{margin-left:-1px}.tabbed-pane-header-tab{float:left;padding:2px 0.8em;height:26px;line-height:16px;white-space:nowrap;cursor:default;display:flex;align-items:center;color:var(--ui-text)}.tabbed-pane-header-tab.closeable{padding-right:4px}.tabbed-pane-header-tab.preview.closeable{padding-right:5px}.tabbed-pane-header-tab devtools-icon.dot::before{outline-color:var(--icon-gap-toolbar)}.tabbed-pane-header-tab:hover devtools-icon.dot::before{outline-color:var(--icon-gap-toolbar-hover)}.tabbed-pane-header-tab:not(.vertical-tab-layout):hover,\n.tabbed-pane-shadow .tabbed-pane-header-tab:focus-visible{color:var(--sys-color-on-surface);background-color:var(--sys-color-state-hover-on-subtle)}.tabbed-pane-header-tab-title{text-overflow:ellipsis;overflow:hidden}.tabbed-pane-header-tab.measuring{visibility:hidden}.tabbed-pane-header-tab.selected{border-bottom:none;color:var(--sys-color-primary)}.tabbed-pane-header-tab.dragging{--override-dragging-box-shadow-color:rgb(0 0 0/37%);position:relative;box-shadow:0 1px 4px 0 var(--override-dragging-box-shadow-color);background-color:var(--sys-color-state-hover-on-subtle)}.theme-with-dark-background .tabbed-pane-header-tab.dragging,\n:host-context(.theme-with-dark-background) .tabbed-pane-header-tab.dragging{--override-dragging-box-shadow-color:rgb(230 230 230/37%)}.tabbed-pane-header-tab .tabbed-pane-close-button{margin:0 -3px 0 4px;visibility:hidden}.tabbed-pane-header-tab:hover .tabbed-pane-close-button,\n.tabbed-pane-header-tab.selected .tabbed-pane-close-button{visibility:visible}.tabbed-pane-header-tabs-drop-down-container{float:left;opacity:80%;display:flex;align-items:center;height:100%}.tabbed-pane-header-tabs-drop-down-container > .chevron-icon:hover,\n.tabbed-pane-header-tabs-drop-down-container > .chevron-icon:focus-visible{color:var(--icon-default-hover)}.tabbed-pane-header-tabs-drop-down-container:hover,\n.tabbed-pane-header-tabs-drop-down-container:focus-visible{background-color:var(--sys-color-state-hover-on-subtle)}.tabbed-pane-header-tabs-drop-down-container.measuring{visibility:hidden}.tabbed-pane-header-tabs-drop-down-container:active{opacity:80%}.tabbed-pane-shadow.vertical-tab-layout{flex-direction:row!important}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header{background-color:transparent;border:none transparent!important;width:auto;flex:0 0 auto;flex-direction:column;padding-top:5px;overflow:hidden}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-content{padding:var(--sys-size-10);overflow-x:hidden}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-contents{margin:0;flex:none}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-tabs{display:flex;flex-direction:column;width:var(--sys-size-24);margin-right:var(--sys-size-5)}.tabbed-pane-shadow.vertical-tab-layout .tabbed-pane-header-tab{height:var(--size-12,28px);padding:0 var(--size-8,16px) 0 var(--size-7,14px);border-radius:0 100px 100px 0;color:var(--sys-color-on-surface);position:relative;& > .tabbed-pane-header-tab-icon devtools-icon{margin:0;margin-right:var(--sys-size-6)}&.selected{color:var(--app-color-navigation-drawer-label-selected);background-color:var(--app-color-navigation-drawer-background-selected);& > .tabbed-pane-header-tab-icon devtools-icon{color:var(--app-color-navigation-drawer-label-selected)}}&:focus-visible{outline:2px solid var(--sys-color-state-focus-ring)}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle);content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}}.tabbed-pane-tab-slider{height:2px;position:absolute;bottom:-1px;background-color:var(--sys-color-primary-bright);left:0;transform-origin:0 100%;transition:transform 150ms cubic-bezier(0,0,0.2,1);visibility:hidden}@media (-webkit-min-device-pixel-ratio: 1.1){.tabbed-pane-tab-slider{border-top:none}}.tabbed-pane-tab-slider.enabled{visibility:visible}.tabbed-pane-header-tab.disabled{opacity:50%;pointer-events:none}.tabbed-pane-left-toolbar{margin-right:-4px;flex:none}.tabbed-pane-right-toolbar{margin-left:-4px;flex:none}.preview-icon{--override-tabbed-pane-preview-icon-color:var(--icon-default);width:16px;height:16px;display:flex;align-items:center;justify-content:center;margin-left:3px;flex-shrink:0}.close-button{--tabbed-pane-close-icon-color:var(--icon-default);width:16px;height:16px;display:flex;align-items:center;justify-content:center;border-radius:50%;background-color:transparent;border:none}.close-button:hover,\n.close-button:active,\n.close-button:focus{background-color:var(--sys-color-state-hover-on-subtle)}.tabbed-pane-header-tab.preview.closeable .close-button{margin-left:0}@media (forced-colors: active){.tabbed-pane-tab-slider{forced-color-adjust:none;background-color:Highlight}.tabbed-pane-header{forced-color-adjust:none;border-bottom:1px solid transparent;background-color:ButtonFace}.tabbed-pane-header-contents .tabbed-pane-header-tabs .tabbed-pane-header-tab{background:ButtonFace;color:ButtonText}.tabbed-pane-header-tabs .tabbed-pane-header-tab:hover,\n  .tabbed-pane-header-tabs .tabbed-pane-shadow .tabbed-pane-header-tab:focus-visible{background-color:Highlight;color:HighlightText}.tabbed-pane-header-tab .tabbed-pane-header-tab-title{color:inherit}.tabbed-pane-header-contents .tabbed-pane-header-tabs .tabbed-pane-header-tab.selected,\n  .tabbed-pane-header-contents .tabbed-pane-header-tabs .tabbed-pane-header-tab.selected:focus-visible{background-color:Highlight;color:HighlightText}.tabbed-pane-header-tab:hover .tabbed-pane-close-button,\n  .tabbed-pane-shadow .tabbed-pane-header-tab:focus-visible .tabbed-pane-close-button{color:HighlightText}.tabbed-pane-header-tabs-drop-down-container{opacity:100%}.tabbed-pane-header-tabs-drop-down-container:hover,\n  .tabbed-pane-header-tabs-drop-down-container:focus-visible{background-color:Highlight}.tabbed-pane-header-tabs-drop-down-container > .chevron-icon{color:ButtonText}.tabbed-pane-header-tabs-drop-down-container:hover > .chevron-icon,\n  .tabbed-pane-header-tabs-drop-down-container:focus-visible > .chevron-icon{color:HighlightText}.tabbed-pane-header-tabs .tabbed-pane-header-tab .preview-icon{--override-tabbed-pane-preview-icon-color:ButtonText}.tabbed-pane-header-tab.selected .preview-icon,\n  .tabbed-pane-header-tab:hover .preview-icon{--override-tabbed-pane-preview-icon-color:HighlightText}.close-button{--tabbed-pane-close-icon-color:ButtonText;forced-color-adjust:none}.close-button:hover,\n  .close-button:active{--tabbed-pane-close-icon-color:HighlightText;background-color:Highlight}.selected .close-button{--tabbed-pane-close-icon-color:HighlightText}}\n/*# sourceURL=tabbedPane.css */\n'};class wt{static install(e,t){e.title=t||""}static installWithActionBinding(e,t,n){let i=t;const s=Y.instance().shortcutsForAction(n);for(const e of s)i+=` - ${e.title()}`;e.title=i}}var xt=Object.freeze({__proto__:null,Tooltip:wt});const Et={moreTabs:"More tabs",closeS:"Close {PH1}",close:"Close",closeOthers:"Close others",closeTabsToTheRight:"Close tabs to the right",closeAll:"Close all",previewFeature:"Preview feature"},It=t.i18n.registerUIStrings("ui/legacy/TabbedPane.ts",Et),St=t.i18n.getLocalizedString.bind(void 0,It);class kt extends(e.ObjectWrapper.eventMixin(lt)){headerElementInternal;headerContentsElement;tabSlider;tabsElement;contentElementInternal;tabs;tabsHistory;tabsById;currentTabLocked;autoSelectFirstItemOnShow;triggerDropDownTimeout;dropDownButton;currentDevicePixelRatio;shrinkableTabs;verticalTabLayout;closeableTabs;delegate;currentTab;sliderEnabled;placeholderElement;focusedPlaceholderElement;placeholderContainerElement;lastSelectedOverflowTab;overflowDisabled;measuredDropDownButtonWidth;leftToolbarInternal;rightToolbarInternal;allowTabReorder;automaticReorder;constructor(){super(!0),this.registerRequiredCSS(yt),this.element.classList.add("tabbed-pane"),this.contentElement.classList.add("tabbed-pane-shadow"),this.contentElement.tabIndex=-1,this.setDefaultFocusedElement(this.contentElement),this.headerElementInternal=this.contentElement.createChild("div","tabbed-pane-header"),this.headerContentsElement=this.headerElementInternal.createChild("div","tabbed-pane-header-contents"),this.tabSlider=document.createElement("div"),this.tabSlider.classList.add("tabbed-pane-tab-slider"),this.tabsElement=this.headerContentsElement.createChild("div","tabbed-pane-header-tabs"),this.tabsElement.setAttribute("role","tablist"),this.tabsElement.addEventListener("keydown",this.keyDown.bind(this),!1),this.contentElementInternal=this.contentElement.createChild("div","tabbed-pane-content"),this.contentElementInternal.createChild("slot"),this.tabs=[],this.tabsHistory=[],this.tabsById=new Map,this.currentTabLocked=!1,this.autoSelectFirstItemOnShow=!0,this.triggerDropDownTimeout=null,this.dropDownButton=this.createDropDownButton(),this.currentDevicePixelRatio=window.devicePixelRatio,pt.instance().addEventListener("ZoomChanged",this.zoomChanged,this),this.makeTabSlider()}setAccessibleName(e){or(this.tabsElement,e)}setCurrentTabLocked(e){this.currentTabLocked=e,this.headerElementInternal.classList.toggle("locked",this.currentTabLocked)}setAutoSelectFirstItemOnShow(e){this.autoSelectFirstItemOnShow=e}get visibleView(){return this.currentTab?this.currentTab.view:null}tabIds(){return this.tabs.map((e=>e.id))}tabIndex(e){return this.tabs.findIndex((t=>t.id===e))}tabViews(){return this.tabs.map((e=>e.view))}tabView(e){const t=this.tabsById.get(e);return t?t.view:null}get selectedTabId(){return this.currentTab?this.currentTab.id:null}setShrinkableTabs(e){this.shrinkableTabs=e}makeVerticalTabLayout(){this.verticalTabLayout=!0,this.setTabSlider(!1),this.contentElement.classList.add("vertical-tab-layout"),this.invalidateConstraints()}setCloseableTabs(e){this.closeableTabs=e}focus(){this.visibleView?this.visibleView.focus():this.contentElement.focus()}focusSelectedTabHeader(){const e=this.currentTab;e&&e.tabElement.focus()}headerElement(){return this.headerElementInternal}tabbedPaneContentElement(){return this.contentElementInternal}isTabCloseable(e){const t=this.tabsById.get(e);return!!t&&t.isCloseable()}setTabDelegate(e){const t=this.tabs.slice();for(let n=0;n<t.length;++n)t[n].setDelegate(e);this.delegate=e}appendTab(e,t,n,i,r,o,a,l,c){const d="boolean"==typeof o?o:Boolean(this.closeableTabs),h=new Tt(this,e,t,d,Boolean(a),n,i,c);h.setDelegate(this.delegate),console.assert(!this.tabsById.has(e),`Tabbed pane already contains a tab with id '${e}'`),this.tabsById.set(e,h),h.tabElement.tabIndex=-1,h.tabElement.setAttribute("jslog",`${s.panelTabHeader().track({click:!0,drag:!0}).context(h.jslogContext)}`),void 0!==l?this.tabs.splice(l,0,h):this.tabs.push(h),this.tabsHistory.push(h),this.tabsHistory[0]===h&&this.isShowing()&&this.selectTab(h.id,r),this.updateTabElements()}closeTab(e,t){this.closeTabs([e],t)}closeTabs(e,t){if(0===e.length)return;const n=this.hasFocus();for(let n=0;n<e.length;++n)this.innerCloseTab(e[n],t);this.updateTabElements(),this.tabsHistory.length&&this.selectTab(this.tabsHistory[0].id,!1),n&&this.focus()}innerCloseTab(e,t){const n=this.tabsById.get(e);if(!n)return;if(t&&!n.closeable)return;this.currentTab&&this.currentTab.id===e&&this.hideCurrentTab(),this.tabsById.delete(e),this.tabsHistory.splice(this.tabsHistory.indexOf(n),1),this.tabs.splice(this.tabs.indexOf(n),1),n.shown&&this.hideTabElement(n);const i={prevTabId:void 0,tabId:e,view:n.view,isUserGesture:t};return this.dispatchEventToListeners(Ct.TabClosed,i),!0}hasTab(e){return this.tabsById.has(e)}otherTabs(e){const t=[];for(let n=0;n<this.tabs.length;++n)this.tabs[n].id!==e&&t.push(this.tabs[n].id);return t}tabsToTheRight(e){let t=-1;for(let n=0;n<this.tabs.length;++n)if(this.tabs[n].id===e){t=n;break}return-1===t?[]:this.tabs.slice(t+1).map((function(e){return e.id}))}viewHasFocus(){if(this.visibleView&&this.visibleView.hasFocus())return!0;const e=this.contentElement.getComponentRoot();return e instanceof Document&&this.contentElement===e.activeElement}selectTab(e,t,n){if(this.currentTabLocked)return!1;const i=this.viewHasFocus(),s=this.tabsById.get(e);if(!s)return!1;const r={prevTabId:this.currentTab?this.currentTab.id:void 0,tabId:e,view:s.view,isUserGesture:t};return this.dispatchEventToListeners(Ct.TabInvoked,r),this.currentTab&&this.currentTab.id===e||(this.suspendInvalidations(),this.hideCurrentTab(),this.showTab(s),this.resumeInvalidations(),this.currentTab=s,this.tabsHistory.splice(this.tabsHistory.indexOf(s),1),this.tabsHistory.splice(0,0,s),this.updateTabElements(),(i||n)&&this.focus(),this.dispatchEventToListeners(Ct.TabSelected,r)),!0}selectNextTab(){const e=this.tabs.indexOf(this.currentTab),t=n.NumberUtilities.mod(e+1,this.tabs.length);this.selectTab(this.tabs[t].id,!0)}selectPrevTab(){const e=this.tabs.indexOf(this.currentTab),t=n.NumberUtilities.mod(e-1,this.tabs.length);this.selectTab(this.tabs[t].id,!0)}lastOpenedTabIds(e){return this.tabsHistory.slice(0,e).map((function(e){return e.id}))}setTabIcon(e,t){const n=this.tabsById.get(e);n&&(n.setIcon(t),this.updateTabElements())}setTabEnabled(e,t){const n=this.tabsById.get(e);n&&n.tabElement.classList.toggle("disabled",!t)}toggleTabClass(e,t,n){const i=this.tabsById.get(e);i&&i.toggleClass(t,n)&&this.updateTabElements()}zoomChanged(){this.clearMeasuredWidths(),this.isShowing()&&this.updateTabElements()}clearMeasuredWidths(){for(let e=0;e<this.tabs.length;++e)delete this.tabs[e].measuredWidth}changeTabTitle(e,t,n){const i=this.tabsById.get(e);i&&void 0!==n&&(i.tooltip=n),i&&i.title!==t&&(i.title=t,or(i.tabElement,t),this.updateTabElements())}changeTabView(e,t){const n=this.tabsById.get(e);if(!n||n.view===t)return;this.suspendInvalidations();const i=this.currentTab&&this.currentTab.id===e,s=n.view.hasFocus();i&&this.hideTab(n),n.view=t,i&&this.showTab(n),s&&n.view.focus(),this.resumeInvalidations()}onResize(){this.currentDevicePixelRatio!==window.devicePixelRatio&&(this.clearMeasuredWidths(),this.currentDevicePixelRatio=window.devicePixelRatio),this.updateTabElements()}headerResized(){this.updateTabElements()}wasShown(){const e=this.currentTab||this.tabsHistory[0];e&&this.autoSelectFirstItemOnShow&&this.selectTab(e.id)}makeTabSlider(){this.verticalTabLayout||this.setTabSlider(!0)}setTabSlider(e){this.sliderEnabled=e,this.tabSlider.classList.toggle("enabled",e)}calculateConstraints(){let e=super.calculateConstraints();const t=new K(new _(0,0),new _(50,50));return e=e.widthToMax(t).heightToMax(t),e=this.verticalTabLayout?e.addWidth(new K(new _(120,0))):e.addHeight(new K(new _(0,30))),e}updateTabElements(){Hi(this,this.innerUpdateTabElements)}setPlaceholderElement(e,t){this.placeholderElement=e,t&&(this.focusedPlaceholderElement=t),this.placeholderContainerElement&&(this.placeholderContainerElement.removeChildren(),this.placeholderContainerElement.appendChild(e))}async waitForTabElementUpdate(){this.innerUpdateTabElements()}innerUpdateTabElements(){this.isShowing()&&(this.tabs.length?(this.contentElementInternal.classList.remove("has-no-tabs"),this.placeholderContainerElement&&(this.placeholderContainerElement.remove(),this.setDefaultFocusedElement(this.contentElement),delete this.placeholderContainerElement)):(this.contentElementInternal.classList.add("has-no-tabs"),this.placeholderElement&&!this.placeholderContainerElement&&(this.placeholderContainerElement=this.contentElementInternal.createChild("div","tabbed-pane-placeholder fill"),this.placeholderContainerElement.appendChild(this.placeholderElement),this.focusedPlaceholderElement&&this.setDefaultFocusedElement(this.focusedPlaceholderElement))),this.measureDropDownButton(),this.adjustToolbarWidth(),this.updateWidths(),this.updateTabsDropDown(),this.updateTabSlider())}adjustToolbarWidth(){if(!this.rightToolbarInternal||!this.measuredDropDownButtonWidth)return;const e=this.leftToolbarInternal?.element.getBoundingClientRect().width??0,t=this.rightToolbarInternal.element.getBoundingClientRect().width,n=this.headerElementInternal.getBoundingClientRect().width;!this.rightToolbarInternal.hasCompactLayout()&&n-t-e<this.measuredDropDownButtonWidth+10?this.rightToolbarInternal.setCompactLayout(!0):this.rightToolbarInternal.hasCompactLayout()&&n-2*t-e>this.measuredDropDownButtonWidth+10&&this.rightToolbarInternal.setCompactLayout(!1)}showTabElement(e,t){e>=this.tabsElement.children.length?this.tabsElement.appendChild(t.tabElement):this.tabsElement.insertBefore(t.tabElement,this.tabsElement.children[e]),t.shown=!0}hideTabElement(e){this.tabsElement.removeChild(e.tabElement),e.shown=!1}createDropDownButton(){const e=document.createElement("div");e.classList.add("tabbed-pane-header-tabs-drop-down-container"),e.setAttribute("jslog",`${s.dropDown("more-tabs").track({click:!0})}`);const t=l.Icon.create("chevron-double-right","chevron-icon"),n=St(Et.moreTabs);return e.title=n,Ps(e),or(e,n),Qs(e,!1),e.tabIndex=0,e.appendChild(t),e.addEventListener("click",this.dropDownClicked.bind(this)),e.addEventListener("keydown",this.dropDownKeydown.bind(this)),e.addEventListener("mousedown",(e=>{0!==e.button||this.triggerDropDownTimeout||(this.triggerDropDownTimeout=window.setTimeout(this.dropDownClicked.bind(this,e),200))})),e}dropDownClicked(e){const t=e;if(0!==t.button)return;this.triggerDropDownTimeout&&(clearTimeout(this.triggerDropDownTimeout),this.triggerDropDownTimeout=null);const n=this.dropDownButton.getBoundingClientRect(),i=new xn(t,{useSoftMenu:!1,x:n.left,y:n.bottom,onSoftMenuClosed:()=>{Qs(this.dropDownButton,!1)}});for(const e of this.tabs)e.shown||(0===this.numberOfTabsShown()&&this.tabsHistory[0]===e?i.defaultSection().appendCheckboxItem(e.title,this.dropDownMenuItemSelected.bind(this,e),{checked:!0,jslogContext:e.jslogContext}):i.defaultSection().appendItem(e.title,this.dropDownMenuItemSelected.bind(this,e),{jslogContext:e.jslogContext}));i.show().then((()=>Qs(this.dropDownButton,i.isHostedMenuOpen())))}dropDownKeydown(e){n.KeyboardUtilities.isEnterOrSpaceKey(e)&&(this.dropDownButton.click(),e.consume(!0))}dropDownMenuItemSelected(e){this.lastSelectedOverflowTab=e,this.selectTab(e.id,!0,!0)}totalWidth(){return this.headerContentsElement.getBoundingClientRect().width}numberOfTabsShown(){let e=0;for(const t of this.tabs)t.shown&&e++;return e}disableOverflowMenu(){this.overflowDisabled=!0}updateTabsDropDown(){const e=this.tabsToShowIndexes(this.tabs,this.tabsHistory,this.totalWidth(),this.measuredDropDownButtonWidth||0);if(this.lastSelectedOverflowTab&&this.numberOfTabsShown()!==e.length)return delete this.lastSelectedOverflowTab,void this.updateTabsDropDown();for(let t=0;t<this.tabs.length;++t)this.tabs[t].shown&&-1===e.indexOf(t)&&this.hideTabElement(this.tabs[t]);for(let t=0;t<e.length;++t){const n=this.tabs[e[t]];n.shown||this.showTabElement(t,n)}this.overflowDisabled||this.maybeShowDropDown(e.length!==this.tabs.length)}maybeShowDropDown(e){e&&!this.dropDownButton.parentElement?this.headerContentsElement.appendChild(this.dropDownButton):!e&&this.dropDownButton.parentElement&&this.headerContentsElement.removeChild(this.dropDownButton)}measureDropDownButton(){this.overflowDisabled||this.measuredDropDownButtonWidth||(this.dropDownButton.classList.add("measuring"),this.headerContentsElement.appendChild(this.dropDownButton),this.measuredDropDownButtonWidth=this.dropDownButton.getBoundingClientRect().width,this.headerContentsElement.removeChild(this.dropDownButton),this.dropDownButton.classList.remove("measuring"))}updateWidths(){const e=this.measureWidths(),t=this.shrinkableTabs?this.calculateMaxWidth(e.slice(),this.totalWidth()):Number.MAX_VALUE;let n=0;for(const i of this.tabs)i.setWidth(this.verticalTabLayout?-1:Math.min(t,e[n++]))}measureWidths(){this.tabsElement.style.setProperty("width","2000px");const e=new Map;for(const t of this.tabs){if("number"==typeof t.measuredWidth)continue;const n=t.createTabElement(!0);e.set(n,t),this.tabsElement.appendChild(n)}for(const[t,n]of e){const e=t.getBoundingClientRect().width;n.measuredWidth=Math.ceil(e)}for(const t of e.keys())t.remove();const t=[];for(const e of this.tabs)t.push(e.measuredWidth||0);return this.tabsElement.style.removeProperty("width"),t}calculateMaxWidth(e,t){if(!e.length)return 0;e.sort((function(e,t){return e-t}));let n=0;for(let t=0;t<e.length;++t)n+=e[t];if(t>=n)return e[e.length-1];let i=0;for(let s=e.length-1;s>0;--s){const r=e[s]-e[s-1];if(i+=(e.length-s)*r,t+i>=n)return e[s-1]+(t+i-n)/(e.length-s)}return t/e.length}tabsToShowIndexes(e,t,n,i){const s=[];let r=0;const o=e.length,a=e.slice(0);void 0!==this.currentTab&&a.unshift(a.splice(a.indexOf(this.currentTab),1)[0]),void 0!==this.lastSelectedOverflowTab&&a.unshift(a.splice(a.indexOf(this.lastSelectedOverflowTab),1)[0]);for(let l=0;l<o;++l){const c=this.automaticReorder?t[l]:a[l];r+=c.width();let d=r;if(l!==o-1&&(d+=i),!this.verticalTabLayout&&d>n)break;s.push(e.indexOf(c))}return s.sort((function(e,t){return e-t})),s}hideCurrentTab(){this.currentTab&&(this.hideTab(this.currentTab),delete this.currentTab)}showTab(e){e.tabElement.tabIndex=0,e.tabElement.classList.add("selected"),nr(e.tabElement,!0),e.view.show(this.element),this.updateTabSlider()}updateTabSlider(){if(!this.sliderEnabled)return;if(!this.currentTab)return void(this.tabSlider.style.width="0");let e=0;for(let t=0;t<this.tabs.length&&this.currentTab!==this.tabs[t];t++)this.tabs[t].shown&&(e+=this.tabs[t].measuredWidth||0);const t=this.currentTab.shown?this.currentTab.measuredWidth:this.dropDownButton.offsetWidth,n=window.devicePixelRatio>=1.5?" scaleY(0.75)":"";this.tabSlider.style.transform="translateX("+e+"px)"+n,this.tabSlider.style.width=t+"px",this.tabSlider.parentElement!==this.headerContentsElement&&this.headerContentsElement.appendChild(this.tabSlider)}hideTab(e){e.tabElement.removeAttribute("tabIndex"),e.tabElement.classList.remove("selected"),e.tabElement.tabIndex=-1,e.tabElement.setAttribute("aria-selected","false"),e.view.detach()}elementsToRestoreScrollPositionsFor(){return[this.contentElementInternal]}insertBefore(e,t){this.tabsElement.insertBefore(e.tabElement,this.tabsElement.childNodes[t]);const n=this.tabs.indexOf(e);this.tabs.splice(n,1),n<t&&--t,this.tabs.splice(t,0,e);const i={prevTabId:void 0,tabId:e.id,view:e.view,isUserGesture:void 0};this.dispatchEventToListeners(Ct.TabOrderChanged,i)}leftToolbar(){return this.leftToolbarInternal||(this.leftToolbarInternal=new Xn("tabbed-pane-left-toolbar"),this.headerElementInternal.insertBefore(this.leftToolbarInternal.element,this.headerElementInternal.firstChild)),this.leftToolbarInternal}rightToolbar(){return this.rightToolbarInternal||(this.rightToolbarInternal=new Xn("tabbed-pane-right-toolbar"),this.headerElementInternal.appendChild(this.rightToolbarInternal.element)),this.rightToolbarInternal}setAllowTabReorder(e,t){this.allowTabReorder=e,this.automaticReorder=t}keyDown(e){if(!this.currentTab)return;let t=null;switch(e.key){case"ArrowUp":case"ArrowLeft":t=this.currentTab.tabElement.previousElementSibling,t||this.dropDownButton.parentElement||(t=this.currentTab.tabElement.parentElement?this.currentTab.tabElement.parentElement.lastElementChild:null);break;case"ArrowDown":case"ArrowRight":t=this.currentTab.tabElement.nextElementSibling,t||this.dropDownButton.parentElement||(t=this.currentTab.tabElement.parentElement?this.currentTab.tabElement.parentElement.firstElementChild:null);break;case"Enter":case" ":return void this.currentTab.view.focus();default:return}if(!t)return void this.dropDownButton.click();const n=this.tabs.find((e=>e.tabElement===t));n&&this.selectTab(n.id,!0),t.focus()}}var Ct;!function(e){e.TabInvoked="TabInvoked",e.TabSelected="TabSelected",e.TabClosed="TabClosed",e.TabOrderChanged="TabOrderChanged"}(Ct||(Ct={}));class Tt{closeable;previewFeature=!1;tabbedPane;idInternal;titleInternal;tooltipInternal;viewInternal;shown;measuredWidth;tabElementInternal;icon=null;widthInternal;delegate;titleElement;dragStartX;jslogContextInternal;constructor(e,t,n,i,s,r,o,a){this.closeable=i,this.previewFeature=s,this.tabbedPane=e,this.idInternal=t,this.titleInternal=n,this.tooltipInternal=o,this.viewInternal=r,this.shown=!1,this.jslogContextInternal=a}get id(){return this.idInternal}get title(){return this.titleInternal}set title(e){if(e!==this.titleInternal){if(this.titleInternal=e,this.titleElement){this.titleElement.textContent=e;const t=this.tabElementInternal?.querySelector(".close-button");t?.setAttribute("title",St(Et.closeS,{PH1:e})),t?.setAttribute("aria-label",St(Et.closeS,{PH1:e}))}delete this.measuredWidth}}get jslogContext(){return this.jslogContextInternal??("console-view"===this.idInternal?"console":this.idInternal)}isCloseable(){return this.closeable}setIcon(e){this.icon=e,this.tabElementInternal&&this.titleElement&&this.createIconElement(this.tabElementInternal,this.titleElement,!1),delete this.measuredWidth}toggleClass(e,t){const n=this.tabElement;return n.classList.contains(e)!==t&&(n.classList.toggle(e,t),delete this.measuredWidth,!0)}get view(){return this.viewInternal}set view(e){this.viewInternal=e}get tooltip(){return this.tooltipInternal}set tooltip(e){this.tooltipInternal=e,this.titleElement&&wt.install(this.titleElement,e||"")}get tabElement(){return this.tabElementInternal||(this.tabElementInternal=this.createTabElement(!1)),this.tabElementInternal}width(){return this.widthInternal||0}setWidth(e){this.tabElement.style.width=-1===e?"":e+"px",this.widthInternal=e}setDelegate(e){this.delegate=e}createIconElement(e,t,n){const i=Lt.get(e);if(i&&(i.remove(),Lt.delete(e)),!this.icon)return;const s=document.createElement("span");s.classList.add("tabbed-pane-header-tab-icon");const r=n?this.createMeasureClone(this.icon):this.icon;s.appendChild(r),e.insertBefore(s,t),Lt.set(e,s)}createMeasureClone(e){const t=document.createElement("div");return t.style.width=e.style.width,t.style.height=e.style.height,t}createTabElement(e){const t=document.createElement("div");t.classList.add("tabbed-pane-header-tab"),t.id="tab-"+this.idInternal,Ds(t),nr(t,!1),or(t,this.title);const n=t.createChild("span","tabbed-pane-header-tab-title");if(n.textContent=this.title,wt.install(n,this.tooltip||""),this.createIconElement(t,n,e),e||(this.titleElement=n),this.previewFeature){const e=this.createPreviewIcon();t.appendChild(e),t.classList.add("preview")}if(this.closeable){const e=this.createCloseIconButton();t.appendChild(e),t.classList.add("closeable")}return e?t.classList.add("measuring"):(t.addEventListener("click",this.tabClicked.bind(this),!1),t.addEventListener("auxclick",this.tabClicked.bind(this),!1),t.addEventListener("mousedown",this.tabMouseDown.bind(this),!1),t.addEventListener("mouseup",this.tabMouseUp.bind(this),!1),t.addEventListener("contextmenu",this.tabContextMenu.bind(this),!1),this.tabbedPane.allowTabReorder&&mi(t,this.startTabDragging.bind(this),this.tabDragging.bind(this),this.endTabDragging.bind(this),null,null,200)),t}createCloseIconButton(){const e=document.createElement("button");e.classList.add("close-button","tabbed-pane-close-button"),e.setAttribute("jslog",`${s.close().track({click:!0})}`);const t=new l.Icon.Icon;return t.data={iconName:"cross",color:"var(--tabbed-pane-close-icon-color)",width:"16px"},e.appendChild(t),e.setAttribute("role","button"),e.setAttribute("title",St(Et.closeS,{PH1:this.title})),e.setAttribute("aria-label",St(Et.closeS,{PH1:this.title})),e}createPreviewIcon(){const e=document.createElement("div");e.classList.add("preview-icon");const t=new l.Icon.Icon;return t.data={iconName:"experiment",color:"var(--override-tabbed-pane-preview-icon-color)",width:"16px"},e.appendChild(t),e.setAttribute("title",St(Et.previewFeature)),e.setAttribute("aria-label",St(Et.previewFeature)),e}isCloseIconClicked(e){return e?.classList.contains("tabbed-pane-close-button")||e?.parentElement?.classList.contains("tabbed-pane-close-button")||!1}tabClicked(e){const t=e,n=1===t.button;this.closeable&&(n||this.isCloseIconClicked(t.target))?(this.closeTabs([this.id]),t.consume(!0)):this.tabbedPane.focus()}tabMouseDown(e){const t=e;this.isCloseIconClicked(t.target)||0!==t.button||this.tabbedPane.selectTab(this.id,!0)}tabMouseUp(e){const t=e;1===t.button&&t.consume(!0)}closeTabs(e){this.delegate?this.delegate.closeTabs(this.tabbedPane,e):this.tabbedPane.closeTabs(e,!0)}tabContextMenu(e){const t=new xn(e);this.closeable&&(t.defaultSection().appendItem(St(Et.close),function(){this.closeTabs([this.id])}.bind(this),{jslogContext:"close"}),t.defaultSection().appendItem(St(Et.closeOthers),function(){this.closeTabs(this.tabbedPane.otherTabs(this.id))}.bind(this),{jslogContext:"close-others"}),t.defaultSection().appendItem(St(Et.closeTabsToTheRight),function(){this.closeTabs(this.tabbedPane.tabsToTheRight(this.id))}.bind(this),{jslogContext:"close-tabs-to-the-right"}),t.defaultSection().appendItem(St(Et.closeAll),function(){this.closeTabs(this.tabbedPane.tabIds())}.bind(this),{jslogContext:"close-all"})),this.delegate&&this.delegate.onContextMenu(this.id,t),t.show()}startTabDragging(e){const t=e;return!this.isCloseIconClicked(t.target)&&(this.dragStartX=t.pageX,this.tabElementInternal&&this.tabElementInternal.classList.add("dragging"),this.tabbedPane.tabSlider.remove(),!0)}tabDragging(e){const t=e,n=this.tabbedPane.tabsElement.childNodes;for(let e=0;e<n.length;++e){let i=n[e];if(!this.tabElementInternal||i===this.tabElementInternal)continue;if(!(i.offsetLeft+i.clientWidth>this.tabElementInternal.offsetLeft&&this.tabElementInternal.offsetLeft+this.tabElementInternal.clientWidth>i.offsetLeft))continue;const s=this.dragStartX;if(Math.abs(t.pageX-s)<i.clientWidth/2+5)break;t.pageX-s>0&&(i=i.nextSibling,++e);const r=this.tabElementInternal.offsetLeft;this.tabbedPane.insertBefore(this,e),this.dragStartX=s+this.tabElementInternal.offsetLeft-r;break}const i=this.dragStartX,s=this.tabElementInternal;!s.previousSibling&&t.pageX-i<0||!s.nextSibling&&t.pageX-i>0?s.style.setProperty("left","0px"):s.style.setProperty("left",t.pageX-i+"px")}endTabDragging(e){const t=this.tabElementInternal;t.classList.remove("dragging"),t.style.removeProperty("left"),delete this.dragStartX,this.tabbedPane.updateTabSlider()}}const Lt=new WeakMap;var Mt=Object.freeze({__proto__:null,TabbedPane:kt,get Events(){return Ct},TabbedPaneTab:Tt}),Pt={cssContent:".expandable-view-title{display:flex;align-items:center;background-color:var(--sys-color-surface2);height:22px;padding:0 5px;white-space:nowrap;overflow:hidden;position:relative;border-bottom:1px solid transparent}.expandable-view-title.expanded,\n.expandable-view-title:last-child{border-bottom:1px solid var(--sys-color-divider)}.expandable-view-title .toolbar{margin-top:-3px}.expandable-view-title > .toolbar{position:absolute;right:0;top:0}.expandable-view-title:not(.expanded) .toolbar{display:none}.title-expand-icon{margin-right:2px;margin-bottom:-2px}.expandable-view-title:focus-visible{background-color:var(--sys-color-state-focus-highlight)}@media (forced-colors: active){.expandable-view-title:focus-visible{forced-color-adjust:none;color:HighlightText;background-color:Highlight;box-shadow:0 0 0 2px Highlight inset}.expandable-view-title:focus-visible .title-expand-icon{color:HighlightText}}\n/*# sourceURL=viewContainers.css */\n"};const Dt={elements:"Elements",drawer:"Drawer",drawer_sidebar:"Drawer sidebar",panel:"Panel",network:"Network",settings:"Settings",sources:"Sources"},At=t.i18n.registerUIStrings("ui/legacy/ViewRegistration.ts",Dt),Rt=t.i18n.getLocalizedString.bind(void 0,At),Bt=[],zt=new Set;function Ot(e){return Bt.filter((t=>i.Runtime.Runtime.isDescriptorEnabled({experiment:t.experiment(),condition:t.condition()},e)))}const Ft=[],Ht=new Set;function Wt(){return Ft}const Nt={sPanel:"{PH1} panel"},jt=t.i18n.registerUIStrings("ui/legacy/ViewManager.ts",Nt),Vt=t.i18n.getLocalizedString.bind(void 0,jt),Ut={security:!0};class _t{viewRegistration;widgetPromise;constructor(e){this.viewRegistration=e,this.widgetPromise=null}title(){return this.viewRegistration.title()}commandPrompt(){return this.viewRegistration.commandPrompt()}isCloseable(){return"closeable"===this.viewRegistration.persistence}isPreviewFeature(){return Boolean(this.viewRegistration.isPreviewFeature)}iconName(){return this.viewRegistration.iconName}isTransient(){return"transient"===this.viewRegistration.persistence}viewId(){return this.viewRegistration.id}location(){return this.viewRegistration.location}order(){return this.viewRegistration.order}settings(){return this.viewRegistration.settings}tags(){if(this.viewRegistration.tags)return this.viewRegistration.tags.map((e=>e())).join("\0")}persistence(){return this.viewRegistration.persistence}async toolbarItems(){if(!this.viewRegistration.hasToolbar)return[];return(await this.widget()).toolbarItems()}widget(){return null===this.widgetPromise&&(this.widgetPromise=this.viewRegistration.loadView()),this.widgetPromise}async disposeView(){if(null===this.widgetPromise)return;const e=await this.widgetPromise;await e.ownerViewDisposed()}experiment(){return this.viewRegistration.experiment}condition(){return this.viewRegistration.condition}}let Kt;class qt{views;locationNameByViewId;locationOverrideSetting;constructor(){this.views=new Map,this.locationNameByViewId=new Map,this.locationOverrideSetting=e.Settings.Settings.instance().createSetting("views-location-override",{});const t=this.locationOverrideSetting.get(),i=new Map;for(const t of Ot(e.Settings.Settings.instance().getHostConfig())){const e=t.location()||"none",n=i.get(e)||[];n.push(t),i.set(e,n)}let s=[];for(const e of i.values())e.sort(((e,t)=>{const n=e.order(),i=t.order();return void 0!==n&&void 0!==i?n-i:0})),s=s.concat(e);for(const e of s){const i=e.viewId(),s=e.location();if(this.views.has(i))throw new Error(`Duplicate view id '${i}'`);if(!n.StringUtilities.isExtendedKebabCase(i))throw new Error(`Invalid view ID '${i}'`);this.views.set(i,e);const r=t[i]||s;this.locationNameByViewId.set(i,r)}}static instance(e={forceNew:null}){const{forceNew:t}=e;return Kt&&!t||(Kt=new qt),Kt}static removeInstance(){Kt=void 0}static createToolbar(e){if(!e.length)return null;const t=new Xn("");for(const n of e)t.appendToolbarItem(n);return t.element}locationNameForViewId(e){const t=this.locationNameByViewId.get(e);if(!t)throw new Error(`No location name for view with id ${e}`);return t}moveView(e,t,n){const{shouldSelectTab:i,overrideSaving:s}=n||{shouldSelectTab:!0,overrideSaving:!1};if(!e||!t)return;const r=this.view(e);if(r){if(!s){this.locationNameByViewId.set(e,t);const n=this.locationOverrideSetting.get();n[e]=t,this.locationOverrideSetting.set(n)}this.resolveLocation(t).then((t=>{if(!t)throw new Error("Move view: Could not resolve location for view: "+e);return t.reveal(),t.showView(r,void 0,!0,!1,i)}))}}revealView(e){const t=Zt.get(e);return t?(t.reveal(),t.showView(e)):Promise.resolve()}showViewInLocation(e,t,n=!0){this.moveView(e,t,{shouldSelectTab:n,overrideSaving:!0})}view(e){const t=this.views.get(e);if(!t)throw new Error(`No view with id ${e} found!`);return t}materializedWidget(e){const t=this.view(e);return t&&Gt.get(t)||null}async showView(e,t,n){const i=this.views.get(e);if(!i)return void console.error("Could not find view for id: '"+e+"' "+(new Error).stack);const s=Zt.get(i)??await this.resolveLocation(this.locationNameByViewId.get(e));if(!s)throw new Error("Could not resolve location for view: "+e);s.reveal(),await s.showView(i,void 0,t,n)}async resolveLocation(e){if(!e)return null;const t=Wt().filter((t=>t.name===e));if(t.length>1)throw new Error("Duplicate resolver for location: "+e);if(t.length){return(await t[0].loadResolver()).resolveLocation(e)}throw new Error("Unresolved location: "+e)}createTabbedLocation(e,t,n,i,s){return new Jt(this,e,t,n,i,s)}createStackLocation(e,t,n){return new en(this,e,t,n)}hasViewsForLocation(e){return Boolean(this.viewsForLocation(e).length)}viewsForLocation(e){const t=[];for(const[n,i]of this.views.entries())this.locationNameByViewId.get(n)===e&&t.push(i);return t}}const Gt=new WeakMap;class $t extends lt{view;materializePromise;constructor(e){super(),this.element.classList.add("flex-auto","view-container","overflow-auto"),this.view=e,this.element.tabIndex=-1,As(this.element),or(this.element,Vt(Nt.sPanel,{PH1:e.title()})),this.setDefaultFocusedElement(this.element)}materialize(){if(this.materializePromise)return this.materializePromise;const e=[];return e.push(this.view.toolbarItems().then((e=>{const t=qt.createToolbar(e);t&&this.element.insertBefore(t,this.element.firstChild)}))),e.push(this.view.widget().then((e=>{const t=this.element.hasFocus();this.setDefaultFocusedElement(null),Gt.set(this.view,e),e.show(this.element),t&&e.focus()}))),this.materializePromise=Promise.all(e).then((()=>{})),this.materializePromise}wasShown(){this.materialize().then((()=>{const e=Gt.get(this.view);e&&(e.show(this.element),this.wasShownForTest())}))}wasShownForTest(){}}class Xt extends lt{titleElement;titleExpandIcon;view;widget;materializePromise;constructor(e){super(!0),this.element.classList.add("flex-none"),this.registerRequiredCSS(Pt),this.titleElement=document.createElement("div"),this.titleElement.classList.add("expandable-view-title"),this.titleElement.setAttribute("jslog",`${s.sectionHeader().context(e.viewId()).track({click:!0,keydown:"Enter|Space|ArrowLeft|ArrowRight"})}`),Bs(this.titleElement),this.titleExpandIcon=l.Icon.create("triangle-right","title-expand-icon"),this.titleElement.appendChild(this.titleExpandIcon);const t=e.title();ji(this.titleElement,t),or(this.titleElement,t),Qs(this.titleElement,!1),this.titleElement.tabIndex=0,self.onInvokeElement(this.titleElement,this.toggleExpanded.bind(this)),this.titleElement.addEventListener("keydown",this.onTitleKeyDown.bind(this),!1),this.contentElement.insertBefore(this.titleElement,this.contentElement.firstChild),Ys(this.titleElement,this.contentElement.createChild("slot")),this.view=e,Yt.set(e,this)}wasShown(){this.widget&&this.materializePromise&&this.materializePromise.then((()=>{this.titleElement.classList.contains("expanded")&&this.widget&&this.widget.show(this.element)}))}materialize(){if(this.materializePromise)return this.materializePromise;const e=[];return e.push(this.view.toolbarItems().then((e=>{const t=qt.createToolbar(e);t&&this.titleElement.appendChild(t)}))),e.push(this.view.widget().then((e=>{this.widget=e,Gt.set(this.view,e),e.show(this.element)}))),this.materializePromise=Promise.all(e).then((()=>{})),this.materializePromise}expand(){return this.titleElement.classList.contains("expanded")?this.materialize():(this.titleElement.classList.add("expanded"),Qs(this.titleElement,!0),this.titleExpandIcon.name="triangle-down",this.materialize().then((()=>{this.widget&&this.widget.show(this.element)})))}collapse(){this.titleElement.classList.contains("expanded")&&(this.titleElement.classList.remove("expanded"),Qs(this.titleElement,!1),this.titleExpandIcon.name="triangle-right",this.materialize().then((()=>{this.widget&&this.widget.detach()})))}toggleExpanded(e){"keydown"===e.type&&e.target!==this.titleElement||(this.titleElement.classList.contains("expanded")?this.collapse():this.expand())}onTitleKeyDown(e){if(e.target!==this.titleElement)return;const t=e;"ArrowLeft"===t.key?this.collapse():"ArrowRight"===t.key&&(this.titleElement.classList.contains("expanded")?this.widget&&this.widget.focus():this.expand())}}const Yt=new WeakMap;class Qt{manager;revealCallback;widgetInternal;constructor(e,t,n){this.manager=e,this.revealCallback=n,this.widgetInternal=t}widget(){return this.widgetInternal}reveal(){this.revealCallback&&this.revealCallback()}showView(e,t,n,i,s){throw new Error("not implemented")}removeView(e){throw new Error("not implemented")}}const Zt=new WeakMap;class Jt extends Qt{tabbedPaneInternal;allowReorder;closeableTabSetting;tabOrderSetting;lastSelectedTabSetting;defaultTab;views;constructor(t,n,i,s,r,o){const a=new kt;r&&a.setAllowTabReorder(!0),super(t,a,n),this.tabbedPaneInternal=a,this.allowReorder=r,this.tabbedPaneInternal.addEventListener(Ct.TabSelected,this.tabSelected,this),this.tabbedPaneInternal.addEventListener(Ct.TabClosed,this.tabClosed,this),this.closeableTabSetting=e.Settings.Settings.instance().createSetting("closeable-tabs",{}),this.setOrUpdateCloseableTabsSetting(),this.tabOrderSetting=e.Settings.Settings.instance().createSetting(i+"-tab-order",{}),this.tabbedPaneInternal.addEventListener(Ct.TabOrderChanged,this.persistTabOrder,this),s&&(this.lastSelectedTabSetting=e.Settings.Settings.instance().createSetting(i+"-selected-tab","")),this.defaultTab=o,this.views=new Map,i&&this.appendApplicableItems(i)}setOrUpdateCloseableTabsSetting(){const e={...Ut,...this.closeableTabSetting.get()};this.closeableTabSetting.set(e)}widget(){return this.tabbedPaneInternal}tabbedPane(){return this.tabbedPaneInternal}enableMoreTabsButton(){const e=new si(this.appendTabsToMenu.bind(this),!0,void 0,"more-tabs");return e.setGlyph("dots-vertical"),this.tabbedPaneInternal.leftToolbar().appendToolbarItem(e),this.tabbedPaneInternal.disableOverflowMenu(),e}appendApplicableItems(e){const t=this.manager.viewsForLocation(e);if(this.allowReorder){let e=0;const n=this.tabOrderSetting.get(),i=new Map;for(const s of t)i.set(s.viewId(),n[s.viewId()]||++e*Jt.orderStep);t.sort(((e,t)=>i.get(e.viewId())-i.get(t.viewId())))}for(const e of t){const t=e.viewId();this.views.set(t,e),Zt.set(e,this),e.isTransient()||(e.isCloseable()?this.closeableTabSetting.get()[t]&&this.appendTab(e):this.appendTab(e))}if(this.defaultTab)if(this.tabbedPaneInternal.hasTab(this.defaultTab))this.tabbedPaneInternal.selectTab(this.defaultTab);else{const e=Array.from(this.views.values()).find((e=>e.viewId()===this.defaultTab));e&&this.showView(e)}else this.lastSelectedTabSetting&&this.tabbedPaneInternal.hasTab(this.lastSelectedTabSetting.get())&&this.tabbedPaneInternal.selectTab(this.lastSelectedTabSetting.get())}appendTabsToMenu(e){const t=Array.from(this.views.values());t.sort(((e,t)=>e.title().localeCompare(t.title())));for(const n of t){const t=n.title();"issues-pane"!==n.viewId()?e.defaultSection().appendItem(t,this.showView.bind(this,n,void 0,!0),{jslogContext:n.viewId()}):e.defaultSection().appendItem(t,(()=>{r.userMetrics.issuesPanelOpenedFrom(3),this.showView(n,void 0,!0)}),{jslogContext:"issues-pane"})}}appendTab(e,t){this.tabbedPaneInternal.appendTab(e.viewId(),e.title(),new $t(e),void 0,!1,e.isCloseable()||e.isTransient(),e.isPreviewFeature(),t);const n=e.iconName();if(n){const t=l.Icon.create(n);this.tabbedPaneInternal.setTabIcon(e.viewId(),t)}}appendView(e,t){if(this.tabbedPaneInternal.hasTab(e.viewId()))return;const n=Zt.get(e);let i;n&&n!==this&&n.removeView(e),Zt.set(e,this),this.manager.views.set(e.viewId(),e),this.views.set(e.viewId(),e);const s=this.tabbedPaneInternal.tabIds();if(this.allowReorder){const t=this.tabOrderSetting.get(),n=t[e.viewId()];for(let e=0;n&&e<s.length;++e)if(t[s[e]]&&t[s[e]]>n){i=e;break}}else if(t)for(let e=0;e<s.length;++e)if(s[e]===t.viewId()){i=e;break}if(this.appendTab(e,i),e.isCloseable()){const t=this.closeableTabSetting.get(),n=e.viewId();t[n]||(t[n]=!0,this.closeableTabSetting.set(t))}this.persistTabOrder()}async showView(e,t,n,i,s=!0){this.appendView(e,t),s&&this.tabbedPaneInternal.selectTab(e.viewId(),n),i||this.tabbedPaneInternal.focus();const r=this.tabbedPaneInternal.tabView(e.viewId());await r.materialize()}removeView(e){this.tabbedPaneInternal.hasTab(e.viewId())&&(Zt.delete(e),this.manager.views.delete(e.viewId()),this.tabbedPaneInternal.closeTab(e.viewId()),this.views.delete(e.viewId()))}tabSelected(e){const{tabId:t}=e.data;this.lastSelectedTabSetting&&e.data.isUserGesture&&this.lastSelectedTabSetting.set(t)}tabClosed(e){const{tabId:t}=e.data,n=this.closeableTabSetting.get();n[t]&&(n[t]=!1,this.closeableTabSetting.set(n));const i=this.views.get(t);i&&i.disposeView()}persistTabOrder(){const e=this.tabbedPaneInternal.tabIds(),t={};for(let n=0;n<e.length;n++)t[e[n]]=(n+1)*Jt.orderStep;const n=this.tabOrderSetting.get(),i=Object.keys(n);i.sort(((e,t)=>n[e]-n[t]));let s=0;for(const e of i)e in t?s=t[e]:t[e]=++s;this.tabOrderSetting.set(t)}getCloseableTabSetting(){return this.closeableTabSetting.get()}static orderStep=10}class en extends Qt{vbox;expandableContainers;constructor(e,t,n,i){const r=new lt;r.element.setAttribute("jslog",`${s.pane(i||"sidebar").track({resize:!0})}`),super(e,r,t),this.vbox=r,Rs(r.element),this.expandableContainers=new Map,n&&this.appendApplicableItems(n)}appendView(e,t){const n=Zt.get(e);n&&n!==this&&n.removeView(e);let i=this.expandableContainers.get(e.viewId());if(!i){Zt.set(e,this),this.manager.views.set(e.viewId(),e),i=new Xt(e);let n=null;if(t){const e=Yt.get(t);n=e?e.element:null}i.show(this.vbox.contentElement,n),this.expandableContainers.set(e.viewId(),i)}}async showView(e,t){this.appendView(e,t);const n=this.expandableContainers.get(e.viewId());n&&await n.expand()}removeView(e){const t=this.expandableContainers.get(e.viewId());t&&(t.detach(),this.expandableContainers.delete(e.viewId()),Zt.delete(e),this.manager.views.delete(e.viewId()))}appendApplicableItems(e){for(const t of this.manager.viewsForLocation(e))this.appendView(t)}}var tn=Object.freeze({__proto__:null,defaultOptionsForTabs:Ut,PreRegisteredView:_t,ViewManager:qt,ContainerWidget:$t,getRegisteredViewExtensions:Ot,maybeRemoveViewExtension:function(e){const t=Bt.findIndex((t=>t.viewId()===e));return!(t<0||!zt.delete(e))&&(Bt.splice(t,1),!0)},registerViewExtension:function(e){const t=e.id;if(zt.has(t))throw new Error(`Duplicate view id '${t}'`);zt.add(t),Bt.push(new _t(e))},getRegisteredLocationResolvers:Wt,registerLocationResolver:function(e){const t=e.name;if(Ht.has(t))throw new Error(`Duplicate view location name registration '${t}'`);Ht.add(t),Ft.push(e)},getLocalizedViewLocationCategory:function(e){switch(e){case"ELEMENTS":return Rt(Dt.elements);case"DRAWER":return Rt(Dt.drawer);case"DRAWER_SIDEBAR":return Rt(Dt.drawer_sidebar);case"PANEL":return Rt(Dt.panel);case"NETWORK":return Rt(Dt.network);case"SETTINGS":return Rt(Dt.settings);case"SOURCES":return Rt(Dt.sources);case"":return t.i18n.lockedString("")}},resetViewRegistration:function(){Bt.length=0,Ft.length=0,Ht.clear(),zt.clear()}});const nn={moreTools:"More Tools",closeDrawer:"Close drawer",panels:"Panels",reloadDevtools:"Reload DevTools",moveToTop:"Move to top",moveToBottom:"Move to bottom",devToolsLanguageMissmatch:"DevTools is now available in {PH1}!",setToBrowserLanguage:"Always match Chrome's language",setToSpecificLanguage:"Switch DevTools to {PH1}",mainToolbar:"Main toolbar",drawer:"Tool drawer",drawerShown:"Drawer shown",drawerHidden:"Drawer hidden",selectOverrideFolder:"Select a folder to store override files in.",selectFolder:"Select folder"},sn=t.i18n.registerUIStrings("ui/legacy/InspectorView.ts",nn),rn=t.i18n.getLocalizedString.bind(void 0,sn);let on=null;class an extends lt{drawerSplitWidget;tabDelegate;drawerTabbedLocation;drawerTabbedPane;infoBarDiv;tabbedLocation;tabbedPane;keyDownBound;currentPanelLocked;focusRestorer;ownerSplitWidget;reloadRequiredInfobar;#t;constructor(){super(),ms.setContainer(this.element),this.setMinimumSize(250,72),this.drawerSplitWidget=new mt(!1,!0,"inspector.drawer-split-view-state",200,200),this.drawerSplitWidget.hideSidebar(),this.drawerSplitWidget.enableShowModeSaving(),this.drawerSplitWidget.show(this.element),this.tabDelegate=new dn,this.drawerTabbedLocation=qt.instance().createTabbedLocation(this.showDrawer.bind(this,{focus:!1,hasTargetDrawer:!0}),"drawer-view",!0,!0);this.drawerTabbedLocation.enableMoreTabsButton().setTitle(rn(nn.moreTools)),this.drawerTabbedPane=this.drawerTabbedLocation.tabbedPane(),this.drawerTabbedPane.setMinimumSize(0,27),this.drawerTabbedPane.element.classList.add("drawer-tabbed-pane"),this.drawerTabbedPane.element.setAttribute("jslog",`${s.drawer()}`);const n=new Jn(rn(nn.closeDrawer),"cross");n.element.setAttribute("jslog",`${s.close().track({click:!0})}`),n.addEventListener("Click",this.closeDrawer,this),this.drawerTabbedPane.addEventListener(Ct.TabSelected,(e=>this.tabSelected(e.data.tabId,"drawer")),this);const o=this.drawerTabbedPane.selectedTabId;"OnlyMain"!==this.drawerSplitWidget.showMode()&&o&&(r.userMetrics.panelShown(o,!0),r.userMetrics.panelShownInLocation(o,"drawer")),this.drawerTabbedPane.setTabDelegate(this.tabDelegate);const a=this.drawerTabbedPane.element;Ns(a),or(a,rn(nn.drawer)),this.drawerSplitWidget.installResizer(this.drawerTabbedPane.headerElement()),this.drawerSplitWidget.setSidebarWidget(this.drawerTabbedPane),this.drawerTabbedPane.rightToolbar().appendToolbarItem(n),this.drawerTabbedPane.headerElement().setAttribute("jslog",`${s.toolbar("drawer").track({drag:!0,keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`),this.tabbedLocation=qt.instance().createTabbedLocation(r.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront.bind(r.InspectorFrontendHost.InspectorFrontendHostInstance),"panel",!0,!0,i.Runtime.Runtime.queryParam("panel")),this.tabbedPane=this.tabbedLocation.tabbedPane(),this.tabbedPane.element.classList.add("main-tabbed-pane");const l=i.Runtime.conditions.canDock()?"69px":"41px";this.tabbedPane.leftToolbar().element.style.minWidth=l,this.tabbedPane.registerRequiredCSS(Ne),this.tabbedPane.addEventListener(Ct.TabSelected,(e=>this.tabSelected(e.data.tabId,"main")),this);const c=this.tabbedPane.selectedTabId;c&&(r.userMetrics.panelShown(c,!0),r.userMetrics.panelShownInLocation(c,"main")),this.tabbedPane.setAccessibleName(rn(nn.panels)),this.tabbedPane.setTabDelegate(this.tabDelegate);const d=this.tabbedPane.headerElement();if(js(d),or(d,rn(nn.mainToolbar)),d.setAttribute("jslog",`${s.toolbar("main").track({drag:!0,keydown:"ArrowUp|ArrowLeft|ArrowDown|ArrowRight|Enter|Space"})}`),r.userMetrics.setLaunchPanel(this.tabbedPane.selectedTabId),r.InspectorFrontendHost.isUnderTest()&&this.tabbedPane.setAutoSelectFirstItemOnShow(!1),this.drawerSplitWidget.setMainWidget(this.tabbedPane),this.keyDownBound=this.keyDown.bind(this),r.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(r.InspectorFrontendHostAPI.Events.ShowPanel,function({data:e}){this.showPanel(e)}.bind(this)),function(){if(ln().get())return!1;const n=e.Settings.Settings.instance().moduleSetting("language").get();if("en-US"!==n)return!1;return!t.DevToolsLocale.localeLanguagesMatch(navigator.language,n)&&t.DevToolsLocale.DevToolsLocale.instance().languageIsSupportedByDevTools(navigator.language)}()){const n=function(){const n=t.DevToolsLocale.DevToolsLocale.instance(),i=n.lookupClosestDevToolsLocale(navigator.language),s=new Intl.Locale(i),r=new Intl.DisplayNames([n.locale],{type:"language"}).of(s.language||"en")||"English",o=e.Settings.Settings.instance().moduleSetting("language");return new He("info",rn(nn.devToolsLanguageMissmatch,{PH1:r}),[{text:rn(nn.setToBrowserLanguage),highlight:!0,delegate:()=>{o.set("browserLanguage"),ln().set(!0),cn()},dismiss:!0,jslogContext:"set-to-browser-language"},{text:rn(nn.setToSpecificLanguage,{PH1:r}),highlight:!0,delegate:()=>{o.set(i),ln().set(!0),cn()},dismiss:!0,jslogContext:"set-to-specific-language"}],ln(),void 0,"language-mismatch")}();n.setParentView(this),this.attachInfobar(n)}}static instance(e={forceNew:null}){const{forceNew:t}=e;return on&&!t||(on=new an),on}static maybeGetInspectorViewInstance(){return on}static removeInstance(){on=null}wasShown(){this.element.ownerDocument.addEventListener("keydown",this.keyDownBound,!1)}willHide(){this.element.ownerDocument.removeEventListener("keydown",this.keyDownBound,!1)}resolveLocation(e){return"drawer-view"===e?this.drawerTabbedLocation:"panel"===e?this.tabbedLocation:null}async createToolbars(){await this.tabbedPane.leftToolbar().appendItemsAtLocation("main-toolbar-left"),await this.tabbedPane.rightToolbar().appendItemsAtLocation("main-toolbar-right")}addPanel(e){this.tabbedLocation.appendView(e)}hasPanel(e){return this.tabbedPane.hasTab(e)}async panel(e){const t=qt.instance().view(e);if(!t)throw new Error(`Expected view for panel '${e}'`);return t.widget()}onSuspendStateChanged(e){this.currentPanelLocked=e,this.tabbedPane.setCurrentTabLocked(this.currentPanelLocked),this.tabbedPane.leftToolbar().setEnabled(!this.currentPanelLocked),this.tabbedPane.rightToolbar().setEnabled(!this.currentPanelLocked)}canSelectPanel(e){return!this.currentPanelLocked||this.tabbedPane.selectedTabId===e}async showPanel(e){await qt.instance().showView(e)}setPanelWarnings(e,t){const n=this.getTabbedPaneForTabId(e);if(n){let i=null;if(0!==t.length){const e=1===t.length?t[0]:"· "+t.join("\n· ");i=l.Icon.create("warning-filled"),wt.install(i,e)}n.setTabIcon(e,i)}}emitDrawerChangeEvent(e){const t=new CustomEvent("drawerchange",{bubbles:!0,cancelable:!0,detail:{isDrawerOpen:e}});document.body.dispatchEvent(t)}getTabbedPaneForTabId(e){return this.tabbedPane.hasTab(e)?this.tabbedPane:this.drawerTabbedPane.hasTab(e)?this.drawerTabbedPane:null}currentPanelDeprecated(){return qt.instance().materializedWidget(this.tabbedPane.selectedTabId||"")}showDrawer({focus:e,hasTargetDrawer:t}){this.drawerTabbedPane.isShowing()||(this.drawerTabbedPane.setAutoSelectFirstItemOnShow(!t),this.drawerSplitWidget.showBoth(),this.focusRestorer=e?new dt(this.drawerTabbedPane):null,this.emitDrawerChangeEvent(!0),mr(rn(nn.drawerShown)))}drawerVisible(){return this.drawerTabbedPane.isShowing()}closeDrawer(){this.drawerTabbedPane.isShowing()&&(this.focusRestorer&&this.focusRestorer.restore(),this.drawerSplitWidget.hideSidebar(!0),this.emitDrawerChangeEvent(!1),mr(rn(nn.drawerHidden)))}setDrawerMinimized(e){this.drawerSplitWidget.setSidebarMinimized(e),this.drawerSplitWidget.setResizable(!e)}isDrawerMinimized(){return this.drawerSplitWidget.isSidebarMinimized()}closeDrawerTab(e,t){this.drawerTabbedPane.closeTab(e,t)}keyDown(t){const n=t;if(!ie.eventHasCtrlEquivalentKey(n)||n.altKey||n.shiftKey)return;if(e.Settings.moduleSetting("shortcut-panel-switch").get()){let e=-1;if(n.keyCode>48&&n.keyCode<58?e=n.keyCode-49:n.keyCode>96&&n.keyCode<106&&n.location===KeyboardEvent.DOM_KEY_LOCATION_NUMPAD&&(e=n.keyCode-97),-1!==e){const n=this.tabbedPane.tabIds()[e];n&&(ws.hasInstance()||this.currentPanelLocked||(this.showPanel(n),s.logKeyDown(null,t,`panel-by-index-${n}`)),t.consume(!0))}}}onResize(){ms.containerMoved(this.element)}topResizerElement(){return this.tabbedPane.headerElement()}toolbarItemResized(){this.tabbedPane.headerResized()}tabSelected(e,t){r.userMetrics.panelShown(e),r.userMetrics.panelShownInLocation(e,t)}setOwnerSplit(e){this.ownerSplitWidget=e}ownerSplit(){return this.ownerSplitWidget||null}minimize(){this.ownerSplitWidget&&this.ownerSplitWidget.setSidebarMinimized(!0)}restore(){this.ownerSplitWidget&&this.ownerSplitWidget.setSidebarMinimized(!1)}displayReloadRequiredWarning(e){if(!this.reloadRequiredInfobar){const t=new He("info",e,[{text:rn(nn.reloadDevtools),highlight:!0,delegate:()=>cn(),dismiss:!1,jslogContext:"main.debug-reload"}],void 0,void 0,"reload-required");t.setParentView(this),this.attachInfobar(t),this.reloadRequiredInfobar=t,t.setCloseCallback((()=>{delete this.reloadRequiredInfobar}))}}displaySelectOverrideFolderInfobar(e){if(!this.#t){const t=new He("info",rn(nn.selectOverrideFolder),[{text:rn(nn.selectFolder),highlight:!0,delegate:()=>e(),dismiss:!0,jslogContext:"select-folder"}],void 0,void 0,"select-override-folder");t.setParentView(this),this.attachInfobar(t),this.#t=t,t.setCloseCallback((()=>{this.#t=void 0}))}}createInfoBarDiv(){this.infoBarDiv||(this.infoBarDiv=document.createElement("div"),this.infoBarDiv.classList.add("flex-none"),this.contentElement.insertBefore(this.infoBarDiv,this.contentElement.firstChild))}attachInfobar(e){this.createInfoBarDiv(),this.infoBarDiv?.appendChild(e.element)}}function ln(){return e.Settings.Settings.instance().createSetting("disable-locale-info-bar",!1)}function cn(){Me.instance().canDock()&&"undocked"===Me.instance().dockSide()&&r.InspectorFrontendHost.InspectorFrontendHostInstance.setIsDocked(!0,(function(){})),r.InspectorFrontendHost.InspectorFrontendHostInstance.reattach((()=>window.location.reload()))}class dn{closeTabs(e,t){e.closeTabs(t,!0)}moveToDrawer(e){r.userMetrics.actionTaken(r.UserMetrics.Action.TabMovedToDrawer),qt.instance().moveView(e,"drawer-view")}moveToMainPanel(e){r.userMetrics.actionTaken(r.UserMetrics.Action.TabMovedToMainPanel),qt.instance().moveView(e,"panel")}onContextMenu(e,t){if("console"===e||"console-view"===e)return;"drawer-view"===qt.instance().locationNameForViewId(e)?t.defaultSection().appendItem(rn(nn.moveToTop),this.moveToMainPanel.bind(this,e),{jslogContext:"move-to-top"}):t.defaultSection().appendItem(rn(nn.moveToBottom),this.moveToDrawer.bind(this,e),{jslogContext:"move-to-bottom"})}}var hn=Object.freeze({__proto__:null,InspectorView:an,ActionDelegate:class{handleAction(e,t){switch(t){case"main.toggle-drawer":return an.instance().drawerVisible()?an.instance().closeDrawer():an.instance().showDrawer({focus:!0,hasTargetDrawer:!1}),!0;case"main.next-tab":return an.instance().tabbedPane.selectNextTab(),an.instance().tabbedPane.focus(),!0;case"main.previous-tab":return an.instance().tabbedPane.selectPrevTab(),an.instance().tabbedPane.focus(),!0}return!1}},InspectorViewTabDelegate:dn}),un={cssContent:'.soft-context-menu{overflow-y:auto;min-width:160px!important;padding:var(--sys-size-5) 0;border:1px solid var(--sys-color-neutral-outline);border-radius:var(--sys-shape-corner-small);background-color:var(--app-color-menu-background);box-shadow:var(--sys-elevation-level3)}:host-context(.theme-with-dark-background) .soft-context-menu{border:none}.dockside-title{padding-right:var(--sys-size-14)}.dockside-title + .toolbar{margin-right:-8px}.soft-context-menu-item{display:flex;width:100%;font-size:12px;height:var(--sys-size-11);padding:0 var(--sys-size-8);white-space:nowrap;align-items:center;&.soft-context-menu-item-mouse-over{background-color:var(--sys-color-state-hover-on-subtle)}& devtools-icon{width:var(--sys-size-8);height:var(--sys-size-8);pointer-events:none;&.checkmark{margin-right:var(--sys-size-3);opacity:0%;.soft-context-menu-item[checked] &{opacity:100%}}&[name="experiment"]{width:var(--sys-size-11);height:var(--sys-size-11);padding:0 var(--sys-size-3)}}}.soft-context-menu-disabled{color:var(--sys-color-state-disabled);pointer-events:none}.soft-context-menu-separator{padding:var(--sys-size-4) 0;& > .separator-line{height:var(--sys-size-1);border-bottom:var(--sys-size-1) solid var(--sys-color-divider);pointer-events:none}}.soft-context-menu-item-submenu-arrow{pointer-events:none;text-align:right;align-self:center;margin-left:auto;& > devtools-icon{width:var(--sys-size-8);height:var(--sys-size-8);color:var(--sys-color-on-surface-subtle)}}.soft-context-menu-custom-item{display:inline-flex;justify-content:center;align-items:center;flex:auto}.soft-context-menu-shortcut{color:var(--sys-color-on-surface-subtle);pointer-events:none;flex:1 1 auto;text-align:right;padding-left:var(--sys-size-6);.soft-context-menu-item-mouse-over &{color:inherit}}@media (forced-colors: active){.soft-context-menu-item{color:canvastext}.soft-context-menu-item.soft-context-menu-item-mouse-over,\n  .theme-with-dark-background .soft-context-menu-item.soft-context-menu-item-mouse-over,\n  :host-context(.theme-with-dark-background) .soft-context-menu-item.soft-context-menu-item-mouse-over{background-color:Highlight;color:HighlightText;forced-color-adjust:none}.soft-context-menu .soft-context-menu-item devtools-icon,\n  .soft-context-menu .soft-context-menu-item .soft-context-menu-shortcut{color:ButtonText}.soft-context-menu .soft-context-menu-item.soft-context-menu-item-mouse-over devtools-icon,\n  .soft-context-menu .soft-context-menu-item.soft-context-menu-item-mouse-over .soft-context-menu-shortcut{color:HighlightText}.soft-context-menu:focus-visible{forced-color-adjust:none;background:canvas;border-color:Highlight}.soft-context-menu-separator > .separator-line{border-bottom-color:ButtonText}}\n/*# sourceURL=softContextMenu.css */\n'};const pn={checked:"checked",unchecked:"unchecked",sSS:"{PH1}, {PH2}, {PH3}",sS:"{PH1}, {PH2}"},gn=t.i18n.registerUIStrings("ui/legacy/SoftContextMenu.ts",pn),mn=t.i18n.getLocalizedString.bind(void 0,gn);class bn{items;itemSelectedCallback;parentMenu;highlightedMenuItemElement;detailsForElementMap;document;glassPane;contextMenuElement;focusRestorer;hideOnUserMouseDownUnlessInMenu;activeSubMenuElement;subMenu;onMenuClosed;focusOnTheFirstItem=!0;keepOpen;loggableParent;constructor(e,t,n,i,s,r){this.items=e,this.itemSelectedCallback=t,this.parentMenu=i,this.highlightedMenuItemElement=null,this.detailsForElementMap=new WeakMap,this.onMenuClosed=s,this.keepOpen=n,this.loggableParent=r||null}getItems(){return this.items}show(e,t){if(!this.items.length)return;this.document=e,this.glassPane=new ms,this.glassPane.setPointerEventsBehavior(this.parentMenu?"PierceGlassPane":"BlockedByGlassPane"),this.glassPane.registerRequiredCSS(un),this.glassPane.setContentAnchorBox(t),this.glassPane.setSizeBehavior("MeasureContent"),this.glassPane.setMarginBehavior("NoMargin"),this.glassPane.setAnchorBehavior(this.parentMenu?"PreferRight":"PreferBottom"),this.contextMenuElement=this.glassPane.contentElement.createChild("div","soft-context-menu"),this.contextMenuElement.setAttribute("jslog",`${s.menu().track({resize:!0}).parent("mapped").track({keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Enter|Space|Escape"})}`),this.loggableParent&&s.setMappedParent(this.contextMenuElement,this.loggableParent),this.contextMenuElement.tabIndex=-1,Os(this.contextMenuElement),this.contextMenuElement.addEventListener("mouseup",(e=>e.consume()),!1),this.contextMenuElement.addEventListener("keydown",this.menuKeyDown.bind(this),!1);const n=!!this.items.find((e=>"checkbox"===e.type));for(let e=0;e<this.items.length;++e)this.contextMenuElement.appendChild(this.createMenuItem(this.items[e],n));if(this.glassPane.show(e),this.focusRestorer=new Di(this.contextMenuElement),!this.parentMenu){this.hideOnUserMouseDownUnlessInMenu=e=>{let t=this.subMenu;for(;t;){if(t.contextMenuElement===e.composedPath()[0])return;t=t.subMenu}this.discard(),e.consume(!0)},this.document.body.addEventListener("mousedown",this.hideOnUserMouseDownUnlessInMenu,!1);const e=an.maybeGetInspectorViewInstance()?.element;if(e){let t=!1;const n=new ResizeObserver((()=>{if(t)return n.disconnect(),void this.discard();t=!0}));n.observe(e)}if(this.contextMenuElement.children&&this.focusOnTheFirstItem){const e=this.contextMenuElement.children[0];this.highlightMenuItem(e,!1)}}}setContextMenuElementLabel(e){this.contextMenuElement&&or(this.contextMenuElement,e)}discard(){this.subMenu&&this.subMenu.discard(),this.focusRestorer&&this.focusRestorer.restore(),this.glassPane&&(this.glassPane.hide(),delete this.glassPane,this.hideOnUserMouseDownUnlessInMenu&&(this.document&&this.document.body.removeEventListener("mousedown",this.hideOnUserMouseDownUnlessInMenu,!1),delete this.hideOnUserMouseDownUnlessInMenu)),this.parentMenu&&(delete this.parentMenu.subMenu,this.parentMenu.activeSubMenuElement&&(Qs(this.parentMenu.activeSubMenuElement,!1),delete this.parentMenu.activeSubMenuElement)),this.onMenuClosed?.()}createMenuItem(e,t){if("separator"===e.type)return this.createSeparator();if("subMenu"===e.type)return this.createSubMenu(e,t);const n=document.createElement("div");if(n.classList.add("soft-context-menu-item"),n.tabIndex=-1,Fs(n),e.checked&&n.setAttribute("checked",""),void 0!==e.id&&n.setAttribute("data-action-id",e.id.toString()),t){const e=l.Icon.create("checkmark","checkmark");n.appendChild(e)}e.tooltip&&wt.install(n,e.tooltip);const i={actionId:void 0,isSeparator:void 0,customElement:void 0,subItems:void 0,subMenuTimer:void 0};if(e.jslogContext&&!e.element?.hasAttribute("jslog")&&("checkbox"===e.type?n.setAttribute("jslog",`${s.toggle().track({click:!0}).context(e.jslogContext)}`):n.setAttribute("jslog",`${s.action().track({click:!0}).context(e.jslogContext)}`)),e.element&&!e.label){if(n.createChild("div","soft-context-menu-custom-item").appendChild(e.element),e.element?.classList.contains("location-menu")){const t=e.element.ariaLabel||"";e.element.ariaLabel="",or(n,t)}return i.customElement=e.element,this.detailsForElementMap.set(n,i),n}e.enabled||n.classList.add("soft-context-menu-disabled"),ji(n,e.label||""),e.element&&n.appendChild(e.element),n.createChild("span","soft-context-menu-shortcut").textContent=e.shortcut||"",n.addEventListener("mousedown",this.menuItemMouseDown.bind(this),!1),n.addEventListener("mouseup",this.menuItemMouseUp.bind(this),!1),n.addEventListener("mouseover",this.menuItemMouseOver.bind(this),!1),n.addEventListener("mouseleave",this.menuItemMouseLeave.bind(this),!1),i.actionId=e.id;let r=e.label||"";if("checkbox"===e.type){const t=e.checked?mn(pn.checked):mn(pn.unchecked);r=e.shortcut?mn(pn.sSS,{PH1:String(e.label),PH2:e.shortcut,PH3:t}):mn(pn.sS,{PH1:String(e.label),PH2:t})}else e.shortcut&&(r=mn(pn.sS,{PH1:String(e.label),PH2:e.shortcut}));return or(n,r),this.detailsForElementMap.set(n,i),n}createSubMenu(e,t){const n=document.createElement("div");if(n.classList.add("soft-context-menu-item"),n.tabIndex=-1,Ws(n),this.detailsForElementMap.set(n,{subItems:e.subItems,actionId:void 0,isSeparator:void 0,customElement:void 0,subMenuTimer:void 0}),t){const e=l.Icon.create("checkmark","checkmark soft-context-menu-item-checkmark");n.appendChild(e)}ji(n,e.label||""),Qs(n,!1);const i=l.Icon.create("keyboard-arrow-right","soft-context-menu-item-submenu-arrow");return n.appendChild(i),n.addEventListener("mousedown",this.menuItemMouseDown.bind(this),!1),n.addEventListener("mouseup",this.menuItemMouseUp.bind(this),!1),n.addEventListener("mouseover",this.menuItemMouseOver.bind(this),!1),n.addEventListener("mouseleave",this.menuItemMouseLeave.bind(this),!1),e.jslogContext&&n.setAttribute("jslog",`${s.item().context(e.jslogContext)}`),n}createSeparator(){const e=document.createElement("div");return e.classList.add("soft-context-menu-separator"),this.detailsForElementMap.set(e,{subItems:void 0,actionId:void 0,isSeparator:!0,customElement:void 0,subMenuTimer:void 0}),e.createChild("div","separator-line"),e}menuItemMouseDown(e){e.consume(!0)}menuItemMouseUp(e){this.triggerAction(e.target,e),s.logClick(e.target,e),e.consume()}root(){let e=this;for(;e.parentMenu;)e=e.parentMenu;return e}setChecked(e,t){e.checked=t;const n=this.contextMenuElement?.querySelector(`[data-action-id="${e.id}"]`);if(!n)return;t?n.setAttribute("checked",""):n.removeAttribute("checked");const i=e.checked?mn(pn.checked):mn(pn.unchecked);or(n,e.shortcut?mn(pn.sSS,{PH1:String(e.label),PH2:e.shortcut,PH3:i}):mn(pn.sS,{PH1:String(e.label),PH2:i}))}triggerAction(e,t){const n=this.detailsForElementMap.get(e);if(!n||n.subItems)return this.showSubMenu(e),void t.consume();if(this.keepOpen){t.consume(!0);const e=this.items.find((e=>e.id===n.actionId));void 0!==e?.id&&(this.setChecked(e,!e.checked),this.itemSelectedCallback(e.id))}else this.root().discard(),t.consume(!0),void 0!==n.actionId&&(this.itemSelectedCallback(n.actionId),delete n.actionId)}showSubMenu(e){const t=this.detailsForElementMap.get(e);if(!t)return;if(t.subMenuTimer&&(window.clearTimeout(t.subMenuTimer),delete t.subMenuTimer),this.subMenu||!this.document)return;if(this.activeSubMenuElement=e,Qs(e,!0),!t.subItems)return;this.subMenu=new bn(t.subItems,this.itemSelectedCallback,!1,this);const n=e.boxInWindow();n.y-=9,n.x+=3,n.width-=6,n.height+=18,this.subMenu.show(this.document,n)}menuItemMouseOver(e){this.highlightMenuItem(e.target,!0)}menuItemMouseLeave(e){if(!this.subMenu||!e.relatedTarget)return void this.highlightMenuItem(null,!0);e.relatedTarget===this.contextMenuElement&&this.highlightMenuItem(null,!0)}highlightMenuItem(e,t){if(this.highlightedMenuItemElement!==e){if(this.subMenu&&this.subMenu.discard(),this.highlightedMenuItemElement){const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);this.highlightedMenuItemElement.classList.remove("force-white-icons"),this.highlightedMenuItemElement.classList.remove("soft-context-menu-item-mouse-over"),e&&e.subItems&&e.subMenuTimer&&(window.clearTimeout(e.subMenuTimer),delete e.subMenuTimer)}if(this.highlightedMenuItemElement=e,this.highlightedMenuItemElement){this.highlightedMenuItemElement.classList.add("force-white-icons"),this.highlightedMenuItemElement.classList.add("soft-context-menu-item-mouse-over");const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);e&&e.customElement&&!e.customElement.classList.contains("location-menu")?e.customElement.focus():this.highlightedMenuItemElement.focus(),t&&e&&e.subItems&&!e.subMenuTimer&&(e.subMenuTimer=window.setTimeout(this.showSubMenu.bind(this,this.highlightedMenuItemElement),150))}this.contextMenuElement&&lr(this.contextMenuElement,e)}}highlightPrevious(){let e=this.highlightedMenuItemElement?this.highlightedMenuItemElement.previousSibling:this.contextMenuElement?this.contextMenuElement.lastChild:null,t=e?this.detailsForElementMap.get(e):void 0;for(;e&&t&&(t.isSeparator||e.classList.contains("soft-context-menu-disabled"));)e=e.previousSibling,t=e?this.detailsForElementMap.get(e):void 0;e&&this.highlightMenuItem(e,!1)}highlightNext(){let e=this.highlightedMenuItemElement?this.highlightedMenuItemElement.nextSibling:this.contextMenuElement?this.contextMenuElement.firstChild:null,t=e?this.detailsForElementMap.get(e):void 0;for(;e&&(t&&t.isSeparator||e.classList.contains("soft-context-menu-disabled"));)e=e.nextSibling,t=e?this.detailsForElementMap.get(e):void 0;e&&this.highlightMenuItem(e,!1)}menuKeyDown(e){const t=e;function n(){if(!this.highlightedMenuItemElement)return;const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);e&&!e.customElement&&(s.logClick(this.highlightedMenuItemElement,t),this.triggerAction(this.highlightedMenuItemElement,t),e.subItems&&this.subMenu&&this.subMenu.highlightNext(),t.consume(!0))}switch(t.key){case"ArrowUp":this.highlightPrevious(),t.consume(!0);break;case"ArrowDown":this.highlightNext(),t.consume(!0);break;case"ArrowLeft":this.parentMenu&&(this.highlightMenuItem(null,!1),this.discard()),t.consume(!0);break;case"ArrowRight":{if(!this.highlightedMenuItemElement)break;const e=this.detailsForElementMap.get(this.highlightedMenuItemElement);e&&e.subItems&&(this.showSubMenu(this.highlightedMenuItemElement),this.subMenu&&this.subMenu.highlightNext()),e?.customElement?.classList.contains("location-menu")&&(e.customElement.dispatchEvent(new KeyboardEvent("keydown",{key:"ArrowRight"})),this.highlightMenuItem(null,!0)),t.consume(!0);break}case"Escape":this.discard(),t.consume(!0);break;case"Enter":if("Enter"!==t.key)return;n.call(this);break;case" ":n.call(this);break;default:t.consume(!0)}}markAsMenuItemCheckBox(){if(this.contextMenuElement)for(const e of this.contextMenuElement.children)"soft-context-menu-separator"!==e.className&&Hs(e)}setFocusOnTheFirstItem(e){this.focusOnTheFirstItem=e}}var fn=Object.freeze({__proto__:null,SoftContextMenu:bn});class vn{typeInternal;label;disabled;checked;contextMenu;idInternal;customElement;shortcut;#n;jslogContext;constructor(e,t,n,i,s,r,o){this.typeInternal=t,this.label=n,this.disabled=i,this.checked=s,this.contextMenu=e,this.idInternal=void 0,this.#n=r,"item"!==t&&"checkbox"!==t||(this.idInternal=e?e.nextId():0),this.jslogContext=o}id(){if(void 0===this.idInternal)throw new Error("Tried to access a ContextMenu Item ID but none was set.");return this.idInternal}type(){return this.typeInternal}isEnabled(){return!this.disabled}setEnabled(e){this.disabled=!e}buildDescriptor(){switch(this.typeInternal){case"item":{const e={type:"item",id:this.idInternal,label:this.label,enabled:!this.disabled,checked:void 0,subItems:void 0,tooltip:this.#n,jslogContext:this.jslogContext};return this.customElement&&(e.element=this.customElement),this.shortcut&&(e.shortcut=this.shortcut),e}case"separator":return{type:"separator",id:void 0,label:void 0,enabled:void 0,checked:void 0,subItems:void 0};case"checkbox":{const e={type:"checkbox",id:this.idInternal,label:this.label,checked:Boolean(this.checked),enabled:!this.disabled,subItems:void 0,tooltip:this.#n,jslogContext:this.jslogContext};return this.customElement&&(e.element=this.customElement),e}}throw new Error("Invalid item type:"+this.typeInternal)}setShortcut(e){this.shortcut=e}}class yn{contextMenu;items;constructor(e){this.contextMenu=e,this.items=[]}appendItem(e,t,n){const i=new vn(this.contextMenu,"item",e,n?.disabled,void 0,n?.tooltip,n?.jslogContext);return n?.additionalElement&&(i.customElement=n?.additionalElement),this.items.push(i),this.contextMenu&&this.contextMenu.setHandler(i.id(),t),i}appendCustomItem(e,t){const n=new vn(this.contextMenu,"item",void 0,void 0,void 0,void 0,t);return n.customElement=e,this.items.push(n),n}appendSeparator(){const e=new vn(this.contextMenu,"separator");return this.items.push(e),e}appendAction(e,t,n){if(n&&!T.instance().hasAction(e))return;const i=T.instance().getAction(e);t||(t=i.title());const s=this.appendItem(t,i.execute.bind(i),{disabled:!i.enabled(),jslogContext:e}),r=Y.instance().shortcutTitleForAction(e);r&&s.setShortcut(r)}appendSubMenuItem(e,t,n){const i=new wn(this.contextMenu,e,t,n);return i.init(),this.items.push(i),i}appendCheckboxItem(e,t,n){const i=new vn(this.contextMenu,"checkbox",e,n?.disabled,n?.checked,n?.tooltip,n?.jslogContext);return this.items.push(i),this.contextMenu&&this.contextMenu.setHandler(i.id(),t),n?.additionalElement&&(i.customElement=n.additionalElement),i}}class wn extends vn{sections;sectionList;constructor(e,t,n,i){super(e,"subMenu",t,n,void 0,void 0,i),this.sections=new Map,this.sectionList=[]}init(){xn.groupWeights.forEach((e=>this.section(e)))}section(e){e||(e="default");let t=e?this.sections.get(e):null;return t||(t=new yn(this.contextMenu),e?(this.sections.set(e,t),this.sectionList.push(t)):this.sectionList.splice(xn.groupWeights.indexOf("default"),0,t)),t}headerSection(){return this.section("header")}newSection(){return this.section("new")}revealSection(){return this.section("reveal")}clipboardSection(){return this.section("clipboard")}editSection(){return this.section("edit")}debugSection(){return this.section("debug")}viewSection(){return this.section("view")}defaultSection(){return this.section("default")}overrideSection(){return this.section("override")}saveSection(){return this.section("save")}footerSection(){return this.section("footer")}buildDescriptor(){const e={type:"subMenu",label:this.label,enabled:!this.disabled,subItems:[],id:void 0,checked:void 0,jslogContext:this.jslogContext},t=this.sectionList.filter((e=>Boolean(e.items.length)));for(const n of t){for(const t of n.items)e.subItems||(e.subItems=[]),e.subItems.push(t.buildDescriptor());n!==t[t.length-1]&&(e.subItems||(e.subItems=[]),e.subItems.push({type:"separator",id:void 0,subItems:void 0,checked:void 0,enabled:void 0,label:void 0}))}return e}appendItemsAtLocation(e){const t=In;t.sort(((e,t)=>(e.order||0)-(t.order||0)));for(const n of t){if(n.experiment&&!i.Runtime.experiments.isEnabled(n.experiment))continue;const t=n.location,s=n.actionId;if(!t||!t.startsWith(e+"/"))continue;const r=t.substr(e.length+1);r&&!r.includes("/")&&(s&&this.section(r).appendAction(s))}}static uniqueSectionName=0}class xn extends wn{contextMenu;pendingTargets;event;useSoftMenu;keepOpen;x;y;onSoftMenuClosed;jsLogContext;handlers;idInternal;softMenu;contextMenuLabel;openHostedMenu;eventTarget;loggableParent=null;constructor(e,t={}){super(null);const n=e;this.contextMenu=this,super.init(),this.pendingTargets=[],this.event=n,this.eventTarget=this.event.target,this.useSoftMenu=Boolean(t.useSoftMenu),this.keepOpen=Boolean(t.keepOpen),this.x=void 0===t.x?n.x:t.x,this.y=void 0===t.y?n.y:t.y,this.onSoftMenuClosed=t.onSoftMenuClosed,this.handlers=new Map,this.idInternal=0,this.openHostedMenu=null;let i=rs(e)||e.target;if(i){for(this.appendApplicableItems(i);i instanceof Element&&!i.hasAttribute("jslog");)i=i.parentElementOrShadowHost()??null;i instanceof Element&&(this.loggableParent=i)}}static initialize(){r.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(r.InspectorFrontendHostAPI.Events.SetUseSoftMenu,(function(e){xn.useSoftMenu=e.data}))}static installHandler(e){e.body.addEventListener("contextmenu",(function(e){new xn(e).show()}),!1)}nextId(){return this.idInternal++}isHostedMenuOpen(){return Boolean(this.openHostedMenu)}getItems(){return this.softMenu?.getItems()||[]}setChecked(e,t){this.softMenu?.setChecked(e,t)}async show(){xn.pendingMenu=this,this.event.consume(!0);const e=await Promise.all(this.pendingTargets.map((async e=>{const t=await async function(e){const t=[];for(const n of En)if(i.Runtime.Runtime.isDescriptorEnabled({experiment:n.experiment,condition:void 0})&&n.contextTypes)for(const i of n.contextTypes())e instanceof i&&t.push(await n.loadProvider());return t}(e);return{target:e,providers:t}})));if(xn.pendingMenu===this){xn.pendingMenu=null;for(const{target:t,providers:n}of e)for(const e of n)e.appendApplicableItems(this.event,this,t);this.pendingTargets=[],this.innerShow()}}discard(){this.softMenu&&this.softMenu.discard()}registerLoggablesWithin(e,t){for(const n of e)n.jslogContext&&("checkbox"===n.type?s.registerLoggable(n,`${s.toggle().track({click:!0}).context(n.jslogContext)}`,t||e):"item"===n.type?s.registerLoggable(n,`${s.action().track({click:!0}).context(n.jslogContext)}`,t||e):"subMenu"===n.type&&s.registerLoggable(n,`${s.item().context(n.jslogContext)}`,t||e),n.subItems&&this.registerLoggablesWithin(n.subItems,n))}innerShow(){const e=this.buildMenuDescriptors();if(!this.eventTarget)return;const t=this.eventTarget.ownerDocument;if(this.useSoftMenu||xn.useSoftMenu||r.InspectorFrontendHost.InspectorFrontendHostInstance.isHostedMode()){this.softMenu=new bn(e,this.itemSelected.bind(this),this.keepOpen,void 0,this.onSoftMenuClosed,this.loggableParent);const n="mouse"===this.event.pointerType&&this.event.button>=0;this.softMenu.setFocusOnTheFirstItem(!n),this.softMenu.show(t,new AnchorBox(this.x,this.y,0,0)),this.contextMenuLabel&&this.softMenu.setContextMenuElementLabel(this.contextMenuLabel)}else{function i(){r.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(r.InspectorFrontendHostAPI.Events.ContextMenuCleared,this.menuCleared,this),r.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(r.InspectorFrontendHostAPI.Events.ContextMenuItemSelected,this.onItemSelected,this)}r.InspectorFrontendHost.InspectorFrontendHostInstance.showContextMenuAtPoint(this.x,this.y,e,t),s.registerLoggable(e,`${s.menu()}`,this.loggableParent),this.registerLoggablesWithin(e),this.openHostedMenu=e,queueMicrotask(i.bind(this))}}setContextMenuLabel(e){this.contextMenuLabel=e}setX(e){this.x=e}setY(e){this.y=e}setHandler(e,t){t&&this.handlers.set(e,t)}invokeHandler(e){const t=this.handlers.get(e);t&&t.call(this)}buildMenuDescriptors(){return super.buildDescriptor().subItems}onItemSelected(e){this.itemSelected(e.data)}itemSelected(e){if(this.invokeHandler(e),this.openHostedMenu){const t=(e,n)=>{for(const i of e){if(i.id===n)return i;const e=i.subItems&&t(i.subItems,n);if(e)return e}return null},n=t(this.openHostedMenu,e);n&&n.jslogContext&&s.logClick(n,new MouseEvent("click"))}this.menuCleared()}menuCleared(){r.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(r.InspectorFrontendHostAPI.Events.ContextMenuCleared,this.menuCleared,this),r.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(r.InspectorFrontendHostAPI.Events.ContextMenuItemSelected,this.onItemSelected,this),this.openHostedMenu&&s.logResize(this.openHostedMenu,new DOMRect(0,0,0,0)),this.openHostedMenu=null,this.keepOpen||this.onSoftMenuClosed?.()}appendApplicableItems(e){this.pendingTargets.includes(e)||this.pendingTargets.push(e)}markAsMenuItemCheckBox(){this.softMenu&&this.softMenu.markAsMenuItemCheckBox()}static pendingMenu=null;static useSoftMenu=!1;static groupWeights=["header","new","reveal","edit","clipboard","debug","view","default","override","save","footer"]}const En=[];const In=[];var Sn=Object.freeze({__proto__:null,Item:vn,Section:yn,SubMenu:wn,ContextMenu:xn,registerProvider:function(e){En.push(e)},registerItem:function(e){In.push(e)},maybeRemoveItem:function(e){const t=In.findIndex((t=>t.actionId===e.actionId&&t.location===e.location));return!(t<0)&&(In.splice(t,1),!0)}});const kn={srequiresReload:"*Requires reload",oneOrMoreSettingsHaveChanged:"One or more settings have changed which requires a reload to take effect."},Cn=t.i18n.registerUIStrings("ui/legacy/SettingsUI.ts",kn),Tn=t.i18n.getLocalizedString.bind(void 0,Cn),Ln=function(e,t,n){const i=e;function s(){i.checked!==t.get()&&(i.checked=t.get())}t.addChangeListener(s),s(),i.addEventListener("change",(function(){t.get()!==i.checked&&t.set(i.checked),t.get()&&n?.enable&&r.userMetrics.actionTaken(n.enable),!t.get()&&n?.disable&&r.userMetrics.actionTaken(n.disable),n?.toggle&&r.userMetrics.actionTaken(n.toggle)}),!1)};var Mn,Pn=Object.freeze({__proto__:null,createSettingCheckbox:function(e,t,n,i){const s=Ki.create(e,void 0,void 0,t.name);i&&wt.install(s,i);const r=s.checkboxElement;if(r.name=e,Ln(r,t),n)return s;const o=document.createElement("p");return o.appendChild(s),o},bindCheckbox:Ln,createCustomSetting:function(e,t){const n=document.createElement("p");n.classList.add("settings-select");const i=n.createChild("label");return i.textContent=e,Ss(i,t),n.appendChild(t),n},createControlForSetting:function(e,t){const i=e.title();switch(e.type()){case"boolean":{const t=new u.SettingCheckbox.SettingCheckbox;return t.data={setting:e},t.onchange=()=>{e.reloadRequired()&&an.instance().displayReloadRequiredWarning(Tn(kn.oneOrMoreSettingsHaveChanged))},t}case"enum":return Array.isArray(e.options())?function(e,t,i,r,o){const a=document.createElement("div"),l=a.createChild("p");l.classList.add("settings-select");const c=l.createChild("label"),d=l.createChild("select","chrome-select");c.textContent=e,o&&(a.classList.add("chrome-select-label"),c.createChild("p").textContent=o),d.setAttribute("jslog",`${s.dropDown().track({change:!0}).context(r.name)}`),Ss(c,d);for(const e of t)e.text&&"string"==typeof e.value&&d.add(_i(e.text,e.value,n.StringUtilities.toKebabCase(e.value)));let h=null;i&&(h=a.createChild("span","reload-warning hidden"),h.textContent=Tn(kn.srequiresReload),ks(h));const{deprecation:p}=r;if(p){const e=new u.SettingDeprecationWarning.SettingDeprecationWarning;e.data=p,c.appendChild(e)}return r.addChangeListener(g),g(),d.addEventListener("change",(function(){r.set(t[d.selectedIndex].value),h&&(h.classList.remove("hidden"),an.instance().displayReloadRequiredWarning(Tn(kn.oneOrMoreSettingsHaveChanged)))}),!1),a;function g(){const e=r.get();for(let n=0;n<t.length;n++)t[n].value===e&&(d.selectedIndex=n);d.disabled=r.disabled()}}(i,e.options(),e.reloadRequired(),e,t):(console.error("Enum setting defined without options"),null);default:return console.error("Invalid setting type: "+e.type()),null}}});!function(e){e.NonViewport="UI.ListMode.NonViewport",e.EqualHeightItems="UI.ListMode.EqualHeightItems",e.VariousHeightItems="UI.ListMode.VariousHeightItems"}(Mn||(Mn={}));class Dn{element;topElement;bottomElement;firstIndex;lastIndex;renderedHeight;topHeight;bottomHeight;model;itemToElement;selectedIndexInternal;selectedItemInternal;delegate;mode;fixedHeight;variableOffsets;constructor(e,t,n){this.element=document.createElement("div"),this.element.style.overflowY="auto",this.topElement=this.element.createChild("div"),this.bottomElement=this.element.createChild("div"),this.firstIndex=0,this.lastIndex=0,this.renderedHeight=0,this.topHeight=0,this.bottomHeight=0,this.model=e,this.model.addEventListener("ItemsReplaced",this.replacedItemsInRange,this),this.itemToElement=new Map,this.selectedIndexInternal=-1,this.selectedItemInternal=null,this.element.tabIndex=-1,this.element.addEventListener("click",this.onClick.bind(this),!1),this.element.addEventListener("keydown",this.onKeyDown.bind(this),!1),Vs(this.element),this.delegate=t,this.mode=n||Mn.EqualHeightItems,this.fixedHeight=0,this.variableOffsets=new Int32Array(0),this.clearContents(),this.mode!==Mn.NonViewport&&this.element.addEventListener("scroll",(()=>{this.updateViewport(this.element.scrollTop,this.element.offsetHeight)}),!1)}setModel(e){this.itemToElement.clear();const t=this.model.length;this.model.removeEventListener("ItemsReplaced",this.replacedItemsInRange,this),this.model=e,this.model.addEventListener("ItemsReplaced",this.replacedItemsInRange,this),this.invalidateRange(0,t)}replacedItemsInRange(e){const t=e.data,n=t.index,i=n+t.removed.length,s=t.keepSelectedIndex,r=this.selectedItemInternal,o=null!==r&&this.itemToElement.get(r)||null;for(let e=0;e<t.removed.length;e++)this.itemToElement.delete(t.removed[e]);if(this.invalidate(n,i,t.inserted),this.selectedIndexInternal>=i)this.selectedIndexInternal+=t.inserted-(i-n),this.selectedItemInternal=this.model.at(this.selectedIndexInternal);else if(this.selectedIndexInternal>=n){const e=s?n:n+t.inserted;let i=this.findFirstSelectable(e,1,!1);if(-1===i){const e=s?n:n-1;i=this.findFirstSelectable(e,-1,!1)}this.select(i,r,o)}}refreshItem(e){const t=this.model.indexOf(e);-1!==t?this.refreshItemByIndex(t):console.error("Item to refresh is not present")}refreshItemByIndex(e){const t=this.model.at(e);this.itemToElement.delete(t),this.invalidateRange(e,e+1),-1!==this.selectedIndexInternal&&this.select(this.selectedIndexInternal,null,null)}refreshAllItems(){this.itemToElement.clear(),this.invalidateRange(0,this.model.length),-1!==this.selectedIndexInternal&&this.select(this.selectedIndexInternal,null,null)}invalidateRange(e,t){this.invalidate(e,t,t-e)}viewportResized(){if(this.mode===Mn.NonViewport)return;const e=this.element.scrollTop,t=this.element.offsetHeight;this.clearViewport(),this.updateViewport(n.NumberUtilities.clamp(e,0,this.totalHeight()-t),t)}invalidateItemHeight(){this.mode===Mn.EqualHeightItems?(this.fixedHeight=0,this.model.length&&(this.itemToElement.clear(),this.invalidate(0,this.model.length,this.model.length))):console.error("Only supported in equal height items mode")}itemForNode(e){for(;e&&e.parentNodeOrShadowHost()!==this.element;)e=e.parentNodeOrShadowHost();if(!e)return null;const t=e,n=this.model.findIndex((e=>this.itemToElement.get(e)===t));return-1!==n?this.model.at(n):null}scrollItemIntoView(e,t){const n=this.model.indexOf(e);-1!==n?this.scrollIntoView(n,t):console.error("Attempt to scroll onto missing item")}selectedItem(){return this.selectedItemInternal}selectedIndex(){return this.selectedIndexInternal}selectItem(e,t,n){let i=-1;if(null!==e){if(i=this.model.indexOf(e),-1===i)return void console.error("Attempt to select missing item");if(!this.delegate.isItemSelectable(e))return void console.error("Attempt to select non-selectable item")}-1===i||n||this.scrollIntoView(i,t),this.selectedIndexInternal!==i&&this.select(i)}selectPreviousItem(e,t){if(-1===this.selectedIndexInternal&&!e)return!1;let n=-1===this.selectedIndexInternal?this.model.length-1:this.selectedIndexInternal-1;return n=this.findFirstSelectable(n,-1,Boolean(e)),-1!==n&&(this.scrollIntoView(n,t),this.select(n),!0)}selectNextItem(e,t){if(-1===this.selectedIndexInternal&&!e)return!1;let n=-1===this.selectedIndexInternal?0:this.selectedIndexInternal+1;return n=this.findFirstSelectable(n,1,Boolean(e)),-1!==n&&(this.scrollIntoView(n,t),this.select(n),!0)}selectItemPreviousPage(e){if(this.mode===Mn.NonViewport)return!1;let t=-1===this.selectedIndexInternal?this.model.length-1:this.selectedIndexInternal;return t=this.findPageSelectable(t,-1),-1!==t&&(this.scrollIntoView(t,e),this.select(t),!0)}selectItemNextPage(e){if(this.mode===Mn.NonViewport)return!1;let t=-1===this.selectedIndexInternal?0:this.selectedIndexInternal;return t=this.findPageSelectable(t,1),-1!==t&&(this.scrollIntoView(t,e),this.select(t),!0)}scrollIntoView(e,t){if(this.mode===Mn.NonViewport)return void this.elementAtIndex(e).scrollIntoViewIfNeeded(Boolean(t));const i=this.offsetAtIndex(e),s=this.offsetAtIndex(e+1),r=this.element.offsetHeight;if(t){const e=(i+s)/2-r/2;return void this.updateViewport(n.NumberUtilities.clamp(e,0,this.totalHeight()-r),r)}const o=this.element.scrollTop;i<o?this.updateViewport(i,r):s>o+r&&this.updateViewport(s-r,r)}onClick(e){const t=this.itemForNode(e.target);null!==t&&this.delegate.isItemSelectable(t)&&this.selectItem(t)}onKeyDown(e){const t=e;let n=!1;switch(t.key){case"ArrowUp":n=this.selectPreviousItem(!0,!1);break;case"ArrowDown":n=this.selectNextItem(!0,!1);break;case"PageUp":n=this.selectItemPreviousPage(!1);break;case"PageDown":n=this.selectItemNextPage(!1)}n&&t.consume(!0)}totalHeight(){return this.offsetAtIndex(this.model.length)}indexAtOffset(e){if(this.mode===Mn.NonViewport)throw"There should be no offset conversions in non-viewport mode";return!this.model.length||e<0?0:this.mode===Mn.VariousHeightItems?Math.min(this.model.length-1,n.ArrayUtilities.lowerBound(this.variableOffsets,e,n.ArrayUtilities.DEFAULT_COMPARATOR,0,this.model.length)):(this.fixedHeight||this.measureHeight(),Math.min(this.model.length-1,Math.floor(e/this.fixedHeight)))}elementAtIndex(e){const t=this.model.at(e);let n=this.itemToElement.get(t);return n||(n=this.delegate.createElementForItem(t),n.hasAttribute("jslog")||n.setAttribute("jslog",`${s.item().track({click:!0,keydown:"ArrowUp|ArrowDown|PageUp|PageDown"})}`),this.itemToElement.set(t,n),this.updateElementARIA(n,e)),n}refreshARIA(){for(let e=this.firstIndex;e<=this.lastIndex;e++){const t=this.model.at(e),n=this.itemToElement.get(t);n&&this.updateElementARIA(n,e)}}updateElementARIA(e,t){Gs(e)||_s(e),cr(e,this.model.length),dr(e,t+1)}offsetAtIndex(e){if(this.mode===Mn.NonViewport)throw new Error("There should be no offset conversions in non-viewport mode");return this.model.length?this.mode===Mn.VariousHeightItems?this.variableOffsets[e]:(this.fixedHeight||this.measureHeight(),e*this.fixedHeight):0}measureHeight(){this.fixedHeight=this.delegate.heightForItem(this.model.at(0)),this.fixedHeight||(this.fixedHeight=Bi(this.elementAtIndex(0),this.element).height)}select(e,t,n){void 0===t&&(t=this.selectedItemInternal),void 0===n&&(n=this.itemToElement.get(t)||null),this.selectedIndexInternal=e,this.selectedItemInternal=-1===e?null:this.model.at(e);const i=this.selectedItemInternal,s=-1!==this.selectedIndexInternal?this.elementAtIndex(e):null;this.delegate.selectedItemChanged(t,i,n,s),this.delegate.updateSelectedItemARIA(n,s)||(n&&nr(n,!1),s&&nr(s,!0),lr(this.element,s))}findFirstSelectable(e,t,n){const i=this.model.length;if(!i)return-1;for(let s=0;s<=i;s++){if(e<0||e>=i){if(!n)return-1;e=(e+i)%i}if(this.delegate.isItemSelectable(this.model.at(e)))return e;e+=t}return-1}findPageSelectable(e,t){let n=-1;const i=this.offsetAtIndex(e),s=this.element.offsetHeight-1;for(;e>=0&&e<this.model.length;){if(this.delegate.isItemSelectable(this.model.at(e))){if(Math.abs(this.offsetAtIndex(e)-i)>=s)return e;n=e}e+=t}return n}reallocateVariableOffsets(e,t){if(this.variableOffsets.length<e){const n=new Int32Array(Math.max(e,2*this.variableOffsets.length));n.set(this.variableOffsets.slice(0,t),0),this.variableOffsets=n}else if(this.variableOffsets.length>=2*e){const n=new Int32Array(e);n.set(this.variableOffsets.slice(0,t),0),this.variableOffsets=n}}invalidate(e,t,i){if(this.mode===Mn.NonViewport)return void this.invalidateNonViewportMode(e,t-e,i);if(this.mode===Mn.VariousHeightItems){this.reallocateVariableOffsets(this.model.length+1,e+1);for(let t=e+1;t<=this.model.length;t++)this.variableOffsets[t]=this.variableOffsets[t-1]+this.delegate.heightForItem(this.model.at(t-1))}const s=this.element.offsetHeight,r=this.totalHeight(),o=this.element.scrollTop;if(this.renderedHeight<s||r<s)return this.clearViewport(),void this.updateViewport(n.NumberUtilities.clamp(o,0,r-s),s);const a=r-this.renderedHeight;if(t<=this.firstIndex){const n=this.topHeight+a;this.topElement.style.height=n+"px",this.element.scrollTop=o+a,this.topHeight=n,this.renderedHeight=r;const s=i-(t-e);return this.firstIndex+=s,void(this.lastIndex+=s)}if(e>=this.lastIndex){const e=this.bottomHeight+a;return this.bottomElement.style.height=e+"px",this.bottomHeight=e,void(this.renderedHeight=r)}this.clearViewport(),this.updateViewport(n.NumberUtilities.clamp(o,0,r-s),s),this.refreshARIA()}invalidateNonViewportMode(e,t,n){let i=this.topElement;for(let t=0;t<e;t++)i=i.nextElementSibling;for(;t--;)i.nextElementSibling.remove();for(;n--;)this.element.insertBefore(this.elementAtIndex(e+n),i.nextElementSibling)}clearViewport(){this.mode!==Mn.NonViewport?(this.firstIndex=0,this.lastIndex=0,this.renderedHeight=0,this.topHeight=0,this.bottomHeight=0,this.clearContents()):console.error("There should be no viewport updates in non-viewport mode")}clearContents(){this.topElement.style.height="0",this.bottomElement.style.height="0",this.element.removeChildren(),this.element.appendChild(this.topElement),this.element.appendChild(this.bottomElement)}updateViewport(e,t){if(this.mode===Mn.NonViewport)return void console.error("There should be no viewport updates in non-viewport mode");const n=this.totalHeight();if(!n)return this.firstIndex=0,this.lastIndex=0,this.topHeight=0,this.bottomHeight=0,this.renderedHeight=0,this.topElement.style.height="0",void(this.bottomElement.style.height="0");const i=this.indexAtOffset(e-t),s=this.indexAtOffset(e+2*t)+1;for(;this.firstIndex<Math.min(i,this.lastIndex);)this.elementAtIndex(this.firstIndex).remove(),this.firstIndex++;for(;this.lastIndex>Math.max(s,this.firstIndex);)this.elementAtIndex(this.lastIndex-1).remove(),this.lastIndex--;this.firstIndex=Math.min(this.firstIndex,s),this.lastIndex=Math.max(this.lastIndex,i);for(let e=this.firstIndex-1;e>=i;e--){const t=this.elementAtIndex(e);this.element.insertBefore(t,this.topElement.nextSibling)}for(let e=this.lastIndex;e<s;e++){const t=this.elementAtIndex(e);this.element.insertBefore(t,this.bottomElement)}this.firstIndex=i,this.lastIndex=s,this.topHeight=this.offsetAtIndex(i),this.topElement.style.height=this.topHeight+"px",this.bottomHeight=n-this.offsetAtIndex(s),this.bottomElement.style.height=this.bottomHeight+"px",this.renderedHeight=n,this.element.scrollTop=e}}var An=Object.freeze({__proto__:null,get ListMode(){return Mn},ListControl:Dn});class Rn extends e.ObjectWrapper.ObjectWrapper{items;constructor(e){super(),this.items=e||[]}[Symbol.iterator](){return this.items[Symbol.iterator]()}get length(){return this.items.length}at(e){return this.items[e]}every(e){return this.items.every(e)}filter(e){return this.items.filter(e)}find(e){return this.items.find(e)}findIndex(e){return this.items.findIndex(e)}indexOf(e,t){return this.items.indexOf(e,t)}insert(e,t){this.items.splice(e,0,t),this.replaced(e,[],1)}insertWithComparator(e,t){this.insert(n.ArrayUtilities.lowerBound(this.items,e,t),e)}join(e){return this.items.join(e)}remove(e){const t=this.items[e];return this.items.splice(e,1),this.replaced(e,[t],0),t}replace(e,t,n){const i=this.items[e];return this.items[e]=t,this.replaced(e,[i],1,n),i}replaceRange(e,t,n){let i;if(n.length<1e4)i=this.items.splice(e,t-e,...n);else{i=this.items.slice(e,t);const s=this.items.slice(0,e),r=this.items.slice(t);this.items=[...s,...n,...r]}return this.replaced(e,i,n.length),i}replaceAll(e){const t=this.items.slice();return this.items=e,this.replaced(0,t,e.length),t}slice(e,t){return this.items.slice(e,t)}some(e){return this.items.some(e)}replaced(e,t,n,i){this.dispatchEventToListeners("ItemsReplaced",{index:e,removed:t,inserted:n,keepSelectedIndex:i})}}var Bn=Object.freeze({__proto__:null,ListModel:Rn}),zn={cssContent:":host{display:flex;flex:auto}.suggest-box{flex:auto;background-color:var(--sys-color-cdt-base-container);pointer-events:auto;margin-left:-3px;box-shadow:var(--drop-shadow);overflow-x:hidden}.suggest-box-content-item{padding:1px 0 1px 1px;margin:0;border:1px solid transparent;white-space:nowrap;display:flex;align-items:center;justify-content:space-between}.suggest-box-content-item.secondary{background-color:var(--sys-color-neutral-container);justify-content:normal}.suggestion-title{overflow:hidden;text-overflow:ellipsis}.suggestion-title span{white-space:pre}.suggestion-subtitle{flex:auto;text-align:right;color:var(--sys-color-token-subtle);margin-right:3px;overflow:hidden;text-overflow:ellipsis}.suggest-box-content-item devtools-icon{color:var(--sys-color-on-surface-subtle);margin-right:1px}.suggest-box-content-item .query{font-weight:bold}.suggest-box-content-item .spacer{display:inline-block;width:20px}.suggest-box-content-item.selected{background-color:var(--sys-color-tonal-container)}.suggest-box-content-item.selected .suggestion-subtitle,\n.suggest-box-content-item.selected > span{color:var(--sys-color-on-tonal-container)}.suggest-box-content-item:hover:not(.selected){background-color:var(--sys-color-state-hover-on-subtle)}@media (forced-colors: active){.suggest-box-content-item.selected{forced-color-adjust:none;background-color:Highlight}.suggest-box-content-item.selected > span{color:HighlightText}}\n/*# sourceURL=suggestBox.css */\n"};const On={sSuggestionSOfS:"{PH1}, suggestion {PH2} of {PH3}",sSuggestionSSelected:"{PH1}, suggestion selected"},Fn=t.i18n.registerUIStrings("ui/legacy/SuggestBox.ts",On),Hn=t.i18n.getLocalizedString.bind(void 0,Fn);class Wn{suggestBoxDelegate;maxItemsHeight;rowHeight;userEnteredText;defaultSelectionIsDimmed;onlyCompletion;items;list;element;glassPane;constructor(e,t){this.suggestBoxDelegate=e,this.maxItemsHeight=t,this.rowHeight=17,this.userEnteredText="",this.defaultSelectionIsDimmed=!1,this.onlyCompletion=null,this.items=new Rn,this.list=new Dn(this.items,this,Mn.EqualHeightItems),this.element=this.list.element,this.element.classList.add("suggest-box"),this.element.addEventListener("mousedown",(e=>e.preventDefault()),!0),this.element.addEventListener("click",this.onClick.bind(this),!1),this.element.setAttribute("jslog",`${s.menu().parent("mapped").track({resize:!0,keydown:"ArrowUp|ArrowDown|PageUp|PageDown"})}`),this.glassPane=new ms,this.glassPane.setAnchorBehavior("PreferBottom"),this.glassPane.setOutsideClickCallback(this.hide.bind(this));ds(this.glassPane.contentElement,{cssFile:zn,delegatesFocus:void 0}).appendChild(this.element)}visible(){return this.glassPane.isShowing()}setPosition(e){this.glassPane.setContentAnchorBox(e)}setAnchorBehavior(e){this.glassPane.setAnchorBehavior(e)}updateMaxSize(e){const t=this.maxWidth(e),n=(this.maxItemsHeight?Math.min(this.maxItemsHeight,e.length):e.length)*this.rowHeight;this.glassPane.setMaxContentSize(new _(t,n))}maxWidth(e){if(!e.length)return 300;let t,n=-1/0;for(let i=0;i<e.length;i++){const s=(e[i].title||e[i].text).length+(e[i].subtitle||"").length;s>n&&(n=s,t=e[i])}const i=Bi(this.createElementForItem(t),this.element).width+us(this.element.ownerDocument);return Math.min(300,i)}show(){if(this.visible())return;s.setMappedParent(this.element,this.suggestBoxDelegate.ownerElement()),this.glassPane.show(document);this.rowHeight=Bi(this.createElementForItem({text:"1",subtitle:"12"}),this.element).height,Ys(this.suggestBoxDelegate.ownerElement(),this.element),Qs(this.suggestBoxDelegate.ownerElement(),!0)}hide(){this.visible()&&(this.glassPane.hide(),Ys(this.suggestBoxDelegate.ownerElement(),null),Qs(this.suggestBoxDelegate.ownerElement(),!1))}applySuggestion(e){if(this.onlyCompletion)return mr(e?Hn(On.sSuggestionSOfS,{PH1:this.onlyCompletion.text,PH2:this.list.selectedIndex()+1,PH3:this.items.length}):Hn(On.sSuggestionSSelected,{PH1:this.onlyCompletion.text})),this.suggestBoxDelegate.applySuggestion(this.onlyCompletion,e),!0;const t=this.list.selectedItem();return t&&t.text&&mr(e?Hn(On.sSuggestionSOfS,{PH1:t.title||t.text,PH2:this.list.selectedIndex()+1,PH3:this.items.length}):Hn(On.sSuggestionSSelected,{PH1:t.title||t.text})),this.suggestBoxDelegate.applySuggestion(t,e),this.visible()&&Boolean(t)}acceptSuggestion(){const e=this.applySuggestion();return this.hide(),!!e&&(this.suggestBoxDelegate.acceptSuggestion(),!0)}createElementForItem(e){const t=this.userEnteredText,i=document.createElement("div");i.classList.add("suggest-box-content-item"),i.classList.add("source-code"),e.isSecondary&&i.classList.add("secondary"),i.tabIndex=-1;const s=50+t.length,r=n.StringUtilities.trimEndWithMaxLength((e.title||e.text).trim(),s).replace(/\n/g,"↵"),o=i.createChild("span","suggestion-title"),a=r.toLowerCase().indexOf(t.toLowerCase());if(a>0&&(o.createChild("span").textContent=r.substring(0,a)),a>-1&&(o.createChild("span","query").textContent=r.substring(a,a+t.length)),o.createChild("span").textContent=r.substring(a>-1?a+t.length:0),o.createChild("span","spacer"),e.subtitleRenderer){const t=e.subtitleRenderer.call(null);t.classList.add("suggestion-subtitle"),i.appendChild(t)}else if(e.subtitle){i.createChild("span","suggestion-subtitle").textContent=n.StringUtilities.trimEndWithMaxLength(e.subtitle,s-r.length)}return e.iconElement&&i.appendChild(e.iconElement),i}heightForItem(e){return this.rowHeight}isItemSelectable(e){return!0}selectedItemChanged(e,t,n,i){n&&n.classList.remove("selected","force-white-icons"),i&&(i.classList.add("selected"),i.classList.add("force-white-icons")),this.applySuggestion(!0)}updateSelectedItemARIA(e,t){return!1}onClick(e){const t=this.list.itemForNode(e.target);t&&(this.list.selectItem(t),this.acceptSuggestion(),e.consume(!0))}canShowBox(e,t,n,i){return!(!e||!e.length)&&(e.length>1||(!(t&&!t.isSecondary&&t.text.startsWith(i))||n&&t.text!==i))}updateSuggestions(e,t,n,i,s){this.onlyCompletion=null;const r=n?t.reduce(((e,t)=>(e.priority||0)>=(t.priority||0)?e:t)):null;this.canShowBox(t,r,i,s)?(this.userEnteredText=s,this.show(),this.updateMaxSize(t),this.glassPane.setContentAnchorBox(e),this.list.invalidateItemHeight(),this.items.replaceAll(t),r&&!r.isSecondary?this.list.selectItem(r,!0):this.list.selectItem(null)):(1===t.length&&(this.onlyCompletion=t[0],this.applySuggestion(!0)),this.hide())}keyPressed(e){switch(e.key){case"Enter":return this.enterKeyPressed();case"ArrowUp":return this.list.selectPreviousItem(!0,!1);case"ArrowDown":return this.list.selectNextItem(!0,!1);case"PageUp":return this.list.selectItemPreviousPage(!1);case"PageDown":return this.list.selectItemNextPage(!1)}return!1}enterKeyPressed(){const e=Boolean(this.list.selectedItem())||Boolean(this.onlyCompletion);return this.acceptSuggestion(),e}}var Nn=Object.freeze({__proto__:null,SuggestBox:Wn}),jn={cssContent:'.text-prompt-root{display:flex;align-items:center}.text-prompt-editing{box-shadow:var(--drop-shadow);background-color:var(--sys-color-cdt-base-container);text-overflow:clip!important;margin:0 -2px -1px;padding:0 2px 1px;opacity:100%!important}.text-prompt{cursor:text;overflow-x:visible}.text-prompt::-webkit-scrollbar{display:none}.text-prompt-editing > .text-prompt{color:var(--sys-color-on-surface)!important;text-decoration:none!important;white-space:pre}.text-prompt > .auto-complete-text{color:var(--sys-color-token-subtle)!important}.text-prompt[data-placeholder]{&:empty::before{content:attr(data-placeholder);color:var(--sys-color-on-surface-subtle)}&.disabled:empty::before{color:var(--sys-color-state-disabled)}}.text-prompt:not([data-placeholder]):empty::after{content:"\\00A0";width:0;display:block}.text-prompt.disabled{opacity:50%;cursor:default}.text-prompt-editing br{display:none}.text-prompt-root:not(:focus-within) ::selection{background:transparent}@media (forced-colors: active){.text-prompt[data-placeholder]:empty::before{color:GrayText!important}.text-prompt.disabled{opacity:100%}}\n/*# sourceURL=textPrompt.css */\n'};class Vn extends e.ObjectWrapper.ObjectWrapper{proxyElement;proxyElementDisplay;autocompletionTimeout;titleInternal;queryRange;previousText;currentSuggestion;completionRequestId;ghostTextElement;leftParenthesesIndices;loadCompletions;completionStopCharacters;usesSuggestionBuilder;elementInternal;boundOnKeyDown;boundOnInput;boundOnMouseWheel;boundClearAutocomplete;boundOnBlur;contentElement;suggestBox;isEditing;focusRestorer;blurListener;oldTabIndex;completeTimeout;disableDefaultSuggestionForEmptyInputInternal;changed;jslogContext=void 0;constructor(){super(),this.proxyElementDisplay="inline-block",this.autocompletionTimeout=Un,this.titleInternal="",this.queryRange=null,this.previousText="",this.currentSuggestion=null,this.completionRequestId=0,this.ghostTextElement=document.createElement("span"),this.ghostTextElement.classList.add("auto-complete-text"),this.ghostTextElement.setAttribute("contenteditable","false"),this.leftParenthesesIndices=[],this.changed=!1,Ks(this.ghostTextElement)}initialize(e,t,n){this.loadCompletions=e,this.completionStopCharacters=t||" =:[({;,!+-*/&|^<>.",this.usesSuggestionBuilder=n||!1}setAutocompletionTimeout(e){this.autocompletionTimeout=e}renderAsBlock(){this.proxyElementDisplay="block"}attach(e){return this.attachInternal(e)}attachAndStartEditing(e,t){const n=this.attachInternal(e);return this.startEditing(t),n}attachInternal(e){if(this.proxyElement)throw"Cannot attach an attached TextPrompt";this.elementInternal=e,this.boundOnKeyDown=this.onKeyDown.bind(this),this.boundOnInput=this.onInput.bind(this),this.boundOnMouseWheel=this.onMouseWheel.bind(this),this.boundClearAutocomplete=this.clearAutocomplete.bind(this),this.boundOnBlur=this.onBlur.bind(this),this.proxyElement=e.ownerDocument.createElement("span"),c.ThemeSupport.instance().appendStyle(this.proxyElement,jn),this.contentElement=this.proxyElement.createChild("div","text-prompt-root"),this.proxyElement.style.display=this.proxyElementDisplay,e.parentElement&&e.parentElement.insertBefore(this.proxyElement,e),this.contentElement.appendChild(e);let t=s.textField().track({keydown:"ArrowLeft|ArrowUp|PageUp|Home|PageDown|ArrowRight|ArrowDown|End|Space|Tab|Enter|Escape",change:!0});return this.jslogContext&&(t=t.context(this.jslogContext)),this.elementInternal.hasAttribute("jslog")||this.elementInternal.setAttribute("jslog",`${t}`),this.elementInternal.classList.add("text-prompt"),zs(this.elementInternal),Js(this.elementInternal,"both"),tr(this.elementInternal,"listbox"),this.elementInternal.setAttribute("contenteditable","plaintext-only"),this.element().addEventListener("keydown",this.boundOnKeyDown,!1),this.elementInternal.addEventListener("input",this.boundOnInput,!1),this.elementInternal.addEventListener("wheel",this.boundOnMouseWheel,!1),this.elementInternal.addEventListener("selectstart",this.boundClearAutocomplete,!1),this.elementInternal.addEventListener("blur",this.boundOnBlur,!1),this.suggestBox=new Wn(this,20),this.titleInternal&&wt.install(this.proxyElement,this.titleInternal),this.proxyElement}element(){if(!this.elementInternal)throw new Error("Expected an already attached element!");return this.elementInternal}detach(){this.removeFromElement(),this.focusRestorer&&this.focusRestorer.restore(),this.proxyElement&&this.proxyElement.parentElement&&(this.proxyElement.parentElement.insertBefore(this.element(),this.proxyElement),this.proxyElement.remove()),delete this.proxyElement,this.element().classList.remove("text-prompt"),this.element().removeAttribute("contenteditable"),this.element().removeAttribute("role"),er(this.element()),tr(this.element(),"false")}textWithCurrentSuggestion(){const e=this.text();if(!this.queryRange||!this.currentSuggestion)return e;const t=this.currentSuggestion.text;return e.substring(0,this.queryRange.startColumn)+t+e.substring(this.queryRange.endColumn)}text(){let e=this.element().textContent||"";if(this.ghostTextElement.parentNode){const t=this.ghostTextElement.textContent||"";e=e.substring(0,e.length-t.length)}return e}setText(e){this.clearAutocomplete(),this.element().textContent=e,this.previousText=this.text(),this.element().hasFocus()&&(this.moveCaretToEndOfPrompt(),this.element().scrollIntoView())}setSelectedRange(e,t){if(e<0)throw new RangeError("Selected range start must be a nonnegative integer");const n=this.element().textContent,i=n?n.length:0;t>i&&(t=i),t<e&&(t=e);const s=this.element().childNodes[0],r=new Range;r.setStart(s,e),r.setEnd(s,t);const o=window.getSelection();o&&(o.removeAllRanges(),o.addRange(r))}focus(){this.element().focus()}title(){return this.titleInternal}setTitle(e){this.titleInternal=e,this.proxyElement&&wt.install(this.proxyElement,e)}setPlaceholder(e,t){e?(this.element().setAttribute("data-placeholder",e),$s(this.element(),t||e)):(this.element().removeAttribute("data-placeholder"),$s(this.element(),null))}setEnabled(e){e?this.element().setAttribute("contenteditable","plaintext-only"):this.element().removeAttribute("contenteditable"),this.element().classList.toggle("disabled",!e)}removeFromElement(){this.clearAutocomplete(),this.element().removeEventListener("keydown",this.boundOnKeyDown,!1),this.element().removeEventListener("input",this.boundOnInput,!1),this.element().removeEventListener("selectstart",this.boundClearAutocomplete,!1),this.element().removeEventListener("blur",this.boundOnBlur,!1),this.isEditing&&this.stopEditing(),this.suggestBox&&this.suggestBox.hide()}startEditing(e){this.isEditing=!0,this.contentElement&&this.contentElement.classList.add("text-prompt-editing"),this.focusRestorer=new Di(this.element()),e&&(this.blurListener=e,this.element().addEventListener("blur",this.blurListener,!1)),this.oldTabIndex=this.element().tabIndex,this.element().tabIndex<0&&(this.element().tabIndex=0),this.text()||this.autoCompleteSoon()}stopEditing(){this.element().tabIndex=this.oldTabIndex,this.blurListener&&this.element().removeEventListener("blur",this.blurListener,!1),this.contentElement&&this.contentElement.classList.remove("text-prompt-editing"),delete this.isEditing}onMouseWheel(e){}onKeyDown(e){let t=!1;const n=e;if(this.isSuggestBoxVisible()&&this.suggestBox&&this.suggestBox.keyPressed(n))return s.logKeyDown(this.suggestBox.element,n),void n.consume(!0);switch(n.key){case"Tab":t=this.tabKeyPressed(n);break;case"ArrowLeft":case"ArrowUp":case"PageUp":case"Home":this.clearAutocomplete();break;case"PageDown":case"ArrowRight":case"ArrowDown":case"End":this.isCaretAtEndOfPrompt()?t=this.acceptAutoComplete():this.clearAutocomplete();break;case"Escape":this.isSuggestBoxVisible()&&(this.clearAutocomplete(),t=!0);break;case" ":!n.ctrlKey||n.metaKey||n.altKey||n.shiftKey||(this.autoCompleteSoon(!0),t=!0)}"Enter"===n.key&&n.preventDefault(),t&&n.consume(!0)}acceptSuggestionOnStopCharacters(e){if(!(this.currentSuggestion&&this.queryRange&&1===e.length&&this.completionStopCharacters&&this.completionStopCharacters.includes(e)))return!1;const t=this.text().substring(this.queryRange.startColumn,this.queryRange.endColumn);return!(!t||!this.currentSuggestion.text.startsWith(t+e))&&(this.queryRange.endColumn+=1,this.acceptAutoComplete())}onInput(e){const t=e;let i=this.text();const s=t.data;"insertFromPaste"===t.inputType&&i.includes("\n")&&(i=n.StringUtilities.stripLineBreaks(i),this.setText(i));const r=this.getCaretPosition();if(")"===s&&r>=0&&this.leftParenthesesIndices.length>0){if(")"===i[r]&&this.tryMatchingLeftParenthesis(r))return i=i.substring(0,r)+i.substring(r+1),void this.setText(i)}if(s&&!this.acceptSuggestionOnStopCharacters(s)){const e=i.startsWith(this.previousText)||this.previousText.startsWith(i);this.queryRange&&e&&(this.queryRange.endColumn+=i.length-this.previousText.length)}this.refreshGhostText(),this.previousText=i,this.dispatchEventToListeners("TextChanged"),this.changed=!0,this.autoCompleteSoon()}acceptAutoComplete(){let e=!1;return this.isSuggestBoxVisible()&&this.suggestBox&&(e=this.suggestBox.acceptSuggestion()),e||(e=this.acceptSuggestionInternal()),this.usesSuggestionBuilder&&e&&this.autoCompleteSoon(),e}clearAutocomplete(){const e=this.textWithCurrentSuggestion();this.isSuggestBoxVisible()&&this.suggestBox&&this.suggestBox.hide(),this.clearAutocompleteTimeout(),this.queryRange=null,this.refreshGhostText(),e!==this.textWithCurrentSuggestion()&&(this.dispatchEventToListeners("TextChanged"),this.changed=!0)}onBlur(){this.clearAutocomplete()}refreshGhostText(){this.currentSuggestion&&this.currentSuggestion.hideGhostText?this.ghostTextElement.remove():this.queryRange&&this.currentSuggestion&&this.isCaretAtEndOfPrompt()&&this.currentSuggestion.text.startsWith(this.text().substring(this.queryRange.startColumn))?(this.ghostTextElement.textContent=this.currentSuggestion.text.substring(this.queryRange.endColumn-this.queryRange.startColumn),this.element().appendChild(this.ghostTextElement)):this.ghostTextElement.remove()}clearAutocompleteTimeout(){this.completeTimeout&&(clearTimeout(this.completeTimeout),delete this.completeTimeout),this.completionRequestId++}autoCompleteSoon(e){const t=this.isSuggestBoxVisible()||e;this.completeTimeout||(this.completeTimeout=window.setTimeout(this.complete.bind(this,e),t?0:this.autocompletionTimeout))}async complete(e){this.clearAutocompleteTimeout();const t=this.element().getComponentSelection();if(!t||0===t.rangeCount)return;const i=t.getRangeAt(0);let s;if((e||this.isCaretAtEndOfPrompt()||this.isSuggestBoxVisible())&&t.isCollapsed||(s=!0),s)return void this.clearAutocomplete();const r=n.DOMUtilities.rangeOfWord(i.startContainer,i.startOffset,this.completionStopCharacters,this.element(),"backward"),o=r.cloneRange();o.collapse(!0),o.setStartBefore(this.element());const a=++this.completionRequestId,l=await this.loadCompletions.call(null,o.toString(),r.toString(),Boolean(e));this.completionsReady(a,t,r,Boolean(e),l)}disableDefaultSuggestionForEmptyInput(){this.disableDefaultSuggestionForEmptyInputInternal=!0}boxForAnchorAtStart(e,t){const n=e.getRangeAt(0).cloneRange(),i=document.createElement("span");i.textContent="​",t.insertNode(i);const s=i.boxInWindow(window);return i.remove(),e.removeAllRanges(),e.addRange(n),s}additionalCompletions(e){return[]}completionsReady(e,t,n,i,s){if(this.completionRequestId!==e)return;const r=n.toString(),a=new Set;if(s=s.filter((e=>!a.has(e.text)&&Boolean(a.add(e.text)))),(r||i)&&(s=r?s.concat(this.additionalCompletions(r)):this.additionalCompletions(r).concat(s)),!s.length)return void this.clearAutocomplete();const l=t.getRangeAt(0),c=document.createRange();if(c.setStart(n.startContainer,n.startOffset),c.setEnd(l.endContainer,l.endOffset),r+l.toString()!==c.toString())return;const d=document.createRange();d.setStart(this.element(),0),d.setEnd(c.startContainer,c.startOffset),this.queryRange=new o.TextRange.TextRange(0,d.toString().length,0,d.toString().length+c.toString().length);const h=!this.disableDefaultSuggestionForEmptyInputInternal||Boolean(this.text());this.suggestBox&&this.suggestBox.updateSuggestions(this.boxForAnchorAtStart(t,c),s,h,!this.isCaretAtEndOfPrompt(),this.text())}applySuggestion(e,t){this.currentSuggestion=e,this.refreshGhostText(),t&&(this.dispatchEventToListeners("TextChanged"),this.changed=!0)}acceptSuggestion(){this.acceptSuggestionInternal()}acceptSuggestionInternal(){if(!this.queryRange)return!1;const e=this.currentSuggestion?this.currentSuggestion.text.length:0,t=this.currentSuggestion?this.currentSuggestion.selectionRange:null,n=t?t.endColumn:e,i=t?t.startColumn:e;return this.element().textContent=this.textWithCurrentSuggestion(),this.setDOMSelection(this.queryRange.startColumn+i,this.queryRange.startColumn+n),this.updateLeftParenthesesIndices(),this.clearAutocomplete(),this.dispatchEventToListeners("TextChanged"),this.changed=!0,!0}ownerElement(){return this.element()}setDOMSelection(e,t){this.element().normalize();const n=this.element().childNodes[0];if(!n||n===this.ghostTextElement)return;const i=document.createRange();i.setStart(n,e),i.setEnd(n,t);const s=this.element().getComponentSelection();s&&(s.removeAllRanges(),s.addRange(i))}isSuggestBoxVisible(){return void 0!==this.suggestBox&&this.suggestBox.visible()}isCaretInsidePrompt(){const e=this.element().getComponentSelection();if(!e||0===e.rangeCount||!e.isCollapsed)return!1;return e.getRangeAt(0).startContainer.isSelfOrDescendant(this.element())}isCaretAtEndOfPrompt(){const e=this.element().getComponentSelection();if(!e||0===e.rangeCount||!e.isCollapsed)return!1;const t=e.getRangeAt(0);let n=t.startContainer;if(!n.isSelfOrDescendant(this.element()))return!1;if(this.ghostTextElement.isAncestor(n))return!0;if(n.nodeType===Node.TEXT_NODE&&t.startOffset<(n.nodeValue||"").length)return!1;let i=!1;for(;n;){if(n.nodeType===Node.TEXT_NODE&&n.nodeValue&&n.nodeValue.length){if(i&&!this.ghostTextElement.isAncestor(n))return!1;i=!0}n=n.traverseNextNode(this.elementInternal)}return!0}moveCaretToEndOfPrompt(){const e=this.element().getComponentSelection(),t=document.createRange();let n=this.element();for(;n.lastChild;)n=n.lastChild;let i=0;if(n.nodeType===Node.TEXT_NODE){i=(n.textContent||"").length}t.setStart(n,i),t.setEnd(n,i),e&&(e.removeAllRanges(),e.addRange(t))}getCaretPosition(){if(!this.element().hasFocus())return-1;const e=this.element().getComponentSelection();if(!e||0===e.rangeCount||!e.isCollapsed)return-1;const t=e.getRangeAt(0);return t.startOffset!==t.endOffset?-1:t.startOffset}tabKeyPressed(e){return this.acceptAutoComplete()}proxyElementForTests(){return this.proxyElement||null}tryMatchingLeftParenthesis(e){const t=this.leftParenthesesIndices;if(0===t.length||e<0)return!1;for(let n=t.length-1;n>=0;--n)if(t[n]<e)return t.splice(n,1),!0;return!1}updateLeftParenthesesIndices(){const e=this.text(),t=this.leftParenthesesIndices=[];for(let n=0;n<e.length;++n)"("===e[n]&&t.push(n)}suggestBoxForTest(){return this.suggestBox}}const Un=250;var _n=Object.freeze({__proto__:null,TextPrompt:Vn}),Kn={cssContent:':host{flex:none;padding:0 2px;--toolbar-height:26px}.toolbar-shadow{position:relative;white-space:nowrap;height:var(--toolbar-height);overflow:hidden;display:flex;flex:none;align-items:center;z-index:0}.toolbar-shadow.wrappable{flex-wrap:wrap;overflow:visible}.toolbar-shadow.toolbar-grow-vertical{height:initial}.toolbar-shadow.vertical{flex-direction:column;height:auto;align-items:flex-start}.toolbar-item{position:relative;display:flex;background-color:transparent;flex:none;align-items:center;justify-content:center;padding:0;height:var(--toolbar-height);border:none;white-space:pre;overflow:hidden;max-width:100%}devtools-adorner.fix-perf-icon{--override-adorner-text-color:transparent;--override-adorner-border-color:transparent;--override-adorner-background-color:transparent}.toolbar-item,\n.toolbar-item .devtools-link{color:var(--icon-default)}.toolbar-shadow.vertical .toolbar-item{height:auto;min-height:var(--toolbar-height);white-space:normal}devtools-issue-counter.toolbar-item{margin-top:-4px;padding-left:1px}devtools-issue-counter.main-toolbar{margin-left:1px;margin-right:1px}.toolbar-dropdown-arrow{pointer-events:none;flex:none;top:1px}.toolbar-button.dark-text .toolbar-dropdown-arrow{color:var(--sys-color-on-surface)}select.toolbar-item:disabled + .toolbar-dropdown-arrow{opacity:50%}.toolbar-button{white-space:nowrap;overflow:hidden;min-width:28px;background:transparent;border-radius:0;&[aria-haspopup="true"][aria-expanded="true"]{pointer-events:none}}.toolbar-item-search{min-width:5.2em;max-width:300px;flex:1 1 auto;justify-content:start;overflow:revert}.toolbar-text{margin:0 5px;flex:none;color:var(--ui-text)}.toolbar-text:empty{margin:0}.toolbar-has-dropdown{justify-content:space-between;height:var(--sys-size-9);padding:0 var(--sys-size-3) 0 var(--sys-size-5);gap:var(--sys-size-2);border-radius:var(--sys-shape-corner-extra-small);&:hover::after,\n  &:active::before{content:"";height:100%;width:100%;border-radius:inherit;position:absolute;top:0;left:0}&:hover::after{background-color:var(--sys-color-state-hover-on-subtle)}&:active::before{background-color:var(--sys-color-state-ripple-neutral-on-subtle)}&:focus-visible{border:2px solid var(--sys-color-state-focus-ring)}&[disabled]{pointer-events:none;background-color:var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}}.toolbar-has-dropdown-shrinkable{flex-shrink:1}.toolbar-has-dropdown .toolbar-text{margin:0;text-overflow:ellipsis;flex:auto;overflow:hidden;text-align:right}.toolbar-render-as-links *{font-weight:initial;color:var(--sys-color-primary);text-decoration:underline;cursor:pointer}.toolbar-render-as-links button{height:15px;margin:2px}.toolbar-render-as-links button:focus-visible{outline:auto 5px -webkit-focus-ring-color}:not(.toolbar-render-as-links) .toolbar-button:not(.toolbar-has-dropdown):focus-visible::before{position:absolute;top:2px;bottom:2px;left:2px;right:2px;background-color:var(--sys-color-state-focus-highlight);border-radius:2px;content:"";z-index:-1}.toolbar-glyph{flex:none}.toolbar-button:disabled{opacity:50%}.toolbar-button.close-devtools{position:absolute;right:0}:host-context(.right) .toolbar-button.main-menu,\n:host-context(.left) .toolbar-button.main-menu,\n:host-context(.bottom) .toolbar-button.main-menu{margin-inline-end:28px}.toolbar-button.copied-to-clipboard::after{content:attr(data-content);position:fixed;margin-top:calc(2 * var(--toolbar-height));padding:3px 5px;color:var(--sys-color-token-subtle);background:var(--sys-color-cdt-base-container);animation:2s fade-out;font-weight:normal;border:1px solid var(--sys-color-divider);border-radius:3px}@keyframes fade-out{from{opacity:100%}to{opacity:0%}}.toolbar-button.toolbar-state-on .toolbar-glyph{color:var(--icon-toggled)}.toolbar-state-on.toolbar-toggle-with-dot .toolbar-text::after{content:"";position:absolute;bottom:2px;background-color:var(--sys-color-primary-bright);width:4.5px;height:4.5px;border:2px solid var(--override-toolbar-background-color,--sys-color-cdt-base-container);border-radius:50%;right:0}.toolbar-button.toolbar-state-on.toolbar-toggle-with-red-color .toolbar-glyph,\n.toolbar-button.toolbar-state-off.toolbar-default-with-red-color .toolbar-glyph{color:var(--icon-error)!important}.toolbar-button:not(.toolbar-has-glyph):not(.toolbar-has-dropdown):not(.largeicon-menu):not(.toolbar-button-secondary){font-weight:bold}.toolbar-button.dark-text .toolbar-text{color:var(--sys-color-on-surface)!important}.toolbar-button.toolbar-state-on .toolbar-text{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:active .toolbar-text{color:var(--sys-color-primary-bright)}.toolbar-blue-on-hover .toolbar-button:not(.toolbar-state-on):enabled:hover:not(:active){background-color:var(--sys-color-state-hover-on-subtle)}.toolbar-blue-on-hover .toolbar-button:not(.toolbar-state-on):enabled:active:hover{background-color:var(--sys-color-state-hover-on-subtle)}.toolbar-toggled-gray:not(.toolbar-render-as-links) .toolbar-button:not(.toolbar-has-glyph):not(.toolbar-has-dropdown):not(.largeicon-menu):hover{background-color:var(--sys-color-state-hover-on-subtle)}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-on-surface)}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-on-surface)}.toolbar-blue-on-hover .toolbar-button:not(.toolbar-state-on):enabled:hover .toolbar-text{color:var(--sys-color-on-surface)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-glyph{color:var(--sys-color-primary)}.toolbar-button.toolbar-state-on:enabled:hover:not(:active) .toolbar-text{color:var(--sys-color-primary)}.toolbar-toggled-gray .toolbar-button.toolbar-state-on{background-color:var(--sys-color-neutral-container)!important}.toolbar-item.checkbox{padding:0 5px 0 0}.toolbar-select-container{display:inline-flex;flex-shrink:0;margin-right:6px}select.toolbar-item{min-width:38px;appearance:none;border:1px solid transparent;padding:0 13px 0 5px;margin-right:-10px;position:relative;height:22px;margin-top:2px;margin-bottom:2px}button.toolbar-item:focus-visible,\nselect.toolbar-item:focus-visible{background:var(--sys-color-state-hover-on-subtle);border-radius:2px;&.toolbar-has-dropdown{background:none;border-radius:var(--sys-shape-corner-extra-small)}}select.toolbar-item:focus-visible > *{background:var(--sys-color-cdt-base-container);border-radius:7px}select.toolbar-item:not(.toolbar-has-dropdown):disabled{opacity:50%}.toolbar-input{box-shadow:inset 0 0 0 2px transparent;box-sizing:border-box;width:120px;height:var(--sys-size-9);padding:0 var(--sys-size-2) 0 var(--sys-size-5);margin:1px 3px;border-radius:100px;min-width:35px;position:relative;&.focused{box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}&:not(:has(devtools-button:hover)):hover{background-color:var(--sys-color-state-hover-on-subtle)}&::before{content:"";box-sizing:inherit;height:100%;width:100%;position:absolute;left:0;background:var(--sys-color-cdt-base);z-index:-1}& > devtools-icon{color:var(--sys-color-on-surface-subtle);width:var(--sys-size-8);height:var(--sys-size-8);margin-right:var(--sys-size-3)}&.disabled > devtools-icon{color:var(--sys-color-state-disabled)}}.toolbar-input-empty .toolbar-input-clear-button{display:none}.toolbar-prompt-proxy{flex:1}.toolbar-input-prompt{flex:1;overflow:hidden;white-space:nowrap;cursor:auto;color:var(--sys-color-on-surface)}.toolbar-divider{background-color:var(--sys-color-on-base-divider);width:1px;margin:5px 4px;height:16px}.toolbar-spacer{flex:auto}.long-click-glyph{position:absolute;top:2px;left:3px}.toolbar-button.emulate-active{background-color:var(--sys-color-surface-variant)}.toolbar-shadow.floating{flex-direction:column;height:auto;background-color:var(--sys-color-cdt-base-container);border:1px solid var(--sys-color-divider);margin-top:-1px;width:28px;left:-2px}.toolbar-shadow:not(.floating) .toolbar-item:last-child:not(:first-child, .toolbar-select-container){flex-shrink:1;justify-content:left}.toolbar-shadow:not(.floating) .toolbar-button.toolbar-item:last-child:not(:first-child, .toolbar-select-container){justify-content:center;margin-right:2px}input[is="history-input"]{margin:0 1px;border:1px solid var(--sys-color-neutral-outline);border-radius:4px;line-height:16px;padding:1px 1px 1px 3px;color:var(--sys-color-on-surface)}input[is="history-input"]:focus,\ninput[is="history-input"]:not(:placeholder-shown){border-color:var(--sys-color-state-focus-ring)}input[is="history-input"]:hover:not(:focus){background-color:var(--sys-color-state-hover-on-subtle)}.toolbar-item.highlight::before{content:"";position:absolute;top:2px;left:2px;right:2px;bottom:2px;border-radius:2px;background:var(--sys-color-neutral-container);z-index:-1}.toolbar-item.highlight:focus-visible{background:var(--sys-color-tonal-container);& > .title{color:var(--sys-color-on-tonal-container)}}devtools-icon.leading-issue-icon{margin:0 7px}@media (forced-colors: active){.toolbar-button:disabled{opacity:100%;color:Graytext}.toolbar-item,\n  .toolbar-text{color:ButtonText}.toolbar-button:disabled .toolbar-text{color:Graytext}select.toolbar-item:disabled,\n  select.toolbar-item:disabled + .toolbar-dropdown-arrow{opacity:100%;color:Graytext}.toolbar-button.toolbar-state-on .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button.toolbar-state-on .toolbar-text{forced-color-adjust:none;color:Highlight}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-text,\n  :not(.toolbar-render-as-links) .toolbar-button:enabled:focus:not(:active) .toolbar-text{color:HighlightText}.toolbar-button:disabled devtools-icon{color:GrayText}:not(.toolbar-render-as-links) .toolbar-button:disabled .toolbar-glyph{color:GrayText}.toolbar-button:enabled.hover:not(:active) .toolbar-glyph{forced-color-adjust:none;color:Highlight}.toolbar-button:focus,\n  .toolbar-button:hover:enabled,\n  .toolbar-toggled-gray:not(.toolbar-render-as-links) .toolbar-button:not(.toolbar-has-glyph):not(.toolbar-has-dropdown):not(.largeicon-menu):hover{forced-color-adjust:none;background-color:Highlight}:not(.toolbar-render-as-links) .toolbar-button:enabled:hover .toolbar-glyph,\n  :not(.toolbar-render-as-links) .toolbar-button:enabled:focus .toolbar-glyph,\n  :not(.toolbar-render-as-links) .toolbar-button:enabled:hover:not(:active) .toolbar-glyph,\n  .toolbar-button:enabled:hover devtools-icon,\n  .toolbar-button:enabled:focus devtools-icon{color:HighlightText}.toolbar-input{forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-inactive-shadow)}.toolbar-input.focused,\n  .toolbar-input:not(.toolbar-input-empty){forced-color-adjust:none;background:canvas;box-shadow:var(--legacy-focus-ring-active-shadow)}.toolbar-input:hover{box-shadow:var(--legacy-focus-ring-active-shadow)}.toolbar-item .devtools-link{color:linktext}.toolbar-has-dropdown{forced-color-adjust:none;background:ButtonFace;color:ButtonText}}[aria-label="[FB-only] Send feedback"]{height:20px;padding:0 4px;border:1px solid var(--color-details-hairline);border-radius:4px;background:transparent}[aria-label="[FB-only] Send feedback"]:hover{background:color-mix(in srgb,var(--app-color-toolbar-background) 95%,black)}.fusebox-connection-status{margin:4px;height:20px;padding:0 4px;border-radius:4px;background:color-mix(in srgb,var(--color-red) 80%,transparent)}.fusebox-connection-status:hover{background:color-mix(in srgb,var(--color-red) 90%,transparent)}.fusebox-connection-status .toolbar-text,\n.fusebox-connection-status .toolbar-glyph{color:white!important}.toolbar-item.open-in-external-editor-button{background-color:transparent;border:none}.toolbar-item.open-in-external-editor-button:hover{background-color:var(--sys-color-state-hover-on-subtle)}.open-in-external-editor-adorner{background-repeat:no-repeat;background-origin:content-box;background-size:contain;padding:4px;width:28px;height:28px;margin-right:-4px}\n/*# sourceURL=toolbar.css */\n'};const qn={pressed:"pressed",notPressed:"not pressed",clearInput:"Clear",filter:"Filter"},Gn=t.i18n.registerUIStrings("ui/legacy/Toolbar.ts",qn),$n=t.i18n.getLocalizedString.bind(void 0,Gn);class Xn{items;element;enabled;shadowRoot;contentElement;compactLayout=!1;constructor(e,t){this.items=[],this.element=t?t.createChild("div"):document.createElement("div"),this.element.className=e,this.element.classList.add("toolbar"),this.enabled=!0,this.shadowRoot=ds(this.element,{cssFile:Kn,delegatesFocus:void 0}),this.contentElement=this.shadowRoot.createChild("div","toolbar-shadow")}hasCompactLayout(){return this.compactLayout}registerCSSFiles(e){this.shadowRoot.adoptedStyleSheets=this.shadowRoot.adoptedStyleSheets.concat(e)}setCompactLayout(e){if(this.compactLayout!==e){this.compactLayout=e;for(const t of this.items)t.setCompactLayout(e)}}static createLongPressActionButton(e,t,n){const i=Xn.createActionButton(e),s=Xn.createActionButton(e);let r=null,o=null,a=null;return e.addEventListener("Toggled",c),c(),i;function c(){const s=e.toggled()?t||null:n||null;s&&s.length?r||(r=new Wi(i.element,d),a=l.Icon.create("triangle-bottom-right","long-click-glyph"),i.element.appendChild(a),o=s):r&&(r.dispose(),r=null,a&&a.remove(),a=null,o=null)}function d(){let e=o?o.slice():[];e.push(s);const t=i.element.ownerDocument;t.documentElement.addEventListener("mouseup",(function i(s){if(1!==s.which)return;n.hide(),t.documentElement.removeEventListener("mouseup",i,!1);for(let t=0;t<e.length;++t)if(e[t].element.classList.contains("emulate-active")){e[t].element.classList.remove("emulate-active"),e[t].clicked(s);break}}),!1);const n=new ms;n.setPointerEventsBehavior("BlockedByGlassPane"),n.show(t);const r=new Xn("fill",n.contentElement);r.contentElement.classList.add("floating");const a=i.element.boxInWindow().relativeToElement(ms.container(t)),l=a.y+26*e.length<t.documentElement.offsetHeight;l&&(e=e.reverse()),r.element.style.height=26*e.length+"px",r.element.style.top=l?a.y-5+"px":a.y-26*(e.length-1)-6+"px",r.element.style.left=a.x-5+"px";for(let t=0;t<e.length;++t)e[t].element.addEventListener("mousemove",d,!1),e[t].element.addEventListener("mouseout",h,!1),r.appendToolbarItem(e[t]);const c=l?0:e.length-1;function d(e){if(1===e.which&&e.target instanceof HTMLElement){e.target.enclosingNodeOrSelfWithClass("toolbar-item").classList.add("emulate-active")}}function h(e){if(1===e.which&&e.target instanceof HTMLElement){e.target.enclosingNodeOrSelfWithClass("toolbar-item").classList.remove("emulate-active")}}e[c].element.classList.add("emulate-active")}}static createActionButton(e,t=Yn){const n=e.toggleable()?function(){const t=new ii(e.title(),e.icon(),e.toggledIcon(),e.id());return t.setToggleWithRedColor(e.toggleWithRedColor()),e.addEventListener("Toggled",n),n(),t;function n(){t.setToggled(e.toggled()),e.title()&&(t.setTitle(e.title()),wt.installWithActionBinding(t.element,e.title(),e.id()))}}():function(){const t=new Jn(e.title(),e.icon(),void 0,e.id());e.title()&&wt.installWithActionBinding(t.element,e.title(),e.id());return t}();t.showLabel&&n.setText(t.label?.()||e.title());let i=()=>{e.execute()};if(t.userActionCode){const n=t.userActionCode;i=()=>{r.userMetrics.actionTaken(n),e.execute()}}return n.addEventListener("Click",i,e),e.addEventListener("Enabled",(function(e){n.setEnabled(e.data)})),n.setEnabled(e.enabled()),n}static createActionButtonForId(e,t){const n=T.instance().getAction(e);return Xn.createActionButton(n,t)}gripElementForResize(){return this.contentElement}makeWrappable(e){this.contentElement.classList.add("wrappable"),e&&this.contentElement.classList.add("toolbar-grow-vertical")}makeVertical(){this.contentElement.classList.add("vertical")}makeBlueOnHover(){this.contentElement.classList.add("toolbar-blue-on-hover")}makeToggledGray(){this.contentElement.classList.add("toolbar-toggled-gray")}renderAsLinks(){this.contentElement.classList.add("toolbar-render-as-links")}empty(){return!this.items.length}setEnabled(e){this.enabled=e;for(const e of this.items)e.applyEnabledState(this.enabled&&e.enabled)}appendToolbarItem(e){this.items.push(e),e.toolbar=this,e.setCompactLayout(this.hasCompactLayout()),this.enabled||e.applyEnabledState(!1),this.contentElement.appendChild(e.element),this.hideSeparatorDupes()}appendSeparator(){this.appendToolbarItem(new oi)}appendSpacer(){this.appendToolbarItem(new oi(!0))}appendText(e){this.appendToolbarItem(new Zn(e))}removeToolbarItem(e){const t=[];for(const n of this.items)n===e?n.element.remove():t.push(n);this.items=t}removeToolbarItems(){for(const e of this.items)e.toolbar=null;this.items=[],this.contentElement.removeChildren()}setColor(e){const t=document.createElement("style");t.textContent=".toolbar-glyph { background-color: "+e+" !important }",this.shadowRoot.appendChild(t)}setToggledColor(e){const t=document.createElement("style");t.textContent=".toolbar-button.toolbar-state-on .toolbar-glyph { background-color: "+e+" !important }",this.shadowRoot.appendChild(t)}hideSeparatorDupes(){if(!this.items.length)return;let e,t=!1,n=!1;for(let i=0;i<this.items.length;++i)this.items[i]instanceof oi?(this.items[i].setVisible(!t),t=!0,e=this.items[i]):this.items[i].visible()&&(t=!1,e=null,n=!0);e&&e!==this.items[this.items.length-1]&&e.setVisible(!1),this.element.classList.toggle("hidden",null!=e&&e.visible()&&!n)}async appendItemsAtLocation(e){const t=ci.filter((e=>i.Runtime.Runtime.isDescriptorEnabled({experiment:e.experiment,condition:e.condition})));t.sort(((e,t)=>(e.order||0)-(t.order||0)));const n=t.filter((t=>t.location===e)),s=await Promise.all(n.map((e=>{const{separator:t,actionId:n,showLabel:i,label:s,loadItem:r}=e;if(t)return new oi;if(n)return Xn.createActionButtonForId(n,{label:s,showLabel:Boolean(i),userActionCode:void 0});if(!r)throw new Error("Could not load a toolbar item registration with no loadItem function");return r().then((e=>e.item()))})));for(const e of s)e&&this.appendToolbarItem(e)}}const Yn={showLabel:!1,userActionCode:void 0};class Qn extends e.ObjectWrapper.ObjectWrapper{element;visibleInternal;enabled;toolbar;title;constructor(e){super(),this.element=e,this.element.classList.add("toolbar-item"),this.visibleInternal=!0,this.enabled=!0,this.toolbar=null}setTitle(e,t=void 0){this.title!==e&&(this.title=e,or(this.element,e),void 0===t?wt.install(this.element,e):wt.installWithActionBinding(this.element,e,t))}setEnabled(e){this.enabled!==e&&(this.enabled=e,this.applyEnabledState(this.enabled&&(!this.toolbar||this.toolbar.enabled)))}applyEnabledState(e){this.element.disabled=!e}visible(){return this.visibleInternal}setVisible(e){this.visibleInternal!==e&&(this.element.classList.toggle("hidden",!e),this.visibleInternal=e,!this.toolbar||this instanceof oi||this.toolbar.hideSeparatorDupes())}setRightAligned(e){this.element.classList.toggle("toolbar-item-right-aligned",e)}setCompactLayout(e){}}class Zn extends Qn{constructor(e){const t=document.createElement("div");t.classList.add("toolbar-text"),super(t),this.element.classList.add("toolbar-text"),this.setText(e||"")}text(){return this.element.textContent||""}setText(e){this.element.textContent=e}}class Jn extends Qn{glyphElement;textElement;text;glyph;adorner;constructor(e,t,n,i){const r=document.createElement("button");r.classList.add("toolbar-button"),super(r),this.element.addEventListener("click",this.clicked.bind(this),!1),this.element.addEventListener("mousedown",this.mouseDown.bind(this),!1),this.glyphElement=new l.Icon.Icon,this.glyphElement.className="toolbar-glyph hidden",this.element.appendChild(this.glyphElement),this.textElement=this.element.createChild("div","toolbar-text hidden"),this.setTitle(e),t&&this.setGlyphOrAdorner(t),this.setText(n||""),i&&this.element.setAttribute("jslog",`${s.action().track({click:!0}).context(i)}`),this.title=""}focus(){this.element.focus()}setText(e){this.text!==e&&(this.textElement.textContent=e,this.textElement.classList.toggle("hidden",!e),this.text=e)}setGlyphOrAdorner(e){e instanceof d.Adorner.Adorner?(this.adorner?this.adorner.replaceWith(e):this.element.prepend(e),this.adorner=e):this.setGlyph(e)}setGlyph(e){this.glyph!==e&&(this.glyphElement.name=e||null,this.glyphElement.classList.toggle("hidden",!e),this.element.classList.toggle("toolbar-has-glyph",Boolean(e)),this.glyph=e)}setBackgroundImage(e){this.element.style.backgroundImage="url("+e+")"}setSecondary(){this.element.classList.add("toolbar-button-secondary")}setDarkText(){this.element.classList.add("dark-text")}turnIntoSelect(e=!1){this.element.classList.add("toolbar-has-dropdown"),e&&this.element.classList.add("toolbar-has-dropdown-shrinkable");const t=l.Icon.create("triangle-down","toolbar-dropdown-arrow");this.element.appendChild(t)}clicked(e){this.enabled&&(this.dispatchEventToListeners("Click",e),e.consume())}mouseDown(e){this.enabled&&this.dispatchEventToListeners("MouseDown",e)}}class ei extends Qn{glyphElement;textElement;text;glyph;constructor(e,t,n){const i=document.createElement("button");if(i.classList.add("toolbar-button"),super(i),this.element.addEventListener("click",this.clicked.bind(this),!1),this.element.addEventListener("mousedown",this.mouseDown.bind(this),!1),this.glyphElement=new l.Icon.Icon,this.glyphElement.className="toolbar-glyph hidden",this.element.appendChild(this.glyphElement),this.textElement=this.element.createChild("div","toolbar-text hidden"),this.setTitle(e),n&&this.element.setAttribute("jslog",`${s.action().track({click:!0}).context(n)}`),this.title="",!t){this.element.classList.add("toolbar-has-dropdown");const e=l.Icon.create("triangle-down","toolbar-dropdown-arrow");this.element.appendChild(e)}}setText(e){this.text!==e&&(this.textElement.textContent=e,this.textElement.classList.toggle("hidden",!e),this.text=e)}setGlyph(e){this.glyph!==e&&(this.glyphElement.name=e||null,this.glyphElement.classList.toggle("hidden",!e),this.element.classList.toggle("toolbar-has-glyph",Boolean(e)),this.glyph=e)}setDarkText(){this.element.classList.add("dark-text")}turnShrinkable(){this.element.classList.add("toolbar-has-dropdown-shrinkable")}clicked(e){this.enabled&&(this.dispatchEventToListeners("Click",e),e.consume())}mouseDown(e){this.enabled&&this.dispatchEventToListeners("MouseDown",e)}}class ti extends Qn{prompt;proxyElement;constructor(e,t,n,i,r,o,l,c){const d=document.createElement("div");d.classList.add("toolbar-input"),super(d);const h=this.element.createChild("div","toolbar-input-prompt");or(h,t||e),h.addEventListener("focus",(()=>this.element.classList.add("focused"))),h.addEventListener("blur",(()=>this.element.classList.remove("focused"))),this.prompt=new Vn,this.prompt.jslogContext=c,this.proxyElement=this.prompt.attach(h),this.proxyElement.classList.add("toolbar-prompt-proxy"),this.proxyElement.addEventListener("keydown",(e=>this.onKeydownCallback(e))),this.prompt.initialize(o||(()=>Promise.resolve([]))," ",l),r&&this.prompt.setTitle(r),this.prompt.setPlaceholder(e,t),this.prompt.addEventListener("TextChanged",this.onChangeCallback.bind(this)),n&&(this.element.style.flexGrow=String(n)),i&&(this.element.style.flexShrink=String(i));const u=$n(qn.clearInput),p=new a.Button.Button;p.className="toolbar-input-clear-button",p.setAttribute("jslog",`${s.action("clear").track({click:!0}).parent("mapped")}`),s.setMappedParent(p,h),p.variant="icon",p.size="SMALL",p.iconName="cross-circle-filled",p.title=u,p.ariaLabel=u,p.tabIndex=-1,p.addEventListener("click",(()=>{this.setValue("",!0),this.prompt.focus()})),this.element.appendChild(p),this.updateEmptyStyles()}applyEnabledState(e){e?this.element.classList.remove("disabled"):this.element.classList.add("disabled"),this.prompt.setEnabled(e)}setValue(e,t){this.prompt.setText(e),t&&this.onChangeCallback(),this.updateEmptyStyles()}value(){return this.prompt.textWithCurrentSuggestion()}valueWithoutSuggestion(){return this.prompt.text()}clearAutocomplete(){this.prompt.clearAutocomplete()}focus(){this.prompt.focus()}onKeydownCallback(e){"Enter"===e.key&&this.prompt.text()&&this.dispatchEventToListeners("EnterPressed",this.prompt.text()),n.KeyboardUtilities.isEscKey(e)&&this.prompt.text()&&(this.setValue("",!0),e.consume(!0))}onChangeCallback(){this.updateEmptyStyles(),this.dispatchEventToListeners("TextChanged",this.prompt.text())}updateEmptyStyles(){this.element.classList.toggle("toolbar-input-empty",!this.prompt.text())}}class ni extends ti{constructor(e,t,n,i,s,r,o){const a=e||$n(qn.filter);super(a,a,t,n,i,s,r,o);const c=l.Icon.create("filter");this.element.prepend(c)}}class ii extends Jn{toggledInternal;untoggledGlyph;toggledGlyph;constructor(e,t,n,i){super(e,t,""),this.toggledInternal=!1,this.untoggledGlyph=t,this.toggledGlyph=n,this.element.classList.add("toolbar-state-off"),rr(this.element,!1),i&&this.element.setAttribute("jslog",`${s.toggle().track({click:!0}).context(i)}`)}toggled(){return this.toggledInternal}setToggled(e){this.toggledInternal!==e&&(this.toggledInternal=e,this.element.classList.toggle("toolbar-state-on",e),this.element.classList.toggle("toolbar-state-off",!e),rr(this.element,e),this.toggledGlyph&&this.untoggledGlyph&&this.setGlyph(e?this.toggledGlyph:this.untoggledGlyph))}setDefaultWithRedColor(e){this.element.classList.toggle("toolbar-default-with-red-color",e)}setToggleWithRedColor(e){this.element.classList.toggle("toolbar-toggle-with-red-color",e)}setToggleWithDot(e){this.element.classList.toggle("toolbar-toggle-with-dot",e)}}class si extends ei{contextMenuHandler;useSoftMenu;triggerTimeout;constructor(e,t,n,i){super("",t,i),i&&this.element.setAttribute("jslog",`${s.dropDown().track({click:!0}).context(i)}`),this.contextMenuHandler=e,this.useSoftMenu=Boolean(n),Ps(this.element)}mouseDown(e){1===e.buttons?this.triggerTimeout||(this.triggerTimeout=window.setTimeout(this.trigger.bind(this,e),200)):super.mouseDown(e)}trigger(e){delete this.triggerTimeout;const t=new xn(e,{useSoftMenu:this.useSoftMenu,x:this.element.getBoundingClientRect().left,y:this.element.getBoundingClientRect().top+this.element.offsetHeight,onSoftMenuClosed:()=>requestAnimationFrame((()=>this.element.removeAttribute("aria-expanded")))});this.contextMenuHandler(t),this.element.setAttribute("aria-expanded","true"),t.show()}clicked(e){this.triggerTimeout&&clearTimeout(this.triggerTimeout),this.trigger(e)}}class ri extends ii{defaultTitle;setting;willAnnounceState;constructor(e,t,n,i,s){super(n,t,i,s),this.defaultTitle=n,this.setting=e,this.settingChanged(),this.setting.addChangeListener(this.settingChanged,this),this.willAnnounceState=!1}settingChanged(){const e=this.setting.get();this.setToggled(e);const t=$n(e?qn.pressed:qn.notPressed);this.willAnnounceState&&mr(t),this.willAnnounceState=!1,this.setTitle(this.defaultTitle)}clicked(e){this.willAnnounceState=!0,this.setting.set(!this.toggled()),super.clicked(e)}}class oi extends Qn{constructor(e){const t=document.createElement("div");t.classList.add(e?"toolbar-spacer":"toolbar-divider"),super(t)}}class ai extends Qn{selectElementInternal;constructor(e,t,n,i){const r=document.createElement("span");r.classList.add("toolbar-select-container"),super(r),this.selectElementInternal=this.element.createChild("select","toolbar-item");const o=l.Icon.create("triangle-down","toolbar-dropdown-arrow");this.element.appendChild(o),e&&this.selectElementInternal.addEventListener("change",e,!1),or(this.selectElementInternal,t),super.setTitle(t),n&&this.selectElementInternal.classList.add(n),i&&this.selectElementInternal.setAttribute("jslog",`${s.dropDown().track({change:!0}).context(i)}`)}selectElement(){return this.selectElementInternal}size(){return this.selectElementInternal.childElementCount}options(){return Array.prototype.slice.call(this.selectElementInternal.children,0)}addOption(e){this.selectElementInternal.appendChild(e)}createOption(e,t){const i=this.selectElementInternal.createChild("option");i.text=e,void 0!==t&&(i.value=t);const r=t?n.StringUtilities.toKebabCase(t):void 0;return i.setAttribute("jslog",`${s.item(r).track({click:!0})}`),i}applyEnabledState(e){super.applyEnabledState(e),this.selectElementInternal.disabled=!e}removeOption(e){this.selectElementInternal.removeChild(e)}removeOptions(){this.selectElementInternal.removeChildren()}selectedOption(){return this.selectElementInternal.selectedIndex>=0?this.selectElementInternal[this.selectElementInternal.selectedIndex]:null}select(e){this.selectElementInternal.selectedIndex=Array.prototype.indexOf.call(this.selectElementInternal,e)}setSelectedIndex(e){this.selectElementInternal.selectedIndex=e}selectedIndex(){return this.selectElementInternal.selectedIndex}setMaxWidth(e){this.selectElementInternal.style.maxWidth=e+"px"}setMinWidth(e){this.selectElementInternal.style.minWidth=e+"px"}}class li extends Qn{inputElement;constructor(e,t,n,i,r){super(Ki.create(e)),this.element.classList.add("checkbox"),this.inputElement=this.element.checkboxElement,this.inputElement.classList.toggle("small",r),t&&(wt.install(this.inputElement,t),wt.install(this.element.textElement,t)),n&&this.inputElement.addEventListener("click",n,!1),i&&this.inputElement.setAttribute("jslog",`${s.toggle().track({change:!0}).context(i)}`)}checked(){return this.inputElement.checked}setChecked(e){this.inputElement.checked=e}applyEnabledState(e){super.applyEnabledState(e),this.inputElement.disabled=!e}setIndeterminate(e){this.inputElement.indeterminate=e}}const ci=[];var di=Object.freeze({__proto__:null,Toolbar:Xn,ToolbarItem:Qn,ToolbarItemWithCompactLayout:class extends Qn{constructor(e){super(e)}setCompactLayout(e){this.dispatchEventToListeners("CompactLayoutUpdated",e)}},ToolbarText:Zn,ToolbarButton:Jn,ToolbarCombobox:ei,ToolbarInput:ti,ToolbarFilter:ni,ToolbarToggle:ii,ToolbarMenuButton:si,ToolbarSettingToggle:ri,ToolbarSeparator:oi,ToolbarComboBox:ai,ToolbarSettingComboBox:class extends ai{optionsInternal;setting;muteSettingListener;constructor(e,t,n){super(null,n),this.optionsInternal=e,this.setting=t,this.selectElementInternal.addEventListener("change",this.valueChanged.bind(this),!1),this.setOptions(e),t.addChangeListener(this.settingChanged,this)}setOptions(e){this.optionsInternal=e,this.selectElementInternal.removeChildren();for(let t=0;t<e.length;++t){const n=e[t],i=this.createOption(n.label,n.value);this.selectElementInternal.appendChild(i),this.setting.get()===n.value&&this.setSelectedIndex(t)}}value(){return this.optionsInternal[this.selectedIndex()].value}settingChanged(){if(this.muteSettingListener)return;const e=this.setting.get();for(let t=0;t<this.optionsInternal.length;++t)if(e===this.optionsInternal[t].value){this.setSelectedIndex(t);break}}valueChanged(e){const t=this.optionsInternal[this.selectedIndex()];this.muteSettingListener=!0,this.setting.set(t.value),this.muteSettingListener=!1}},ToolbarCheckbox:li,ToolbarSettingCheckbox:class extends li{constructor(e,t,n){super(n||e.title()||"",t,void 0,e.name),Ln(this.inputElement,e)}},registerToolbarItem:function(e){ci.push(e)}});const hi={openInNewTab:"Open in new tab",copyLinkAddress:"Copy link address",copyFileName:"Copy file name",anotherProfilerIsAlreadyActive:"Another profiler is already active",promiseResolvedAsync:"Promise resolved (async)",promiseRejectedAsync:"Promise rejected (async)",asyncCall:"Async Call",anonymous:"(anonymous)",close:"Close",ok:"OK",cancel:"Cancel"},ui=t.i18n.registerUIStrings("ui/legacy/UIUtils.ts",hi),pi=t.i18n.getLocalizedString.bind(void 0,ui),gi="highlighted-search-result";function mi(e,t,n,i,s,r,o){let a;e.addEventListener("pointerdown",(function(r){const l=new fi,c=()=>l.elementDragStart(e,t,n,i,s,r);o?a=window.setTimeout(c,o):c()}),!1),o&&e.addEventListener("pointerup",(function(){a&&window.clearTimeout(a),a=null}),!1),null!==r&&(e.style.cursor=r||s||"")}function bi(e,t,n,i,s,r){(new fi).elementDragStart(e,t,n,i,s,r)}class fi{glassPaneInUse;elementDraggingEventListener;elementEndDraggingEventListener;dragEventsTargetDocument;dragEventsTargetDocumentTop;restoreCursorAfterDrag;constructor(){this.elementDragMove=this.elementDragMove.bind(this),this.elementDragEnd=this.elementDragEnd.bind(this),this.mouseOutWhileDragging=this.mouseOutWhileDragging.bind(this)}createGlassPane(){this.glassPaneInUse=!0,fi.glassPaneUsageCount++||(fi.glassPane=new ms,fi.glassPane.setPointerEventsBehavior("BlockedByGlassPane"),fi.documentForMouseOut&&fi.glassPane.show(fi.documentForMouseOut))}disposeGlassPane(){this.glassPaneInUse&&(this.glassPaneInUse=!1,--fi.glassPaneUsageCount||(fi.glassPane&&(fi.glassPane.hide(),fi.glassPane=null),fi.documentForMouseOut=null,fi.rootForMouseOut=null))}elementDragStart(e,t,n,i,s,o){const a=o;if(a.button||r.Platform.isMac()&&a.ctrlKey)return;if(this.elementDraggingEventListener)return;if(t&&!t(a))return;const l=a.target instanceof Node&&a.target.ownerDocument;this.elementDraggingEventListener=n,this.elementEndDraggingEventListener=i,console.assert((fi.documentForMouseOut||l)===l,"Dragging on multiple documents."),fi.documentForMouseOut=l,fi.rootForMouseOut=a.target instanceof Node&&a.target.getRootNode()||null,this.dragEventsTargetDocument=l;try{l.defaultView&&l.defaultView.top&&(this.dragEventsTargetDocumentTop=l.defaultView.top.document)}catch(e){this.dragEventsTargetDocumentTop=this.dragEventsTargetDocument}l.addEventListener("pointermove",this.elementDragMove,!0),l.addEventListener("pointerup",this.elementDragEnd,!0),fi.rootForMouseOut&&fi.rootForMouseOut.addEventListener("pointerout",this.mouseOutWhileDragging,{capture:!0}),this.dragEventsTargetDocumentTop&&l!==this.dragEventsTargetDocumentTop&&this.dragEventsTargetDocumentTop.addEventListener("pointerup",this.elementDragEnd,!0);const c=e;"string"==typeof s&&(this.restoreCursorAfterDrag=function(e){l.body.style.removeProperty("cursor"),c.style.cursor=e,this.restoreCursorAfterDrag=void 0}.bind(this,c.style.cursor),c.style.cursor=s,l.body.style.cursor=s),a.preventDefault()}mouseOutWhileDragging(){this.unregisterMouseOutWhileDragging(),this.createGlassPane()}unregisterMouseOutWhileDragging(){fi.rootForMouseOut&&fi.rootForMouseOut.removeEventListener("pointerout",this.mouseOutWhileDragging,{capture:!0})}unregisterDragEvents(){this.dragEventsTargetDocument&&(this.dragEventsTargetDocument.removeEventListener("pointermove",this.elementDragMove,!0),this.dragEventsTargetDocument.removeEventListener("pointerup",this.elementDragEnd,!0),this.dragEventsTargetDocumentTop&&this.dragEventsTargetDocument!==this.dragEventsTargetDocumentTop&&this.dragEventsTargetDocumentTop.removeEventListener("pointerup",this.elementDragEnd,!0),delete this.dragEventsTargetDocument,delete this.dragEventsTargetDocumentTop)}elementDragMove(e){1===e.buttons?this.elementDraggingEventListener&&this.elementDraggingEventListener(e)&&this.cancelDragEvents(e):this.elementDragEnd(e)}cancelDragEvents(e){this.unregisterDragEvents(),this.unregisterMouseOutWhileDragging(),this.restoreCursorAfterDrag&&this.restoreCursorAfterDrag(),this.disposeGlassPane(),delete this.elementDraggingEventListener,delete this.elementEndDraggingEventListener}elementDragEnd(e){const t=this.elementEndDraggingEventListener;this.cancelDragEvents(e),e.preventDefault(),t&&t(e)}static glassPaneUsageCount=0;static glassPane=null;static documentForMouseOut=null;static rootForMouseOut=null}function vi(){if(wi.size)return!0;const e=n.DOMUtilities.deepActiveElement(document);return!!e&&(e.classList.contains("text-prompt")||"INPUT"===e.nodeName||"TEXTAREA"===e.nodeName||"true"===e.contentEditable||"plaintext-only"===e.contentEditable)}function yi(e,t){if(t){if(wi.has(e))return!1;e.classList.add("being-edited"),wi.add(e)}else{if(!wi.has(e))return!1;e.classList.remove("being-edited"),wi.delete(e)}return!0}const wi=new Set,xi=/^(-?(?:\d+(?:\.\d+)?|\.\d+))$/,Ei="  \t\n\"':;,/()";function Ii(e){let t=null;if("wheel"===e.type){const n=e;n.deltaY<0||n.deltaX<0?t="Up":(n.deltaY>0||n.deltaX>0)&&(t="Down")}else{const n=e;"ArrowUp"===n.key||"PageUp"===n.key?t="Up":"ArrowDown"!==n.key&&"PageDown"!==n.key||(t="Down")}return t}function Si(e,t,n){const i=Ii(t);if(!i)return null;const s=t;let r=1;ie.eventHasCtrlEquivalentKey(s)?r=100:s.shiftKey?r=10:s.altKey&&(r=.1),"Down"===i&&(r*=-1),n&&(r*=n);const o=Number((e+r).toFixed(6));return String(o).match(xi)?o:null}function ki(e,t,i){let s,r,o,a=null,l=/(.*#)([\da-fA-F]+)(.*)/.exec(e);return l&&l.length?(s=l[1],r=l[3],o=function(e,t){const i=Ii(t);if(!i)return null;const s=t,r=parseInt(e,16);if(isNaN(r)||!isFinite(r))return null;const o=e.length,a=o/3;if(1!==a&&2!==a)return null;let l=0;ie.eventHasCtrlEquivalentKey(s)&&(l+=Math.pow(16,2*a)),s.shiftKey&&(l+=Math.pow(16,a)),s.altKey&&(l+=1),0===l&&(l=1),"Down"===i&&(l*=-1);const c=Math.pow(16,o)-1;let d=n.NumberUtilities.clamp(r+l,0,c).toString(16).toUpperCase();for(let e=0,t=o-d.length;e<t;++e)d="0"+d;return d}(l[2],t),null!==o&&(a=s+o+r)):(l=/(.*?)(-?(?:\d+(?:\.\d+)?|\.\d+))(.*)/.exec(e),l&&l.length&&(s=l[1],r=l[3],o=Si(parseFloat(l[2]),t),null!==o&&(a=i?i(s,o,r):s+o+r))),a}function Ci(e){const t="ArrowUp"===e.key||"ArrowDown"===e.key||"wheel"===e.type,n="PageUp"===e.key||"PageDown"===e.key;return t||n}function Ti(){return pi(hi.openInNewTab)}function Li(){return pi(hi.copyLinkAddress)}function Mi(e,t){t.target instanceof Window&&t.target.document.nodeType===Node.DOCUMENT_NODE&&e.body.classList.remove("inactive")}function Pi(e,t){t.target instanceof Window&&t.target.document.nodeType===Node.DOCUMENT_NODE&&e.body.classList.add("inactive")}class Di{element;previous;constructor(e){this.element=e,this.previous=n.DOMUtilities.deepActiveElement(e.ownerDocument),e.focus()}restore(){this.element&&(this.element.hasFocus()&&this.previous&&this.previous.focus(),this.previous=null,this.element=null)}}function Ai(e,t,n){return Ri(e,t,gi,n)}function Ri(e,t,n,i){i=i||[];const s=[],r=e.childTextNodes(),a=r.map((function(e){return e.textContent})).join(""),l=e.ownerDocument;if(0===r.length)return s;const c=[];let d=0;for(const e of r){const t=new o.TextRange.SourceRange(d,e.textContent?e.textContent.length:0);d=t.offset+t.length,c.push(t)}let h=0;for(let e=0;e<t.length;++e){const o=t[e].offset,d=o+t[e].length;for(;h<r.length&&c[h].offset+c[h].length<=o;)h++;let u=h;for(;u<r.length&&c[u].offset+c[u].length<d;)u++;if(u===r.length)break;const p=l.createElement("span");p.className=n,p.textContent=a.substring(o,d);const g=r[u],m=g.textContent||"";if(g.textContent=m.substring(d-c[u].offset),i.push({node:g,type:"changed",oldText:m,newText:g.textContent,nextSibling:void 0,parent:void 0}),h===u&&g.parentElement){g.parentElement.insertBefore(p,g),i.push({node:p,type:"added",nextSibling:g,parent:g.parentElement,oldText:void 0,newText:void 0}),s.push(p);const e=l.createTextNode(m.substring(0,o-c[h].offset));g.parentElement.insertBefore(e,p),i.push({node:e,type:"added",nextSibling:p,parent:g.parentElement,oldText:void 0,newText:void 0})}else{const e=r[h],t=e.textContent||"",n=e.nextSibling;e.parentElement&&(e.parentElement.insertBefore(p,n),i.push({node:p,type:"added",nextSibling:n||void 0,parent:e.parentElement,oldText:void 0,newText:void 0}),s.push(p)),e.textContent=t.substring(0,o-c[h].offset),i.push({node:e,type:"changed",oldText:t,newText:e.textContent,nextSibling:void 0,parent:void 0});for(let e=h+1;e<u;e++){const t=r[e],n=t.textContent;t.textContent="",i.push({node:t,type:"changed",oldText:n||void 0,newText:t.textContent,nextSibling:void 0,parent:void 0})}}h=u,c[h].offset=d,c[h].length=g.textContent.length}return s}function Bi(e,t){const n=e.parentElement,i=e.nextSibling;(t=t||e.ownerDocument.body).appendChild(e),e.positionAt(0,0);const s=e.getBoundingClientRect();return e.positionAt(void 0,void 0),n?n.insertBefore(e,i):e.remove(),new _(s.width,s.height)}class zi{handlers;autoInvoke;constructor(e){this.handlers=null,this.autoInvoke=e}add(e,t){this.handlers||(this.handlers=new Map,this.autoInvoke&&this.scheduleInvoke());let n=this.handlers.get(e);n||(n=new Set,this.handlers.set(e,n)),n.add(t)}scheduleInvoke(){this.handlers&&requestAnimationFrame(this.invoke.bind(this))}invoke(){const e=this.handlers;if(this.handlers=null,e)for(const[t,n]of e)for(const e of n)e.call(t)}}let Oi=0,Fi=null;function Hi(e,t){Fi||(Fi=new zi(!0)),Fi.add(e,t)}class Wi{element;callback;editKey;longClickData;longClickInterval;constructor(e,t,i=(e=>n.KeyboardUtilities.isEnterOrSpaceKey(e))){this.element=e,this.callback=t,this.editKey=i,this.enable()}reset(){this.longClickInterval&&(clearInterval(this.longClickInterval),delete this.longClickInterval)}enable(){if(this.longClickData)return;const e=function(e){if(this.editKey(e)){const t=this.callback;this.longClickInterval=window.setTimeout(t.bind(null,e),Wi.TIME_MS)}}.bind(this),t=function(e){this.editKey(e)&&this.reset()}.bind(this),n=function(e){if(1!==e.which)return;const t=this.callback;this.longClickInterval=window.setTimeout(t.bind(null,e),Wi.TIME_MS)}.bind(this),i=function(e){if(1!==e.which)return;this.reset()}.bind(this),s=this.reset.bind(this);this.element.addEventListener("keydown",e,!1),this.element.addEventListener("keyup",t,!1),this.element.addEventListener("pointerdown",n,!1),this.element.addEventListener("pointerout",s,!1),this.element.addEventListener("pointerup",i,!1),this.element.addEventListener("click",s,!0),this.longClickData={mouseUp:i,mouseDown:n,reset:s}}dispose(){this.longClickData&&(this.element.removeEventListener("pointerdown",this.longClickData.mouseDown,!1),this.element.removeEventListener("pointerout",this.longClickData.reset,!1),this.element.removeEventListener("pointerup",this.longClickData.mouseUp,!1),this.element.addEventListener("click",this.longClickData.reset,!0),delete this.longClickData)}static TIME_MS=200}function Ni(e){return e||pi(hi.anonymous)}const ji=(e,t)=>{const n=e.ownerDocument.createTextNode(t);return e.appendChild(n),n};function Vi(e,t,n){const i=new a.Button.Button;return n?.className&&(i.className=n.className),i.textContent=e,i.variant=n?.variant?n.variant:"outlined",t&&i.addEventListener("click",t),n?.jslogContext&&i.setAttribute("jslog",`${s.action().track({click:!0}).context(n.jslogContext)}`),n?.title&&i.setAttribute("title",n.title),i.type="button",i}function Ui(e,t,n){const i=document.createElement("input");return e&&(i.className=e),i.spellcheck=!1,i.classList.add("harmony-input"),t&&(i.type=t),n&&i.setAttribute("jslog",`${s.textField().track({keydown:"Enter",change:!0}).context(n)}`),i}function _i(e,t,n){const i=new Option(e,t||e);return n&&i.setAttribute("jslog",`${s.item(n).track({click:!0})}`),i}class Ki extends HTMLSpanElement{shadowRootInternal;checkboxElement;textElement;constructor(){super(),Ki.lastId=Ki.lastId+1;const e="ui-checkbox-label"+Ki.lastId;this.shadowRootInternal=ds(this,{cssFile:A,delegatesFocus:void 0}),this.checkboxElement=this.shadowRootInternal.createChild("input"),this.checkboxElement.type="checkbox",this.checkboxElement.setAttribute("id",e),this.textElement=this.shadowRootInternal.createChild("label","dt-checkbox-text"),this.textElement.setAttribute("for",e),this.shadowRootInternal.createChild("slot")}static create(e,t,n,i,r){Ki.constructorInternal||(Ki.constructorInternal=ps("span","dt-checkbox",Ki));const o=Ki.constructorInternal();return o.checkboxElement.checked=Boolean(t),i&&o.checkboxElement.setAttribute("jslog",`${s.toggle().track({change:!0}).context(i)}`),void 0!==e&&(o.textElement.textContent=e,o.checkboxElement.title=e,void 0!==n&&(o.textElement.createChild("div","dt-checkbox-subtitle").textContent=n)),o.checkboxElement.classList.toggle("small",r),o}static lastId=0;static constructorInternal=null}class qi extends HTMLSpanElement{#i;constructor(){super();const e=ds(this,{cssFile:void 0,delegatesFocus:void 0});this.#i=new l.Icon.Icon,this.#i.style.setProperty("margin-right","4px"),this.#i.style.setProperty("vertical-align","baseline"),e.appendChild(this.#i),e.createChild("slot")}set data(e){this.#i.data=e,"14px"===e.height?this.#i.style.setProperty("margin-bottom","-2px"):"20px"===e.height&&this.#i.style.setProperty("margin-bottom","2px")}}let Gi=0;class $i extends HTMLSpanElement{radioElement;labelElement;constructor(){super(),this.radioElement=this.createChild("input","dt-radio-button"),this.labelElement=this.createChild("label");const e="dt-radio-button-id"+ ++Gi;this.radioElement.id=e,this.radioElement.type="radio",this.labelElement.htmlFor=e;ds(this,{cssFile:ye,delegatesFocus:void 0}).createChild("slot"),this.addEventListener("click",this.radioClickHandler.bind(this),!1)}radioClickHandler(){this.radioElement.checked||this.radioElement.disabled||(this.radioElement.checked=!0,this.radioElement.dispatchEvent(new Event("change")))}}ps("span","dt-radio",$i),ps("span","dt-icon-label",qi);class Xi extends HTMLSpanElement{sliderElement;constructor(){super();const e=ds(this,{cssFile:we,delegatesFocus:void 0});this.sliderElement=document.createElement("input"),this.sliderElement.classList.add("dt-range-input"),this.sliderElement.type="range",e.appendChild(this.sliderElement)}set value(e){this.sliderElement.value=String(e)}get value(){return Number(this.sliderElement.value)}}ps("span","dt-slider",Xi);class Yi extends HTMLSpanElement{textElement;constructor(){super();const e=ds(this,{cssFile:xe,delegatesFocus:void 0});this.textElement=e.createChild("div"),this.textElement.className="info",this.textElement.createChild("slot")}set type(e){this.textElement.className=e}}ps("span","dt-small-bubble",Yi);class Qi extends HTMLDivElement{button;constructor(){super();const e=ds(this,{delegatesFocus:void 0});this.button=new a.Button.Button,this.button.data={variant:"icon",iconName:"cross"},this.button.classList.add("close-button"),this.button.setAttribute("jslog",`${s.close().track({click:!0})}`),wt.install(this.button,pi(hi.close)),or(this.button,pi(hi.close)),e.appendChild(this.button)}setAccessibleName(e){or(this.button,e)}setTabbable(e){this.button.tabIndex=e?0:-1}}function Zi(e,t,n,i){if(n<=10)return"";t.length>200&&(t=i(t,200));const s=Ji(e,t);if(s<=n)return t;let r=0,o=t.length,a=0,l=s;for(;r<o&&a!==l&&a!==n;){const s=Math.ceil(r+(o-r)*(n-a)/(l-a)),c=Ji(e,i(t,s));c<=n?(r=s,a=c):(o=s-1,l=c)}return"…"!==(t=i(t,r))?t:""}function Ji(e,t){if(t.length>200)return e.measureText(t).width;es||(es=new Map);const n=e.font;let i=es.get(n);i||(i=new Map,es.set(n,i));let s=i.get(t);return s||(s=e.measureText(t).width,i.set(t,s)),s}ps("div","dt-close-button",Qi);let es=null;function ts(e){return/(\?|&)utm_source=devtools/.test(e)?e:-1===e.indexOf("?")?e.replace(/^([^#]*)(#.*)?$/g,"$1?utm_source=devtools$2"):e.replace(/^([^#]*)(#.*)?$/g,"$1&utm_source=devtools$2")}function ns(e){return/(\/\/developers.google.com\/|\/\/web.dev\/|\/\/developer.chrome.com\/)/.test(e)?ts(e):e}const is=(e,t)=>{let n=e;for(;n&&n!==e.ownerDocument;n=n.parentNodeOrShadowHost())for(let e=0;e<t.length;++e)if(n.nodeName.toLowerCase()===t[e].toLowerCase())return n;return null},ss=(e,t,n)=>{let i=e,s=null;for(;i;){const e=i.elementFromPoint(t,n);if(!e||s===e)break;s=e,i=s.shadowRoot}return s},rs=e=>{const t=e;if(!(t.which||t.pageX||t.pageY||t.clientX||t.clientY||t.movementX||t.movementY))return null;const n=t.target&&t.target.getComponentRoot();return n?ss(n,t.pageX,t.pageY):null},os=[];function as(e){return os.filter((function(t){if(!t.contextTypes)return!0;for(const n of t.contextTypes())if(e instanceof n)return!0;return!1}))}function ls(e){const t=e.target,i=t?t.ownerDocument:null,s=i?n.DOMUtilities.deepActiveElement(i):null;!function(e){for(;e&&!ot.get(e);)e=e.parentNodeOrShadowHost();if(!e)return;let t=ot.get(e);for(;t&&t.parentWidget();){const e=t.parentWidget();if(!e)break;e.defaultFocusedChild=t,t=e}}(s),function(e){e=e&&e.parentNodeOrShadowHost();const t=customElements.get("x-widget");let n=null;for(;e;)t&&e instanceof t&&(n&&(e.defaultFocusedElement=n),n=e),e=e.parentNodeOrShadowHost()}(s)}function cs(e){c.ThemeSupport.instance().appendStyle(e,D),c.ThemeSupport.instance().appendStyle(e,B),c.ThemeSupport.instance().appendStyle(e,$),c.ThemeSupport.instance().appendStyle(e,Ee),c.ThemeSupport.instance().appendStyle(e,Ie),c.ThemeSupport.instance().appendStyle(e,Se),c.ThemeSupport.instance().injectHighlightStyleSheets(e),c.ThemeSupport.instance().injectCustomStyleSheets(e)}function ds(e,t={delegatesFocus:void 0,cssFile:void 0}){const{cssFile:n,delegatesFocus:i}=t,s=e.attachShadow({mode:"open",delegatesFocus:i});return cs(s),n&&("cssContent"in n?c.ThemeSupport.instance().appendStyle(s,n):s.adoptedStyleSheets=n),s.addEventListener("focus",ls,!0),s}let hs;function us(e){if("number"==typeof hs)return hs;if(!e)return 16;const t=e.createElement("div"),n=e.createElement("div");return t.setAttribute("style","display: block; width: 100px; height: 100px; overflow: scroll;"),n.setAttribute("style","height: 200px"),t.appendChild(n),e.body.appendChild(t),hs=t.offsetWidth-t.clientWidth,e.body.removeChild(t),hs}function ps(e,t,n){return self.customElements.define(t,class extends n{constructor(){super(),this.setAttribute("is",t)}},{extends:e}),()=>document.createElement(e,{is:t})}var gs=Object.freeze({__proto__:null,highlightedSearchResultClassName:gi,highlightedCurrentSearchResultClassName:"current-search-result",installDragHandle:mi,elementDragStart:bi,isBeingEdited:function(e){if(!e||e.nodeType!==Node.ELEMENT_NODE)return!1;const t=e;if(t.classList.contains("text-prompt")||"INPUT"===t.nodeName||"TEXTAREA"===t.nodeName)return!0;if(!wi.size)return!1;let n=t;for(;n;){if(wi.has(t))return!0;n=n.parentElementOrShadowHost()}return!1},isEditing:vi,markBeingEdited:yi,StyleValueDelimiters:Ei,getValueModificationDirection:Ii,modifiedFloatNumber:Si,createReplacementString:ki,isElementValueModification:Ci,handleElementValueModifications:function(e,t,i,r,o){if(!Ci(e))return!1;s.logKeyDown(e.currentTarget,e,"element-value-modification");const a=t.getComponentSelection();if(!a||!a.rangeCount)return!1;const l=a.getRangeAt(0);if(!l.commonAncestorContainer.isSelfOrDescendant(t))return!1;const c=t.textContent,d=n.DOMUtilities.rangeOfWord(l.startContainer,l.startOffset,Ei,t),h=d.toString();if(r&&r(h))return!1;const u=ki(h,e,o);if(u){const t=document.createTextNode(u);d.deleteContents(),d.insertNode(t);const n=document.createRange();return n.setStart(t,0),n.setEnd(t,u.length),a.removeAllRanges(),a.addRange(n),e.handled=!0,e.preventDefault(),i&&i(c||"",u),!0}return!1},openLinkExternallyLabel:Ti,copyLinkAddressLabel:Li,copyFileNameLabel:function(){return pi(hi.copyFileName)},anotherProfilerActiveLabel:function(){return pi(hi.anotherProfilerIsAlreadyActive)},asyncStackTraceLabel:function(e,t){if(e){if("Promise.resolve"===e)return pi(hi.promiseResolvedAsync);if("Promise.reject"===e)return pi(hi.promiseRejectedAsync);if("await"===e&&0!==t.length){e=`await in ${Ni(t[t.length-1].functionName)}`}return e}return pi(hi.asyncCall)},installComponentRootStyles:function(e){cs(e),e.classList.add("platform-"+r.Platform.platform()),r.Platform.isMac()||0!==us(e.ownerDocument)||e.classList.add("overlay-scrollbar-enabled")},ElementFocusRestorer:Di,highlightSearchResult:function(e,t,n,i){const s=Ai(e,[new o.TextRange.SourceRange(t,n)],i);return s.length?s[0]:null},highlightSearchResults:Ai,runCSSAnimationOnce:function(e,t){e.classList.contains(t)&&e.classList.remove(t),e.addEventListener("webkitAnimationEnd",(function n(){e.classList.remove(t),e.removeEventListener("webkitAnimationEnd",n,!1)}),!1),e.classList.add(t)},highlightRangesWithStyleClass:Ri,applyDomChanges:function(e){for(let t=0,n=e.length;t<n;++t){const n=e[t];switch(n.type){case"added":n.parent?.insertBefore(n.node,n.nextSibling??null);break;case"changed":n.node.textContent=n.newText??null}}},revertDomChanges:function(e){for(let t=e.length-1;t>=0;--t){const n=e[t];switch(n.type){case"added":n.node.remove();break;case"changed":n.node.textContent=n.oldText??null}}},measurePreferredSize:Bi,startBatchUpdate:function(){Oi++||(Fi=new zi(!1))},endBatchUpdate:function(){--Oi||Fi&&(Fi.scheduleInvoke(),Fi=null)},invokeOnceAfterBatchUpdate:Hi,animateFunction:function(e,t,i,s,r){const o=e.performance.now();let a=e.requestAnimationFrame((function l(c){const d=n.NumberUtilities.clamp((c-o)/s,0,1);t(...i.map((e=>e.from+(e.to-e.from)*d))),d<1?a=e.requestAnimationFrame(l):r&&r()}));return()=>e.cancelAnimationFrame(a)},LongClickController:Wi,initializeUIUtils:function(e){e.body.classList.toggle("inactive",!e.hasFocus()),e.defaultView&&(e.defaultView.addEventListener("focus",Mi.bind(void 0,e),!1),e.defaultView.addEventListener("blur",Pi.bind(void 0,e),!1)),e.addEventListener("focus",ls.bind(void 0),!0);const t=e.body;ms.setContainer(t)},beautifyFunctionName:Ni,createTextChild:ji,createTextChildren:(e,...t)=>{for(const n of t)ji(e,n)},createTextButton:Vi,createInput:Ui,createSelect:function(e,t){const i=document.createElement("select");i.classList.add("chrome-select"),or(i,e);for(const e of t)if(e instanceof Map)for(const[t,s]of e){const e=i.createChild("optgroup");e.label=t;for(const t of s)"string"==typeof t&&e.appendChild(_i(t,t,n.StringUtilities.toKebabCase(t)))}else"string"==typeof e&&i.add(_i(e,e,n.StringUtilities.toKebabCase(e)));return i},createOption:_i,createLabel:function(e,t,n){const i=document.createElement("label");return t&&(i.className=t),i.textContent=e,n&&Ss(i,n),i},createRadioLabel:function(e,t,n,i){const r=document.createElement("span",{is:"dt-radio"});return r.radioElement.name=e,r.radioElement.checked=Boolean(n),ji(r.labelElement,t),i&&r.radioElement.setAttribute("jslog",`${s.toggle().track({change:!0}).context(i)}`),r},createIconLabel:function(e){const t=document.createElement("span",{is:"dt-icon-label"});return e.title&&(t.createChild("span").textContent=e.title),t.data={iconName:e.iconName,color:e.color??"var(--icon-default)",width:e.width??"14px",height:e.height??"14px"},t},createSlider:function(e,t,n){const i=document.createElement("span",{is:"dt-slider"});return i.sliderElement.min=String(e),i.sliderElement.max=String(t),i.sliderElement.step=String(1),i.sliderElement.tabIndex=n,i},setTitle:function(e,t){or(e,t),wt.install(e,t)},CheckboxLabel:Ki,DevToolsIconLabel:qi,DevToolsRadioButton:$i,DevToolsSlider:Xi,DevToolsSmallBubble:Yi,DevToolsCloseButton:Qi,bindInput:function(e,t,n,i,s){function r(t){if(t===e.value)return;const{valid:i}=n(t);e.classList.toggle("error-input",!i),e.value=t}return e.addEventListener("change",(function(){const{valid:i}=n(e.value);e.classList.toggle("error-input",!i),i&&t(e.value)}),!1),e.addEventListener("input",(function(){e.classList.toggle("error-input",!n(e.value))}),!1),e.addEventListener("keydown",(function(o){if("Enter"===o.key){const{valid:i}=n(e.value);return i&&t(e.value),void o.preventDefault()}if(!i)return;const a=Si(parseFloat(e.value),o,s);if(null===a)return;const l=String(a),{valid:c}=n(l);c&&r(l);o.preventDefault()}),!1),e.addEventListener("focus",e.select.bind(e),!1),r},trimText:Zi,trimTextMiddle:function(e,t,i){return Zi(e,t,i,((e,t)=>n.StringUtilities.trimMiddle(e,t)))},trimTextEnd:function(e,t,i){return Zi(e,t,i,((e,t)=>n.StringUtilities.trimEndWithMaxLength(e,t)))},measureTextWidth:Ji,addReferrerToURL:ts,addReferrerToURLIfNecessary:ns,loadImage:function(e){return new Promise((t=>{const n=new Image;n.addEventListener("load",(()=>t(n))),n.addEventListener("error",(()=>t(null))),n.src=e}))},createFileSelectorElement:function(e,t){const n=document.createElement("input");return n.type="file",t&&n.setAttribute("accept",t),n.style.display="none",n.tabIndex=-1,n.onchange=()=>{n.files&&e(n.files[0])},n},MaxLengthForDisplayedURLs:150,MessageDialog:class{static async show(e,t,n){const i=new ws(n);i.setSizeBehavior("MeasureContent"),i.setDimmed(!0);const s=ds(i.contentElement,{cssFile:R,delegatesFocus:void 0}).createChild("div","widget");await new Promise((n=>{const r=Vi(pi(hi.ok),n,{jslogContext:"confirm",variant:"primary"});s.createChild("div","message").createChild("span").textContent=e,s.createChild("div","button").appendChild(r),i.setOutsideClickCallback((e=>{e.consume(),n(void 0)})),i.show(t),r.focus()})),i.hide()}},ConfirmDialog:class{static async show(e,t,n){const i=new ws(n?.jslogContext);i.setSizeBehavior("MeasureContent"),i.setDimmed(!0),or(i.contentElement,e);const s=ds(i.contentElement,{cssFile:R,delegatesFocus:void 0}).createChild("div","widget");s.createChild("div","message").createChild("span").textContent=e;const r=s.createChild("div","button"),o=await new Promise((e=>{const s=Vi(n?.okButtonLabel||pi(hi.ok),(()=>e(!0)),{jslogContext:"confirm",variant:"primary"});r.appendChild(s),r.appendChild(Vi(n?.cancelButtonLabel||pi(hi.cancel),(()=>e(!1)),{jslogContext:"cancel"})),i.setOutsideClickCallback((t=>{t.consume(),e(!1)})),i.show(t),s.focus()}));return i.hide(),o}},createInlineButton:function(e){const t=document.createElement("span"),n=ds(t,{cssFile:G,delegatesFocus:void 0});t.classList.add("inline-button");const i=new Xn("");return i.appendToolbarItem(e),n.appendChild(i.element),t},Renderer:class{static async render(e,t){if(!e)throw new Error("Can't render "+e);const n=as(e)[0];if(!n)return null;return(await n.loadRenderer()).render(e,t)}},formatTimestamp:function(e,t){const n=new Date(e),i=n.getFullYear()+"-"+r(n.getMonth()+1,2)+"-"+r(n.getDate(),2),s=r(n.getHours(),2)+":"+r(n.getMinutes(),2)+":"+r(n.getSeconds(),2)+"."+r(n.getMilliseconds(),3);return t?i+" "+s:s;function r(e,t){return String(e).padStart(t,"0")}},isScrolledToBottom:e=>Math.abs(e.scrollTop+e.clientHeight-e.scrollHeight)<=2,createSVGChild:function(e,t,n){const i=e.ownerDocument.createElementNS("http://www.w3.org/2000/svg",t);return n&&i.setAttribute("class",n),e.appendChild(i),i},enclosingNodeOrSelfWithNodeNameInArray:is,enclosingNodeOrSelfWithNodeName:function(e,t){return is(e,[t])},deepElementFromPoint:ss,deepElementFromEvent:rs,registerRenderer:function(e){os.push(e)},getApplicableRegisteredRenderers:as,injectCoreStyles:cs,createShadowRootWithCoreStyles:ds,resetMeasuredScrollbarWidthForTest:function(){hs=void 0},measuredScrollbarWidth:us,registerCustomElement:ps});class ms{widgetInternal;element;contentElement;arrowElement;onMouseDownBound;onClickOutsideCallback;maxSize;positionX;positionY;anchorBox;anchorBehavior;sizeBehavior;marginBehavior;#s=!1;constructor(e){this.widgetInternal=new ot(!0),this.widgetInternal.markAsRoot(),this.element=this.widgetInternal.element,this.contentElement=this.widgetInternal.contentElement,e&&this.contentElement.setAttribute("jslog",e),this.arrowElement=document.createElement("span"),this.arrowElement.classList.add("arrow","hidden"),this.element.shadowRoot&&this.element.shadowRoot.appendChild(this.arrowElement),this.registerRequiredCSS(P),this.setPointerEventsBehavior("PierceGlassPane"),this.onMouseDownBound=this.onMouseDown.bind(this),this.onClickOutsideCallback=null,this.maxSize=null,this.positionX=null,this.positionY=null,this.anchorBox=null,this.anchorBehavior="PreferTop",this.sizeBehavior="SetExactSize",this.marginBehavior="DefaultMargin"}setJsLog(e){this.contentElement.setAttribute("jslog",e)}isShowing(){return this.widgetInternal.isShowing()}registerRequiredCSS(e){this.widgetInternal.registerRequiredCSS(e)}registerCSSFiles(e){this.widgetInternal.registerCSSFiles(e)}setDefaultFocusedElement(e){this.widgetInternal.setDefaultFocusedElement(e)}setDimmed(e){this.element.classList.toggle("dimmed-pane",e)}setPointerEventsBehavior(e){this.element.classList.toggle("no-pointer-events","BlockedByGlassPane"!==e),this.contentElement.classList.toggle("no-pointer-events","PierceContents"===e)}setOutsideClickCallback(e){this.onClickOutsideCallback=e}setMaxContentSize(e){this.maxSize=e,this.positionContent()}setSizeBehavior(e){this.sizeBehavior=e,this.positionContent()}setContentPosition(e,t){this.positionX=e,this.positionY=t,this.positionContent()}setContentAnchorBox(e){this.anchorBox=e,this.positionContent()}setAnchorBehavior(e){this.anchorBehavior=e}setMarginBehavior(e){this.marginBehavior=e,this.arrowElement.classList.toggle("hidden","Arrow"!==e)}setIgnoreLeftMargin(e){this.#s=e}show(e){this.isShowing()||(this.element.style.zIndex=""+(3e3+1e3*fs.size),this.element.setAttribute("data-devtools-glass-pane",""),e.body.addEventListener("mousedown",this.onMouseDownBound,!0),e.body.addEventListener("pointerdown",this.onMouseDownBound,!0),this.widgetInternal.show(e.body),fs.add(this),this.positionContent())}hide(){this.isShowing()&&(fs.delete(this),this.element.ownerDocument.body.removeEventListener("mousedown",this.onMouseDownBound,!0),this.element.ownerDocument.body.removeEventListener("pointerdown",this.onMouseDownBound,!0),this.widgetInternal.detach())}onMouseDown(e){if(!this.onClickOutsideCallback)return;const t=rs(e);t&&!this.contentElement.isSelfOrAncestor(t)&&this.onClickOutsideCallback.call(null,e)}positionContent(){if(!this.isShowing())return;const e="Arrow"===this.marginBehavior,t=e?8:"NoMargin"===this.marginBehavior?0:3,i=us(this.element.ownerDocument),s=10,r=bs.get(this.element.ownerDocument);"MeasureContent"===this.sizeBehavior&&(this.contentElement.positionAt(0,0),this.contentElement.style.width="",this.contentElement.style.maxWidth="",this.contentElement.style.height="",this.contentElement.style.maxHeight="");const o=r.offsetWidth,a=r.offsetHeight;let l=o-2*t,c=a-2*t,d=t,h=t;if(this.maxSize&&(l=Math.min(l,this.maxSize.width),c=Math.min(c,this.maxSize.height)),"MeasureContent"===this.sizeBehavior){const e=this.contentElement.getBoundingClientRect(),t=c<e.height?i:0,n=l<e.width?i:0;l=Math.min(l,e.width+t),c=Math.min(c,e.height+n)}if(this.anchorBox){const i=this.anchorBox.relativeToElement(r);let u=this.anchorBehavior;if(this.arrowElement.classList.remove("arrow-none","arrow-top","arrow-bottom","arrow-left","arrow-right"),"PreferTop"===u||"PreferBottom"===u){const p=i.y-2*t,g=a-i.y-i.height-2*t;let m;"PreferTop"===u&&p<c&&g>p&&(u="PreferBottom"),"PreferBottom"===u&&g<c&&p>g&&(u="PreferTop");let b=!0;if("PreferTop"===u){h=Math.max(t,i.y-c-t);const e=i.y-h-t;"MeasureContent"===this.sizeBehavior?c>e&&(this.arrowElement.classList.add("arrow-none"),b=!1):c=Math.min(c,e),this.arrowElement.classList.add("arrow-bottom"),m=i.y-t}else{h=i.y+i.height+t;const e=a-h-t;"MeasureContent"===this.sizeBehavior?c>e&&(this.arrowElement.classList.add("arrow-none"),h=a-t-c,b=!1):c=Math.min(c,e),this.arrowElement.classList.add("arrow-top"),m=i.y+i.height+t}const f=Math.min(i.x,o-l-t);if(d=Math.max(t,f),this.#s&&t>f&&(d=0),b?e&&d-s>=t&&(d-=s):d=Math.min(d+s,o-l-t),l=Math.min(l,o-d-t),20>=l)this.arrowElement.classList.add("arrow-none");else{let e=i.x+Math.min(50,Math.floor(i.width/2));e=n.NumberUtilities.clamp(e,d+s,d+l-s),this.arrowElement.positionAt(e,m,r)}}else{const p=i.x-2*t,g=o-i.x-i.width-2*t;let m;"PreferLeft"===u&&p<l&&g>p&&(u="PreferRight"),"PreferRight"===u&&g<l&&p>g&&(u="PreferLeft");let b=!0;if("PreferLeft"===u){d=Math.max(t,i.x-l-t);const e=i.x-d-t;"MeasureContent"===this.sizeBehavior?l>e&&(this.arrowElement.classList.add("arrow-none"),b=!1):l=Math.min(l,e),this.arrowElement.classList.add("arrow-right"),m=i.x-t}else{d=i.x+i.width+t;const e=o-d-t;"MeasureContent"===this.sizeBehavior?l>e&&(this.arrowElement.classList.add("arrow-none"),d=o-t-l,b=!1):l=Math.min(l,e),this.arrowElement.classList.add("arrow-left"),m=i.x+i.width+t}if(h=Math.max(t,Math.min(i.y,a-c-t)),b?e&&h-s>=t&&(h-=s):h=Math.min(h+s,a-c-t),c=Math.min(c,a-h-t),20>=c)this.arrowElement.classList.add("arrow-none");else{let e=i.y+Math.min(50,Math.floor(i.height/2));e=n.NumberUtilities.clamp(e,h+s,h+c-s),this.arrowElement.positionAt(m,e,r)}}}else d=null!==this.positionX?this.positionX:(o-l)/2,h=null!==this.positionY?this.positionY:(a-c)/2,l=Math.min(l,o-d-t),c=Math.min(c,a-h-t),this.arrowElement.classList.add("arrow-none");this.contentElement.style.width=l+"px","SetExactWidthMaxHeight"===this.sizeBehavior?this.contentElement.style.maxHeight=c+"px":this.contentElement.style.height=c+"px",this.contentElement.positionAt(d,h,r),this.widgetInternal.doResize()}widget(){return this.widgetInternal}static setContainer(e){bs.set(e.ownerDocument,e),ms.containerMoved(e)}static container(e){return bs.get(e)}static containerMoved(e){for(const t of fs)t.isShowing()&&t.element.ownerDocument===e.ownerDocument&&t.positionContent()}}const bs=new Map,fs=new Set,vs=fs;var ys=Object.freeze({__proto__:null,GlassPane:ms,GlassPanePanes:vs});class ws extends(e.ObjectWrapper.eventMixin(ms)){tabIndexBehavior;tabIndexMap;focusRestorer;closeOnEscape;targetDocument;targetDocumentKeyDownHandler;escapeKeyCallback;constructor(e){super(),this.registerRequiredCSS(M),this.contentElement.tabIndex=0,this.contentElement.addEventListener("focus",(()=>this.widget().focus()),!1),e&&this.contentElement.setAttribute("jslog",`${s.dialog(e).track({resize:!0,keydown:"Escape"})}`),this.widget().setDefaultFocusedElement(this.contentElement),this.setPointerEventsBehavior("BlockedByGlassPane"),this.setOutsideClickCallback((e=>{this.hide(),e.consume(!0)})),Ts(this.contentElement),this.tabIndexBehavior="DisableAllTabIndex",this.tabIndexMap=new Map,this.focusRestorer=null,this.closeOnEscape=!0,this.targetDocumentKeyDownHandler=this.onKeyDown.bind(this),this.escapeKeyCallback=null}static hasInstance(){return Boolean(ws.instance)}static getInstance(){return ws.instance}show(e){const t=e instanceof Document?e:(e||an.instance().element).ownerDocument;this.targetDocument=t,this.targetDocument.addEventListener("keydown",this.targetDocumentKeyDownHandler,!0),ws.instance&&ws.instance.hide(),ws.instance=this,this.disableTabIndexOnElements(t),super.show(t),this.focusRestorer=new dt(this.widget())}hide(){this.focusRestorer&&this.focusRestorer.restore(),super.hide(),this.targetDocument&&this.targetDocument.removeEventListener("keydown",this.targetDocumentKeyDownHandler,!0),this.restoreTabIndexOnElements(),this.dispatchEventToListeners("hidden"),ws.instance=null}setCloseOnEscape(e){this.closeOnEscape=e}setEscapeKeyCallback(e){this.escapeKeyCallback=e}addCloseButton(){this.contentElement.createChild("div","dialog-close-button","dt-close-button").addEventListener("click",(()=>this.hide()),!1)}setOutsideTabIndexBehavior(e){this.tabIndexBehavior=e}disableTabIndexOnElements(e){if("PreserveTabIndex"===this.tabIndexBehavior)return;let t=null;"PreserveMainViewTabIndex"===this.tabIndexBehavior&&(t=this.getMainWidgetTabIndexElements(an.instance().ownerSplit())),this.tabIndexMap.clear();let n=e;for(;n;n=n.traverseNextNode(e))if(n instanceof HTMLElement){const e=n,i=e.tabIndex;t?.has(e)||(i>=0?(this.tabIndexMap.set(e,i),e.tabIndex=-1):e.hasAttribute("contenteditable")&&(this.tabIndexMap.set(e,e.hasAttribute("tabindex")?i:0),e.tabIndex=-1))}}getMainWidgetTabIndexElements(e){const t=new Set;if(!e)return t;const n=e.mainWidget();if(!n||!n.element)return t;let i=n.element;for(;i;i=i.traverseNextNode(n.element)){if(!(i instanceof HTMLElement))continue;const e=i;e.tabIndex<0||t.add(e)}return t}restoreTabIndexOnElements(){for(const e of this.tabIndexMap.keys())e.tabIndex=this.tabIndexMap.get(e);this.tabIndexMap.clear()}onKeyDown(e){if(e.keyCode===be.Esc.code&&ie.hasNoModifiers(e)){if(this.escapeKeyCallback&&this.escapeKeyCallback(e),e.handled)return;this.closeOnEscape&&(e.consume(!0),this.hide())}}static instance=null}var xs=Object.freeze({__proto__:null,Dialog:ws});let Es=0;function Is(e){return(e||"")+ ++Es}function Ss(e,t){const n=Is("labelledControl");t.id=n,e.setAttribute("for",n)}function ks(e){e.setAttribute("role","alert"),e.setAttribute("aria-live","polite")}function Cs(e){e.setAttribute("role","button")}function Ts(e){e.setAttribute("role","dialog"),e.setAttribute("aria-modal","true")}function Ls(e){e.setAttribute("role","group")}function Ms(e){e.setAttribute("role","link")}function Ps(e){Cs(e),e.setAttribute("aria-haspopup","true")}function Ds(e){e.setAttribute("role","tab")}function As(e){e.setAttribute("role","tabpanel")}function Rs(e){e.setAttribute("role","tree")}function Bs(e){e.setAttribute("role","treeitem")}function zs(e){e.setAttribute("role","textbox")}function Os(e){e.setAttribute("role","menu")}function Fs(e){e.setAttribute("role","menuitem")}function Hs(e){e.setAttribute("role","menuitemcheckbox")}function Ws(e){Fs(e),e.setAttribute("aria-haspopup","true")}function Ns(e){e.setAttribute("role","complementary")}function js(e){e.setAttribute("role","navigation")}function Vs(e){e.setAttribute("role","listbox")}function Us(e){e.setAttribute("aria-multiselectable","true")}function _s(e){e.setAttribute("role","option")}function Ks(e){e.setAttribute("aria-hidden","true")}function qs(e,t){e.setAttribute("role","heading"),e.setAttribute("aria-level",t.toString())}function Gs(e){return e.hasAttribute("role")}function $s(e,t){t?e.setAttribute("aria-placeholder",t):e.removeAttribute("aria-placeholder")}function Xs(e){e.id||(e.id=Is("ariaElement"))}function Ys(e,t){t?(Xs(t),e.setAttribute("aria-controls",t.id)):e.removeAttribute("aria-controls")}function Qs(e,t){e.setAttribute("aria-expanded",Boolean(t).toString())}function Zs(e){e.removeAttribute("aria-expanded")}function Js(e,t="none"){e.setAttribute("aria-autocomplete",t)}function er(e){e.removeAttribute("aria-autocomplete")}function tr(e,t="false"){"false"!==t?e.setAttribute("aria-haspopup",t):e.removeAttribute("aria-haspopup")}function nr(e,t){e.setAttribute("aria-selected",Boolean(t).toString())}function ir(e){e.removeAttribute("aria-selected")}function sr(e,t){t?e.setAttribute("aria-invalid",t.toString()):e.removeAttribute("aria-invalid")}function rr(e,t){e.setAttribute("aria-pressed",Boolean(t).toString())}function or(e,t){e.setAttribute("aria-label",t)}function ar(e,t){e.setAttribute("aria-description",t)}function lr(e,t){t?(t.isConnected&&e.isConnected&&console.assert(n.DOMUtilities.getEnclosingShadowRootForNode(t)===n.DOMUtilities.getEnclosingShadowRootForNode(e),"elements are not in the same shadow dom"),Xs(t),e.setAttribute("aria-activedescendant",t.id)):e.removeAttribute("aria-activedescendant")}function cr(e,t){e.setAttribute("aria-setsize",t.toString())}function dr(e,t){e.setAttribute("aria-posinset",t.toString())}const hr=new WeakMap;function ur(e){const t=e.createChild("div");return function(e){e.style.position="absolute",e.style.left="-999em",e.style.width="100em",e.style.overflow="hidden"}(t),t.setAttribute("role","alert"),t.setAttribute("aria-atomic","true"),t}function pr(e=document.body){let t=hr.get(e);return t||(t={one:ur(e),two:ur(e),alertToggle:!1},hr.set(e,t)),t}function gr(e=document.body){const t=pr(e);return t.alertToggle=!t.alertToggle,t.alertToggle?(t.two.textContent="",t.one):(t.one.textContent="",t.two)}function mr(e){const t=ws.getInstance();gr(t&&t.isShowing()?t.contentElement:void 0).textContent=n.StringUtilities.trimEndWithMaxLength(e,1e4)}var br=Object.freeze({__proto__:null,nextId:Is,bindLabelToControl:Ss,markAsAlert:ks,markAsApplication:function(e){e.setAttribute("role","application")},markAsButton:Cs,markAsCheckbox:function(e){e.setAttribute("role","checkbox")},markAsCombobox:function(e){e.setAttribute("role","combobox")},markAsModalDialog:Ts,markAsGroup:Ls,markAsLink:Ms,markAsMenuButton:Ps,markAsProgressBar:function(e,t=0,n=100){e.setAttribute("role","progressbar"),e.setAttribute("aria-valuemin",t.toString()),e.setAttribute("aria-valuemax",n.toString())},markAsTab:Ds,markAsTablist:function(e){e.setAttribute("role","tablist")},markAsTabpanel:As,markAsTree:Rs,markAsTreeitem:Bs,markAsTextBox:zs,markAsMenu:Os,markAsMenuItem:Fs,markAsMenuItemCheckBox:Hs,markAsMenuItemSubMenu:Ws,markAsList:function(e){e.setAttribute("role","list")},markAsListitem:function(e){e.setAttribute("role","listitem")},markAsMain:function(e){e.setAttribute("role","main")},markAsComplementary:Ns,markAsNavigation:js,markAsListBox:Vs,markAsMultiSelectable:Us,markAsOption:_s,markAsRadioGroup:function(e){e.setAttribute("role","radiogroup")},markAsHidden:Ks,markAsSlider:function(e,t=0,n=100){e.setAttribute("role","slider"),e.setAttribute("aria-valuemin",String(t)),e.setAttribute("aria-valuemax",String(n))},markAsHeading:qs,markAsPoliteLiveRegion:function(e,t){e.setAttribute("aria-live","polite"),t&&e.setAttribute("aria-atomic","true")},markAsLog:function(e){e.setAttribute("role","log")},hasRole:Gs,removeRole:function(e){e.removeAttribute("role")},setPlaceholder:$s,markAsPresentation:function(e){e.setAttribute("role","presentation")},markAsStatus:function(e){e.setAttribute("role","status")},ensureId:Xs,setAriaValueText:function(e,t){e.setAttribute("aria-valuetext",t)},setAriaValueNow:function(e,t){e.setAttribute("aria-valuenow",t)},setAriaValueMinMax:function(e,t,n){e.setAttribute("aria-valuemin",t),e.setAttribute("aria-valuemax",n)},setControls:Ys,setChecked:function(e,t){e.setAttribute("aria-checked",Boolean(t).toString())},setCheckboxAsIndeterminate:function(e){e.setAttribute("aria-checked","mixed")},setDisabled:function(e,t){e.setAttribute("aria-disabled",Boolean(t).toString())},setExpanded:Qs,unsetExpandable:Zs,setHidden:function(e,t){e.setAttribute("aria-hidden",Boolean(t).toString())},setLevel:function(e,t){e.setAttribute("aria-level",t.toString())},setAutocomplete:Js,clearAutocomplete:er,setHasPopup:tr,setSelected:nr,clearSelected:ir,setInvalid:sr,setPressed:rr,setValueNow:function(e,t){e.setAttribute("aria-valuenow",t.toString())},setValueText:function(e,t){e.setAttribute("aria-valuetext",t.toString())},setProgressBarValue:function(e,t,n){e.setAttribute("aria-valuenow",t.toString()),n&&e.setAttribute("aria-valuetext",n)},setLabel:or,setDescription:ar,setActiveDescendant:lr,setSetSize:cr,setPositionInSet:dr,getOrCreateAlertElements:pr,alertElementInstance:gr,alert:mr}),fr=Object.freeze({__proto__:null}),vr={cssContent:":host{position:absolute;top:0;bottom:0;left:0;right:0;display:flex;background-color:var(--color-background-opacity-80);z-index:1000}.drop-target-message{flex:auto;font-size:30px;color:var(--sys-color-token-subtle);display:flex;justify-content:center;align-items:center;margin:20px;border:4px dashed var(--sys-color-neutral-outline);pointer-events:none}\n/*# sourceURL=dropTarget.css */\n"};var yr=Object.freeze({__proto__:null,DropTarget:class{element;transferTypes;messageText;handleDrop;enabled;dragMaskElement;constructor(e,t,n,i){e.addEventListener("dragenter",this.onDragEnter.bind(this),!0),e.addEventListener("dragover",this.onDragOver.bind(this),!0),this.element=e,this.transferTypes=t,this.messageText=n,this.handleDrop=i,this.enabled=!0,this.dragMaskElement=null}setEnabled(e){this.enabled=e}onDragEnter(e){this.enabled&&this.hasMatchingType(e)&&e.consume(!0)}hasMatchingType(e){const t=e;if(!t.dataTransfer)return!1;for(const e of this.transferTypes){if(Array.from(t.dataTransfer.items).find((t=>e.kind===t.kind&&Boolean(e.type.exec(t.type)))))return!0}return!1}onDragOver(e){const t=e;if(!this.enabled||!this.hasMatchingType(t))return;if(t.dataTransfer&&(t.dataTransfer.dropEffect="copy"),t.consume(!0),this.dragMaskElement)return;this.dragMaskElement=this.element.createChild("div","");ds(this.dragMaskElement,{cssFile:vr,delegatesFocus:void 0}).createChild("div","drop-target-message").textContent=this.messageText,this.dragMaskElement.addEventListener("drop",this.onDrop.bind(this),!0),this.dragMaskElement.addEventListener("dragleave",this.onDragLeave.bind(this),!0)}onDrop(e){const t=e;t.consume(!0),this.removeMask(),this.enabled&&t.dataTransfer&&this.handleDrop(t.dataTransfer)}onDragLeave(e){e.consume(!0),this.removeMask()}removeMask(){this.dragMaskElement&&(this.dragMaskElement.remove(),this.dragMaskElement=null)}},Type:{URI:{kind:"string",type:/text\/uri-list/},Folder:{kind:"file",type:/$^/},File:{kind:"file",type:/.*/},WebFile:{kind:"file",type:/[\w]+/},ImageFile:{kind:"file",type:/image\/.*/}}}),wr={cssContent:".empty-bold-text{display:block;font-size:1.5em;margin:0.83em 0;font-weight:bold}.empty-view{color:var(--sys-color-token-subtle);padding:30px;text-align:center;min-width:70px}.empty-view-scroller{justify-content:center;overflow:auto}.empty-view p{white-space:initial;line-height:18px;max-width:300px;flex-shrink:0}\n/*# sourceURL=emptyWidget.css */\n"};function xr(e){return e.data}function Er(e,t){e.data=t}class Ir{elementInternal;elementsById;constructor(e){this.elementInternal=e,this.elementsById=new Map}element(){return this.elementInternal}$(e){return this.elementsById.get(e)}static build(e,...t){return Ir.render(Ir.template(e),t)}static cached(e,...t){let n=Mr.get(e);return n||(n=Ir.template(e),Mr.set(e,n)),Ir.render(n,t)}static template(e){let t="",n=!0;for(let i=0;i<e.length-1;i++){t+=e[i];const s=e[i].lastIndexOf(">"),r=e[i].indexOf("<",s+1);-1!==s&&-1===r?n=!0:-1!==r&&(n=!1),t+=n?Sr:Cr(i)}t+=e[e.length-1];const i=document.createElement("template");i.innerHTML=t;const s=i.ownerDocument.createTreeWalker(i.content,NodeFilter.SHOW_ELEMENT|NodeFilter.SHOW_TEXT,null);let r=0;const o=[],a=[],l=[];for(;s.nextNode();){const e=s.currentNode;if(e.nodeType===Node.ELEMENT_NODE&&e.hasAttributes()){e.hasAttribute("$")&&(l.push(e),a.push({replaceNodeIndex:void 0,attr:void 0,elementId:e.getAttribute("$")||""}),e.removeAttribute("$"));const t=[];for(let n=0;n<e.attributes.length;n++){const i=e.attributes[n].name;if(!Tr.test(i)&&!Tr.test(e.attributes[n].value))continue;t.push(i),l.push(e);const s={index:r,names:i.split(Tr),values:e.attributes[n].value.split(Tr)};r+=s.names.length-1,r+=s.values.length-1;const o={elementId:void 0,replaceNodeIndex:void 0,attr:s};a.push(o)}for(let n=0;n<t.length;n++)e.removeAttribute(t[n])}if(e.nodeType===Node.TEXT_NODE&&-1!==xr(e).indexOf(Sr)){const t=xr(e).split(kr);Er(e,t[t.length-1]);const n=e.parentNode;for(let i=0;i<t.length-1;i++){t[i]&&n.insertBefore(document.createTextNode(t[i]),e);const s=document.createElement("span");l.push(s),a.push({attr:void 0,elementId:void 0,replaceNodeIndex:r++}),n.insertBefore(s,e)}}e.nodeType!==Node.TEXT_NODE||e.previousSibling&&e.previousSibling.nodeType!==Node.ELEMENT_NODE||e.nextSibling&&e.nextSibling.nodeType!==Node.ELEMENT_NODE||!/^\s*$/.test(xr(e))||o.push(e)}for(let e=0;e<l.length;e++)l[e].classList.add(Lr(e));for(const e of o)e.remove();return{template:i,binds:a}}static render(e,t){const n=e.template.ownerDocument.importNode(e.template.content,!0),i=n.firstChild===n.lastChild?n.firstChild:n,s=new Ir(i),r=[];for(let t=0;t<e.binds.length;t++){const e=Lr(t),i=n.querySelector("."+e);i.classList.remove(e),r.push(i)}for(let n=0;n<e.binds.length;n++){const i=e.binds[n],o=r[n];if(void 0!==i.elementId)s.elementsById.set(i.elementId,o);else if(void 0!==i.replaceNodeIndex){const e=t[i.replaceNodeIndex];o.parentNode.replaceChild(this.nodeForValue(e),o)}else{if(void 0===i.attr)throw new Error("Unexpected bind");if(2===i.attr.names.length&&1===i.attr.values.length&&"function"==typeof t[i.attr.index])t[i.attr.index].call(null,o);else{let e=i.attr.names[0];for(let n=1;n<i.attr.names.length;n++)e+=t[i.attr.index+n-1],e+=i.attr.names[n];if(e){let n=i.attr.values[0];for(let e=1;e<i.attr.values.length;e++)n+=t[i.attr.index+i.attr.names.length-1+e-1],n+=i.attr.values[e];o.setAttribute(e,n)}}}}return s}static nodeForValue(e){if(e instanceof Node)return e;if(e instanceof Ir)return e.elementInternal;if(Array.isArray(e)){const t=document.createDocumentFragment();for(const n of e)t.appendChild(this.nodeForValue(n));return t}return document.createTextNode(String(e))}}const Sr="{{template-text}}",kr=/{{template-text}}/,Cr=e=>"template-attribute"+e,Tr=/template-attribute\d+/,Lr=e=>"template-class-"+e,Mr=new Map,Pr=(e,...t)=>Ir.cached(e,...t).element();var Dr=Object.freeze({__proto__:null,Fragment:Ir,textMarker:Sr,attributeMarker:Cr,html:Pr});class Ar extends Ke{hrefInternal;clickable;onClick;onKeyDown;static create(e,t,i,r,o){t||(t=e);return Pr`
  <x-link href='${e}' tabindex="0" class='${i=i||""} devtools-link' ${r?"no-click":""}
  jslog=${s.link().track({click:!0,keydown:"Enter|Space"}).context(o)}>${n.StringUtilities.trimMiddle(t,150)}</x-link>`}constructor(){super(),this.style.setProperty("display","inline"),Ms(this),this.setAttribute("tabindex","0"),this.setAttribute("target","_blank"),this.setAttribute("rel","noopener"),this.hrefInternal=null,this.clickable=!0,this.onClick=e=>{e.consume(!0),this.hrefInternal&&r.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.hrefInternal),this.dispatchEvent(new Event("x-link-invoke"))},this.onKeyDown=e=>{n.KeyboardUtilities.isEnterOrSpaceKey(e)&&(e.consume(!0),this.hrefInternal&&r.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(this.hrefInternal)),this.dispatchEvent(new Event("x-link-invoke"))}}static get observedAttributes(){return Ke.observedAttributes.concat(["href","no-click","title"])}get href(){return this.hrefInternal}attributeChangedCallback(e,t,n){if("no-click"===e)return this.clickable=!n,void this.updateClick();if("href"===e){n||(n="");let e=null,t=null;try{t=new URL(ns(n)),e=t.toString()}catch{}return t&&"javascript:"===t.protocol&&(e=null),this.hrefInternal=e,this.hasAttribute("title")||wt.install(this,n),void this.updateClick()}super.attributeChangedCallback(e,t,n)}updateClick(){null!==this.hrefInternal&&this.clickable?(this.addEventListener("click",this.onClick,!1),this.addEventListener("keydown",this.onKeyDown,!1),this.style.setProperty("cursor","pointer")):(this.removeEventListener("click",this.onClick,!1),this.removeEventListener("keydown",this.onKeyDown,!1),this.style.removeProperty("cursor"))}}customElements.define("x-link",Ar);const Rr=p.html`<p>Hello, <x-link>world!</x-link></p>`;var Br=Object.freeze({__proto__:null,XLink:Ar,ContextMenuProvider:class{appendApplicableItems(e,t,n){let i=n;for(;i&&!(i instanceof Ar);)i=i.parentNodeOrShadowHost();if(!i||!i.href)return;const s=i;t.revealSection().appendItem(Ti(),(()=>{s.href&&r.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(s.href)}),{jslogContext:"open-in-new-tab"}),t.revealSection().appendItem(Li(),(()=>{s.href&&r.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(s.href)}),{jslogContext:"copy-link-address"})}},sample:Rr});const zr={learnMore:"Learn more"},Or=t.i18n.registerUIStrings("ui/legacy/EmptyWidget.ts",zr),Fr=t.i18n.getLocalizedString.bind(void 0,Or);var Hr=Object.freeze({__proto__:null,EmptyWidget:class extends lt{textElement;constructor(e){super(),this.registerRequiredCSS(wr),this.element.classList.add("empty-view-scroller"),this.contentElement=this.element.createChild("div","empty-view"),this.contentElement.setAttribute("jslog",`${s.section("empty-view")}`),this.textElement=this.contentElement.createChild("div","empty-bold-text"),this.textElement.textContent=e}appendParagraph(){return this.contentElement.createChild("p")}appendLink(e){const t=Ar.create(e,Fr(zr.learnMore),void 0,void 0,"learn-more");return this.contentElement.appendChild(t)}set text(e){this.textElement.textContent=e}}}),Wr={cssContent:'.filter-bar{background-color:var(--sys-color-cdt-base-container);flex:none;flex-wrap:wrap;align-items:center;border-bottom:1px solid var(--sys-color-divider);color:var(--sys-color-on-surface-subtle)}.text-filter{width:200px}.filter-bitset-filter{padding:2px;display:inline-flex;overflow:hidden;height:24px;position:relative;margin:0}.filter-bitset-filter span{color:var(--sys-color-on-surface);outline:1px solid var(--sys-color-neutral-outline);outline-offset:-1px;box-sizing:border-box;display:inline-block;flex:none;margin:auto 2px;padding:3px 6px;background:transparent;border-radius:6px;overflow:hidden;cursor:pointer;font-weight:500;font-size:11px}.filter-bitset-filter span:focus-visible{outline:-webkit-focus-ring-color auto 5px}.filter-bitset-filter span:hover{outline:none;background:var(--sys-color-state-hover-on-subtle)}.filter-bitset-filter span.selected,\n.filter-bitset-filter span:active{color:var(--sys-color-on-tonal-container);outline:none;background-color:var(--sys-color-tonal-container)}.filter-bitset-filter-divider{background-color:var(--sys-color-divider);height:16px;width:1px;margin:auto 2px;display:inline-block}.filter-checkbox-filter{padding-left:1px;padding-right:7px;white-space:nowrap;text-overflow:ellipsis;overflow:hidden;display:inline-flex;vertical-align:middle;height:24px;position:relative}.filter-checkbox-filter > [is="dt-checkbox"]{display:flex;margin:auto 0}.toolbar-has-dropdown-shrinkable{flex-shrink:1}.filter-divider{background-color:var(--sys-color-on-base-divider);width:1px;margin:5px 4px;height:16px}.toolbar-button{white-space:nowrap;overflow:hidden;min-width:28px;background:transparent;border-radius:0}.toolbar-button .active-filters-count{margin-right:5px;--override-adorner-background-color:var(--sys-color-tonal-container);--override-adorner-border-color:var(--sys-color-tonal-container);--override-adorner-text-color:var(--sys-color-primary);--override-adorner-font-size:10px;font-weight:700}.toolbar-text{margin:0 4px 0 0;text-overflow:ellipsis;flex:auto;overflow:hidden;text-align:right}.dropdown-filterbar{justify-content:space-between;padding:0 3px 0 5px;border:1px solid transparent;border-radius:7px;display:flex;background-color:transparent;color:var(--sys-color-on-surface-subtle)}button.toolbar-item:focus-visible{background:var(--sys-color-state-hover-on-subtle);border-radius:2px}@media (forced-colors: active){.filter-bitset-filter span:hover,\n  .filter-bitset-filter span.selected,\n  .filter-bitset-filter span:active{forced-color-adjust:none;background:Highlight;color:HighlightText}}\n/*# sourceURL=filter.css */\n'};const Nr={filter:"Filter",egSmalldUrlacomb:"e.g. `/small[d]+/ url:a.com/b`",sclickToSelectMultipleTypes:"{PH1}Click to select multiple types",allStrings:"All"},jr=t.i18n.registerUIStrings("ui/legacy/FilterBar.ts",Nr),Vr=t.i18n.getLocalizedString.bind(void 0,jr);class Ur extends(e.ObjectWrapper.eventMixin(ct)){enabled;stateSetting;filterButtonInternal;filters;alwaysShowFilters;showingWidget;constructor(t,n){super(),this.registerRequiredCSS(Wr),this.enabled=!0,this.element.classList.add("filter-bar"),this.element.setAttribute("jslog",`${s.toolbar("filter-bar")}`),this.stateSetting=e.Settings.Settings.instance().createSetting("filter-bar-"+t+"-toggled",Boolean(n)),this.filterButtonInternal=new ri(this.stateSetting,"filter",Vr(Nr.filter),"filter-filled","filter"),this.filters=[],this.updateFilterBar(),this.stateSetting.addChangeListener(this.updateFilterBar.bind(this))}filterButton(){return this.filterButtonInternal}addDivider(){const e=document.createElement("div");e.classList.add("filter-divider"),this.element.appendChild(e)}addFilter(e){this.filters.push(e),this.element.appendChild(e.element()),e.addEventListener("FilterChanged",this.filterChanged,this),this.updateFilterButton()}setEnabled(e){this.enabled=e,this.filterButtonInternal.setEnabled(e),this.updateFilterBar()}forceShowFilterBar(){this.alwaysShowFilters=!0,this.updateFilterBar()}showOnce(){this.stateSetting.set(!0)}filterChanged(){this.updateFilterButton(),this.dispatchEventToListeners("Changed")}wasShown(){super.wasShown(),this.updateFilterBar()}updateFilterBar(){this.parentWidget()&&!this.showingWidget&&(this.visible()?(this.showingWidget=!0,this.showWidget(),this.showingWidget=!1):this.hideWidget())}focus(){for(let e=0;e<this.filters.length;++e)if(this.filters[e]instanceof _r){this.filters[e].focus();break}}hasActiveFilter(){for(const e of this.filters)if(e.isActive())return!0;return!1}updateFilterButton(){const e=this.hasActiveFilter();this.filterButtonInternal.setDefaultWithRedColor(e),this.filterButtonInternal.setToggleWithRedColor(e)}clear(){this.element.removeChildren(),this.filters=[],this.updateFilterButton()}setting(){return this.stateSetting}visible(){return this.alwaysShowFilters||this.stateSetting.get()&&this.enabled}}class _r extends e.ObjectWrapper.ObjectWrapper{filterElement;#r;suggestionProvider;constructor(){super(),this.filterElement=document.createElement("div");const e=new Xn("text-filter",this.filterElement);e.element.style.borderBottom="none",this.#r=new ni(void 0,1,1,Nr.egSmalldUrlacomb,this.completions.bind(this)),e.appendToolbarItem(this.#r),this.#r.addEventListener("TextChanged",(()=>this.valueChanged())),this.suggestionProvider=null}completions(e,t,n){return this.suggestionProvider?this.suggestionProvider(e,t,n):Promise.resolve([])}isActive(){return Boolean(this.#r.valueWithoutSuggestion())}element(){return this.filterElement}value(){return this.#r.valueWithoutSuggestion()}setValue(e){this.#r.setValue(e),this.valueChanged()}focus(){this.#r.focus()}setSuggestionProvider(e){this.#r.clearAutocomplete(),this.suggestionProvider=e}valueChanged(){this.dispatchEventToListeners("FilterChanged")}clear(){this.setValue("")}}class Kr extends e.ObjectWrapper.ObjectWrapper{filtersElement;typeFilterElementTypeNames;allowedTypes;typeFilterElements;setting;constructor(e,t){super(),this.filtersElement=document.createElement("div"),this.filtersElement.classList.add("filter-bitset-filter"),this.filtersElement.setAttribute("jslog",`${s.section("filter-bitset")}`),Vs(this.filtersElement),Us(this.filtersElement),wt.install(this.filtersElement,Vr(Nr.sclickToSelectMultipleTypes,{PH1:ie.shortcutToString("",se.CtrlOrMeta)})),this.typeFilterElementTypeNames=new WeakMap,this.allowedTypes=new Set,this.typeFilterElements=[],this.addBit(Kr.ALL_TYPES,Vr(Nr.allStrings)),this.typeFilterElements[0].tabIndex=0,this.filtersElement.createChild("div","filter-bitset-filter-divider");for(let t=0;t<e.length;++t)this.addBit(e[t].name,e[t].label(),e[t].title);t?(this.setting=t,t.addChangeListener(this.settingChanged.bind(this)),this.settingChanged()):this.toggleTypeFilter(Kr.ALL_TYPES,!1)}reset(){this.toggleTypeFilter(Kr.ALL_TYPES,!1)}isActive(){return!this.allowedTypes.has(Kr.ALL_TYPES)}element(){return this.filtersElement}accept(e){return this.allowedTypes.has(Kr.ALL_TYPES)||this.allowedTypes.has(e)}settingChanged(){const e=this.setting.get();this.allowedTypes=new Set;for(const t of this.typeFilterElements){const n=this.typeFilterElementTypeNames.get(t);n&&e[n]&&this.allowedTypes.add(n)}this.update()}update(){(0===this.allowedTypes.size||this.allowedTypes.has(Kr.ALL_TYPES))&&(this.allowedTypes=new Set,this.allowedTypes.add(Kr.ALL_TYPES));for(const e of this.typeFilterElements){const t=this.typeFilterElementTypeNames.get(e),n=this.allowedTypes.has(t||"");e.classList.toggle("selected",n),nr(e,n)}this.dispatchEventToListeners("FilterChanged")}addBit(e,t,n){const i=this.filtersElement.createChild("span",e);i.tabIndex=-1,this.typeFilterElementTypeNames.set(i,e),ji(i,t),_s(i),n&&(i.title=n),i.addEventListener("click",this.onTypeFilterClicked.bind(this),!1),i.addEventListener("keydown",this.onTypeFilterKeydown.bind(this),!1),i.setAttribute("jslog",`${s.item(e).track({click:!0})}`),this.typeFilterElements.push(i)}onTypeFilterClicked(e){const t=e;let n;if(n=r.Platform.isMac()?t.metaKey&&!t.ctrlKey&&!t.altKey&&!t.shiftKey:t.ctrlKey&&!t.metaKey&&!t.altKey&&!t.shiftKey,t.target){const e=t.target,i=this.typeFilterElementTypeNames.get(e);this.toggleTypeFilter(i,n)}}onTypeFilterKeydown(e){const t=e,i=t.target;i&&("ArrowLeft"===t.key||"ArrowUp"===t.key?this.keyFocusNextBit(i,!0)&&t.consume(!0):"ArrowRight"===t.key||"ArrowDown"===t.key?this.keyFocusNextBit(i,!1)&&t.consume(!0):n.KeyboardUtilities.isEnterOrSpaceKey(t)&&this.onTypeFilterClicked(t))}keyFocusNextBit(e,t){const n=this.typeFilterElements.indexOf(e);if(-1===n)return!1;const i=t?n-1:n+1;if(i<0||i>=this.typeFilterElements.length)return!1;const s=this.typeFilterElements[i];return s.tabIndex=0,e.tabIndex=-1,s.focus(),!0}toggleTypeFilter(e,t){if(t&&e!==Kr.ALL_TYPES?this.allowedTypes.delete(Kr.ALL_TYPES):this.allowedTypes=new Set,this.allowedTypes.has(e)?this.allowedTypes.delete(e):(this.allowedTypes.add(e),r.userMetrics.legacyResourceTypeFilterItemSelected(e)),0===this.allowedTypes.size&&this.allowedTypes.add(Kr.ALL_TYPES),r.userMetrics.legacyResourceTypeFilterNumberOfSelectedChanged(this.allowedTypes.size),this.setting){const e={};for(const t of this.allowedTypes)e[t]=!0;this.setting.set(e)}else this.update()}static ALL_TYPES="all"}class qr extends e.ObjectWrapper.ObjectWrapper{filterElement;activeWhenChecked;label;checkboxElement;constructor(e,t,n,i,r){super(),this.filterElement=document.createElement("div"),this.filterElement.classList.add("filter-checkbox-filter"),this.activeWhenChecked=Boolean(n),this.label=Ki.create(t),this.filterElement.appendChild(this.label),this.checkboxElement=this.label.checkboxElement,i?Ln(this.checkboxElement,i):this.checkboxElement.checked=!0,this.checkboxElement.addEventListener("change",this.fireUpdated.bind(this),!1),r&&this.checkboxElement.setAttribute("jslog",`${s.toggle().track({change:!0}).context(r)}`)}isActive(){return this.activeWhenChecked===this.checkboxElement.checked}checked(){return this.checkboxElement.checked}setChecked(e){this.checkboxElement.checked=e}element(){return this.filterElement}labelElement(){return this.label}fireUpdated(){this.dispatchEventToListeners("FilterChanged")}}var Gr=Object.freeze({__proto__:null,FilterBar:Ur,TextFilterUI:_r,NamedBitSetFilterUI:Kr,CheckboxFilterUI:qr});var $r=Object.freeze({__proto__:null,FilterSuggestionBuilder:class{keys;valueSorter;valuesMap;constructor(e,t){this.keys=e,this.valueSorter=t||((e,t)=>t.sort()),this.valuesMap=new Map}completions(e,t,i){if(!t&&!i)return Promise.resolve([]);const s=t.startsWith("-");s&&(t=t.substring(1));const r=s?"-":"",o=t.indexOf(":"),a=[];if(-1===o){const e=new RegExp("^"+n.StringUtilities.escapeForRegExp(t),"i");for(const t of this.keys)e.test(t)&&a.push({text:r+t+":"})}else{const e=t.substring(0,o).toLowerCase(),i=t.substring(o+1),s=new RegExp("^"+n.StringUtilities.escapeForRegExp(i),"i"),l=Array.from(this.valuesMap.get(e)||new Set);this.valueSorter(e,l);for(const t of l)s.test(t)&&t!==i&&a.push({text:r+e+":"+t})}return Promise.resolve(a)}addItem(e,t){if(!t)return;let n=this.valuesMap.get(e);n||(n=new Set,this.valuesMap.set(e,n)),n.add(t)}clear(){this.valuesMap.clear()}}});class Xr{constructor(){r.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(r.InspectorFrontendHostAPI.Events.KeyEventUnhandled,this.onKeyEventUnhandled,this)}async onKeyEventUnhandled(e){const{type:t,key:n,keyCode:i,modifiers:s}=e.data;if("keydown"!==t)return;const r=m.instance(),o=Y.instance();r.setFlavor(Z,Z.instance),await o.handleKey(ie.makeKey(i,s),n),r.setFlavor(Z,null)}}new Xr;var Yr=Object.freeze({__proto__:null,ForwardedInputEventHandler:Xr});let Qr=null;class Zr extends HTMLInputElement{history;historyPosition;constructor(){super(),this.history=[""],this.historyPosition=0,this.addEventListener("keydown",this.onKeyDown.bind(this),!1),this.addEventListener("input",this.onInput.bind(this),!1)}static create(){return Qr||(Qr=ps("input","history-input",Zr)),Qr()}onInput(e){this.history.length===this.historyPosition+1&&(this.history[this.history.length-1]=this.value)}onKeyDown(e){const t=e;t.keyCode===be.Up.code?(this.historyPosition=Math.max(this.historyPosition-1,0),this.value=this.history[this.historyPosition],this.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),t.consume(!0)):t.keyCode===be.Down.code?(this.historyPosition=Math.min(this.historyPosition+1,this.history.length-1),this.value=this.history[this.historyPosition],this.dispatchEvent(new Event("input",{bubbles:!0,cancelable:!0})),t.consume(!0)):t.keyCode===be.Enter.code&&this.saveToHistory()}saveToHistory(){this.history.length>1&&this.history[this.history.length-2]===this.value||(this.history[this.history.length-1]=this.value,this.historyPosition=this.history.length-1,this.history.push(""))}}var Jr=Object.freeze({__proto__:null,HistoryInput:Zr});let eo=null;class to{focusRestorer;static startEditing(e,t){return eo||(eo=new to),eo.startEditing(e,t)}editorContent(e){const t=e.element;return"INPUT"===t.tagName&&"text"===t.type?t.value:t.textContent||""}setUpEditor(e){const t=e.element;t.classList.add("editing"),t.setAttribute("contenteditable","plaintext-only");const n=t.getAttribute("role");zs(t),e.oldRole=n;const i=t.getAttribute("tabIndex");("number"!=typeof i||i<0)&&(t.tabIndex=0),this.focusRestorer=new Di(t),e.oldTabIndex=i}closeEditor(e){const t=e.element;t.classList.remove("editing"),t.removeAttribute("contenteditable"),"string"!=typeof e.oldRole?t.removeAttribute("role"):t.setAttribute("role",e.oldRole),"number"!=typeof e.oldTabIndex?t.removeAttribute("tabIndex"):t.setAttribute("tabIndex",e.oldTabIndex),t.scrollTop=0,t.scrollLeft=0}cancelEditing(e){const t=e.element;"INPUT"===t.tagName&&"text"===t.type?t.value=e.oldText||"":t.textContent=e.oldText}startEditing(e,t){if(!yi(e,!0))return null;const i=t||new no((function(){}),(function(){})),s={element:e,config:i,oldRole:null,oldTabIndex:null,oldText:null},r=i.commitHandler,o=i.cancelHandler,a=i.pasteHandler,l=i.context;let c="";const d=this;function h(t){i.blurHandler&&!i.blurHandler(e,t)||g.call(e)}function u(){yi(e,!1),e.removeEventListener("blur",h,!1),e.removeEventListener("keydown",f,!0),a&&e.removeEventListener("paste",b,!0),d.focusRestorer&&d.focusRestorer.restore(),d.closeEditor(s)}function p(){d.cancelEditing(s),u(),o(this,l)}function g(){u(),r(this,d.editorContent(s),s.oldText||"",l,c),e.dispatchEvent(new Event("change"))}function m(t,n){"commit"===t?(g.call(e),n.consume(!0)):"cancel"===t?(p.call(e),n.consume(!0)):t&&t.startsWith("move-")&&(c=t.substring(5),"Tab"===n.key&&n.consume(!0),h())}function b(e){if(!a)return;m(a(e),e)}function f(e){let t=function(e){return"Enter"===e.key?"commit":e.keyCode===be.Esc.code||e.key===n.KeyboardUtilities.ESCAPE_KEY?"cancel":"Tab"===e.key?"move-"+(e.shiftKey?"backward":"forward"):""}(e);if(!t&&i.postKeydownFinishHandler){const n=i.postKeydownFinishHandler(e);n&&(t=n)}m(t,e)}this.setUpEditor(s),s.oldText=this.editorContent(s),e.addEventListener("blur",h,!1),e.addEventListener("keydown",f,!0),void 0!==a&&e.addEventListener("paste",b,!0);return{cancel:p.bind(e),commit:g.bind(e)}}}class no{commitHandler;cancelHandler;context;blurHandler;pasteHandler;postKeydownFinishHandler;constructor(e,t,n,i){this.commitHandler=e,this.cancelHandler=t,this.context=n,this.blurHandler=i}setPasteHandler(e){this.pasteHandler=e}setPostKeydownFinishHandler(e){this.postKeydownFinishHandler=e}}var io=Object.freeze({__proto__:null,InplaceEditor:to,Config:no}),so={cssContent:".list{flex:auto 0 1;overflow-y:auto;border:1px solid var(--sys-color-divider);flex-direction:column;--override-background-list-item-color:hsl(0deg 0% 96%)}.theme-with-dark-background .list,\n:host-context(.theme-with-dark-background) .list{--override-background-list-item-color:hsl(0deg 0% 16%)}.list-separator{background:var(--sys-color-divider);height:1px}.list-item{flex:none;min-height:30px;display:flex;align-items:center;position:relative;overflow:hidden}.list-item:focus-within:not(:active){background:var(--sys-color-state-hover-on-subtle)}.list-widget-input-validation-error{color:var(--sys-color-error);margin:0 5px}.controls-container{display:flex;flex-direction:row;justify-content:flex-end;align-items:stretch;pointer-events:none}.controls-gradient{flex:0 1 50px}.list-item:focus-within:not(:active) .controls-gradient{background-image:linear-gradient(90deg,transparent,var(--override-background-list-item-color))}.controls-buttons{flex:none;display:flex;flex-direction:row;align-items:center;pointer-events:auto;visibility:hidden;background-color:var(--sys-color-cdt-base-container);.list-item:hover &{visibility:visible}}.list-item:focus-within:not(:active) .controls-buttons{background-color:var(--override-background-list-item-color);visibility:visible}.editor-container{display:flex;flex-direction:column;align-items:stretch;flex:none;background:var(--sys-color-surface3);overflow:hidden}.editor-content{flex:auto;display:flex;flex-direction:column;align-items:stretch}.editor-buttons{flex:none;display:flex;flex-direction:row;align-items:center;justify-content:flex-start;padding:5px;gap:var(--sys-size-6)}.editor-buttons > button{flex:none;margin-right:10px}.editor-content input{margin-right:10px}.editor-content input.error-input{background-color:var(--sys-color-cdt-base-container)}.text-prompt-container{padding:3px 6px;height:24px;border:none;box-shadow:var(--legacy-focus-ring-inactive-shadow);border-radius:2px;width:100%;background-color:var(--sys-color-cdt-base-container);&:focus{border:1px solid var(--sys-color-state-focus-ring)}& .text-prompt{width:100%}}@media (forced-colors: active){.list-item:focus-within .controls-buttons,\n  .list-item:hover .controls-buttons{background-color:canvas}.list-item:focus-within,\n  .list-item:hover{forced-color-adjust:none;background:Highlight}.list-item:focus-within *,\n  .list-item:hover *{color:HighlightText}.list-item:focus-within .controls-gradient,\n  .list-item:hover .controls-gradient{background-image:unset}}\n/*# sourceURL=listWidget.css */\n"};const ro={editString:"Edit",removeString:"Remove",saveString:"Save",addString:"Add",cancelString:"Cancel",changesSaved:"Changes to item have been saved",removedItem:"Item has been removed"},oo=t.i18n.registerUIStrings("ui/legacy/ListWidget.ts",ro),ao=t.i18n.getLocalizedString.bind(void 0,oo);var lo=Object.freeze({__proto__:null,ListWidget:class extends lt{delegate;list;lastSeparator;focusRestorer;items;editable;elements;editor;editItem;editElement;emptyPlaceholder;isTable;constructor(e,t=!0,n=!1){super(!0,t),this.registerRequiredCSS(so),this.delegate=e,this.list=this.contentElement.createChild("div","list"),this.lastSeparator=!1,this.focusRestorer=null,this.items=[],this.editable=[],this.elements=[],this.editor=null,this.editItem=null,this.editElement=null,this.emptyPlaceholder=null,this.isTable=n,n&&(this.list.role="table"),this.updatePlaceholder()}clear(){this.items=[],this.editable=[],this.elements=[],this.lastSeparator=!1,this.list.removeChildren(),this.updatePlaceholder(),this.stopEditing()}appendItem(e,t){if(this.lastSeparator&&this.items.length){const e=document.createElement("div");e.classList.add("list-separator"),this.isTable&&(e.role="rowgroup"),this.list.appendChild(e)}this.lastSeparator=!1,this.items.push(e),this.editable.push(t);const n=this.list.createChild("div","list-item");this.isTable&&(n.role="rowgroup");const i=this.delegate.renderItem(e,t);i.hasAttribute("jslog")||n.setAttribute("jslog",`${s.item()}`),n.appendChild(i),t&&(n.classList.add("editable"),n.tabIndex=0,n.appendChild(this.createControls(e,n))),this.elements.push(n),this.updatePlaceholder()}appendSeparator(){this.lastSeparator=!0}removeItem(e){this.editItem===this.items[e]&&this.stopEditing();const t=this.elements[e],n=t.previousElementSibling,i=n&&n.classList.contains("list-separator"),s=t.nextElementSibling,r=s&&s.classList.contains("list-separator");!i||!r&&s||n.remove(),r&&!n&&s.remove(),t.remove(),this.elements.splice(e,1),this.items.splice(e,1),this.editable.splice(e,1),this.updatePlaceholder()}addNewItem(e,t){this.startEditing(t,null,this.elements[e]||null)}setEmptyPlaceholder(e){this.emptyPlaceholder=e,this.updatePlaceholder()}createControls(e,t){const n=document.createElement("div");n.classList.add("controls-container"),n.classList.add("fill"),n.createChild("div","controls-gradient");const i=n.createChild("div","controls-buttons"),s=new Xn("",i),r=new Jn(ao(ro.editString),"edit",void 0,"edit-item");r.addEventListener("Click",function(){const n=this.elements.indexOf(t),i=this.elements[n+1]||null;this.startEditing(e,t,i)}.bind(this)),s.appendToolbarItem(r);const o=new Jn(ao(ro.removeString),"bin",void 0,"remove-item");return o.addEventListener("Click",function(){const e=this.elements.indexOf(t);this.element.focus(),this.delegate.removeItemRequested(this.items[e],e),mr(ao(ro.removedItem)),this.elements[Math.min(e,this.elements.length-1)].focus()}.bind(this)),s.appendToolbarItem(o),n}wasShown(){super.wasShown(),this.stopEditing()}updatePlaceholder(){this.emptyPlaceholder&&(this.elements.length||this.editor?this.emptyPlaceholder.remove():this.list.appendChild(this.emptyPlaceholder))}startEditing(e,t,n){if(t&&this.editElement===t)return;this.stopEditing(),this.focusRestorer=new Di(this.element),this.list.classList.add("list-editing"),this.element.classList.add("list-editing"),this.editItem=e,this.editElement=t,t&&t.classList.add("hidden");const i=t?this.elements.indexOf(t):-1;this.editor=this.delegate.beginEdit(e),this.updatePlaceholder(),this.list.insertBefore(this.editor.element,n),this.editor.beginEdit(e,i,ao(t?ro.saveString:ro.addString),this.commitEditing.bind(this),this.stopEditing.bind(this))}commitEditing(){const e=this.editItem,t=!this.editElement,n=this.editor,i=this.editElement?this.elements.indexOf(this.editElement):this.elements.length-1;this.stopEditing(),null!==e&&(this.delegate.commitEdit(e,n,t),mr(ao(ro.changesSaved)),this.elements[i]&&this.elements[i].focus())}stopEditing(){this.list.classList.remove("list-editing"),this.element.classList.remove("list-editing"),this.focusRestorer&&this.focusRestorer.restore(),this.editElement&&this.editElement.classList.remove("hidden"),this.editor&&this.editor.element.parentElement&&this.editor.element.remove(),this.editor=null,this.editItem=null,this.editElement=null,this.updatePlaceholder()}},Editor:class{element;contentElementInternal;commitButton;cancelButton;errorMessageContainer;controls;controlByName;validators;commit;cancel;item;index;constructor(){this.element=document.createElement("div"),this.element.classList.add("editor-container"),this.element.setAttribute("jslog",`${s.pane("editor").track({resize:!0})}`),this.element.addEventListener("keydown",t.bind(null,n.KeyboardUtilities.isEscKey,this.cancelClicked.bind(this)),!1),this.contentElementInternal=this.element.createChild("div","editor-content"),this.contentElementInternal.addEventListener("keydown",t.bind(null,(e=>"Enter"===e.key&&!(e.target instanceof HTMLSelectElement)),this.commitClicked.bind(this)),!1);const e=this.element.createChild("div","editor-buttons");function t(e,t,n){e(n)&&(n.consume(!0),t())}this.cancelButton=Vi(ao(ro.cancelString),this.cancelClicked.bind(this),{jslogContext:"cancel",variant:"outlined"}),this.cancelButton.setAttribute("jslog",`${s.action("cancel").track({click:!0})}`),e.appendChild(this.cancelButton),this.commitButton=Vi("",this.commitClicked.bind(this),{jslogContext:"commit",variant:"primary"}),e.appendChild(this.commitButton),this.errorMessageContainer=this.element.createChild("div","list-widget-input-validation-error"),ks(this.errorMessageContainer),this.controls=[],this.controlByName=new Map,this.validators=[],this.commit=null,this.cancel=null,this.item=null,this.index=-1}contentElement(){return this.contentElementInternal}createInput(e,t,n,i){const r=Ui("",t);return r.placeholder=n,r.addEventListener("input",this.validateControls.bind(this,!1),!1),r.setAttribute("jslog",`${s.textField().track({change:!0,keydown:"Enter"}).context(e)}`),or(r,n),this.controlByName.set(e,r),this.controls.push(r),this.validators.push(i),r}createSelect(e,t,i,r){const o=document.createElement("select");o.setAttribute("jslog",`${s.dropDown().track({change:!0}).context(e)}`),o.classList.add("chrome-select");for(let e=0;e<t.length;++e){const i=o.createChild("option");i.value=t[e],i.textContent=t[e],i.setAttribute("jslog",`${s.item(n.StringUtilities.toKebabCase(t[e])).track({click:!0})}`)}return r&&(wt.install(o,r),or(o,r)),o.addEventListener("input",this.validateControls.bind(this,!1),!1),o.addEventListener("blur",this.validateControls.bind(this,!1),!1),this.controlByName.set(e,o),this.controls.push(o),this.validators.push(i),o}createCustomControl(e,t,n){const i=new t;return this.controlByName.set(e,i),this.controls.push(i),this.validators.push(n),i}control(e){const t=this.controlByName.get(e);if(!t)throw new Error(`Control with name ${e} does not exist, please verify.`);return t}validateControls(e){let t=!0;this.errorMessageContainer.textContent="";for(let n=0;n<this.controls.length;++n){const i=this.controls[n],{valid:s,errorMessage:r}=this.validators[n].call(null,this.item,this.index,i);if(i.classList.toggle("error-input",!s&&!e),sr(i,!s&&!e),!e&&r){if(this.errorMessageContainer.textContent){const e=document.createElement("br");this.errorMessageContainer.append(e)}this.errorMessageContainer.append(r)}t=t&&s}this.commitButton.disabled=!t}requestValidation(){this.validateControls(!1)}beginEdit(e,t,n,i,s){this.commit=i,this.cancel=s,this.item=e,this.index=t,this.commitButton.textContent=n,this.element.scrollIntoViewIfNeeded(!1),this.controls.length&&this.controls[0].focus(),this.validateControls(!0)}commitClicked(){if(this.commitButton.disabled)return;const e=this.commit;this.commit=null,this.cancel=null,this.item=null,this.index=-1,e&&e()}cancelClicked(){const e=this.cancel;this.commit=null,this.cancel=null,this.item=null,this.index=-1,e&&e()}}});class co extends lt{panelName;constructor(e){super(),this.element.setAttribute("jslog",`${s.panel().context(e).track({resize:!0})}`),this.element.classList.add("panel"),this.element.setAttribute("aria-label",e),this.element.classList.add(e),this.panelName=e,self.UI=self.UI||{},self.UI.panels=self.UI.panels||{},UI.panels[e]=this}get name(){return this.panelName}searchableView(){return null}elementsToRestoreScrollPositionsFor(){return[]}}var ho=Object.freeze({__proto__:null,Panel:co,PanelWithSidebar:class extends co{panelSplitWidget;mainWidget;sidebarWidget;constructor(e,t){super(e),this.panelSplitWidget=new mt(!0,!1,this.panelName+"-panel-split-view-state",t||200),this.panelSplitWidget.show(this.element),this.mainWidget=new lt,this.panelSplitWidget.setMainWidget(this.mainWidget),this.sidebarWidget=new lt,this.sidebarWidget.setMinimumSize(100,25),this.panelSplitWidget.setSidebarWidget(this.sidebarWidget),this.sidebarWidget.element.classList.add("panel-sidebar"),this.sidebarWidget.element.setAttribute("jslog",`${s.pane("sidebar").track({resize:!0})}`)}panelSidebarElement(){return this.sidebarWidget.element}mainElement(){return this.mainWidget.element}splitWidget(){return this.panelSplitWidget}}}),uo={cssContent:".widget{display:flex;background:var(--sys-color-cdt-base-container);border:1px solid transparent;box-shadow:var(--drop-shadow);border-radius:2px;overflow:auto;user-select:text;line-height:11px;&.borderless-popover{border:0}}.widget.has-padding{padding:6px}\n/*# sourceURL=popover.css */\n"};class po{static createPopover=e=>{const t=new ms(`${s.popover(e).parent("mapped")}`);return t.registerRequiredCSS(uo),t.setSizeBehavior("MeasureContent"),t.setMarginBehavior("Arrow"),t};disableOnClick;hasPadding;getRequest;scheduledRequest;hidePopoverCallback;container;showTimeout;hideTimeout;hidePopoverTimer;showPopoverTimer;boundMouseDown;boundMouseMove;boundMouseOut;jslogContext;constructor(e,t,n){this.disableOnClick=!1,this.hasPadding=!1,this.getRequest=t,this.jslogContext=n,this.scheduledRequest=null,this.hidePopoverCallback=null,this.container=e,this.showTimeout=0,this.hideTimeout=0,this.hidePopoverTimer=null,this.showPopoverTimer=null,this.boundMouseDown=this.mouseDown.bind(this),this.boundMouseMove=this.mouseMove.bind(this),this.boundMouseOut=this.mouseOut.bind(this),this.container.addEventListener("mousedown",this.boundMouseDown,!1),this.container.addEventListener("mousemove",this.boundMouseMove,!1),this.container.addEventListener("mouseout",this.boundMouseOut,!1),this.setTimeout(1e3)}setTimeout(e,t){this.showTimeout=e,this.hideTimeout="number"==typeof t?t:e/2}setHasPadding(e){this.hasPadding=e}setDisableOnClick(e){this.disableOnClick=e}eventInScheduledContent(e){const t=e;return!!this.scheduledRequest&&this.scheduledRequest.box.contains(t.clientX,t.clientY)}mouseDown(e){this.disableOnClick?this.hidePopover():this.eventInScheduledContent(e)||(this.startHidePopoverTimer(0),this.stopShowPopoverTimer(),this.startShowPopoverTimer(e,0))}mouseMove(e){const t=e;if(this.eventInScheduledContent(t))return this.stopShowPopoverTimer(),void this.startShowPopoverTimer(t,this.isPopoverVisible()?.6*this.showTimeout:this.showTimeout);this.startHidePopoverTimer(this.hideTimeout),this.stopShowPopoverTimer(),t.buttons&&this.disableOnClick||this.startShowPopoverTimer(t,this.isPopoverVisible()?.6*this.showTimeout:this.showTimeout)}popoverMouseMove(e){this.stopHidePopoverTimer()}popoverMouseOut(e,t){const n=t;if(!e.isShowing())return;const i=n.relatedTarget;i&&!i.isSelfOrDescendant(e.contentElement)&&this.startHidePopoverTimer(this.hideTimeout)}mouseOut(e){this.isPopoverVisible()&&(this.eventInScheduledContent(e)||this.startHidePopoverTimer(this.hideTimeout))}startHidePopoverTimer(e){this.hidePopoverCallback&&!this.hidePopoverTimer&&(this.hidePopoverTimer=window.setTimeout((()=>{this.hidePopoverInternal(),this.hidePopoverTimer=null}),e))}startShowPopoverTimer(e,t){this.scheduledRequest=this.getRequest.call(null,e),this.scheduledRequest&&(this.showPopoverTimer=window.setTimeout((()=>{this.showPopoverTimer=null,this.stopHidePopoverTimer(),this.hidePopoverInternal();const t=e.target.ownerDocument;this.showPopover(t)}),t))}stopShowPopoverTimer(){this.showPopoverTimer&&(clearTimeout(this.showPopoverTimer),this.showPopoverTimer=null)}isPopoverVisible(){return Boolean(this.hidePopoverCallback)}hidePopover(){this.stopShowPopoverTimer(),this.hidePopoverInternal()}hidePopoverInternal(){this.hidePopoverCallback&&(this.hidePopoverCallback.call(null),this.hidePopoverCallback=null)}showPopover(e){const t=po.createPopover(this.jslogContext),n=this.scheduledRequest;n&&n.show.call(null,t).then((i=>{i&&(this.scheduledRequest===n?(go&&go.hidePopover(),go=this,s.setMappedParent(t.contentElement,this.container),t.contentElement.classList.toggle("has-padding",this.hasPadding),t.contentElement.addEventListener("mousemove",this.popoverMouseMove.bind(this),!0),t.contentElement.addEventListener("mouseout",this.popoverMouseOut.bind(this,t),!0),t.setContentAnchorBox(n.box),t.show(e),this.hidePopoverCallback=()=>{n.hide&&n.hide.call(null),t.hide(),go=null}):n.hide&&n.hide.call(null))}))}stopHidePopoverTimer(){this.hidePopoverTimer&&(clearTimeout(this.hidePopoverTimer),this.hidePopoverTimer=null,this.stopShowPopoverTimer())}dispose(){this.container.removeEventListener("mousedown",this.boundMouseDown,!1),this.container.removeEventListener("mousemove",this.boundMouseMove,!1),this.container.removeEventListener("mouseout",this.boundMouseOut,!1)}}let go=null;var mo=Object.freeze({__proto__:null,PopoverHelper:po}),bo={cssContent:".progress-indicator-shadow-stop-button{background-color:var(--sys-color-error-bright);border:0;width:10px;height:12px;border-radius:2px}.progress-indicator-shadow-container{display:flex;flex:1 0 auto;align-items:center}.progress-indicator-shadow-container .title{text-overflow:ellipsis;overflow:hidden;max-width:150px;margin-right:2px;color:var(--sys-color-token-subtle)}.progress-indicator-shadow-container progress{flex:auto;margin:0 2px;width:100px}\n/*# sourceURL=progressIndicator.css */\n"};var fo=Object.freeze({__proto__:null,ProgressIndicator:class{element;shadowRoot;contentElement;labelElement;progressElement;stopButton;isCanceledInternal;worked;isDone;constructor(){this.element=document.createElement("div"),this.element.classList.add("progress-indicator"),this.shadowRoot=ds(this.element,{cssFile:bo,delegatesFocus:void 0}),this.contentElement=this.shadowRoot.createChild("div","progress-indicator-shadow-container"),this.labelElement=this.contentElement.createChild("div","title"),this.progressElement=this.contentElement.createChild("progress"),this.progressElement.value=0,this.stopButton=this.contentElement.createChild("button","progress-indicator-shadow-stop-button"),this.stopButton.addEventListener("click",this.cancel.bind(this)),this.isCanceledInternal=!1,this.worked=0}show(e){e.appendChild(this.element)}done(){this.isDone||(this.isDone=!0,this.element.remove())}cancel(){this.isCanceledInternal=!0}isCanceled(){return this.isCanceledInternal}setTitle(e){this.labelElement.textContent=e}setTotalWork(e){this.progressElement.max=e}setWorked(e,t){this.worked=e,this.progressElement.value=e,t&&this.setTitle(t)}incrementWorked(e){this.setWorked(this.worked+(e||1))}}});const vo=new CSSStyleSheet;vo.replaceSync(".widget{padding:20px}.remote-debugging-terminated-title{font-size:17px;font-weight:normal;margin:6px 0}.remote-debugging-terminated-message{font-size:14px;margin:5px 0;margin-bottom:24px}.remote-debugging-terminated-options{display:grid;grid-template-columns:1fr auto;grid-gap:8px;align-items:center;padding-top:12px;border-top:1px solid var(--color-details-hairline-light)}.remote-debugging-terminated-label{grid-column:1;margin:8px 0;max-width:300px;font-size:larger;line-height:1.4}.remote-debugging-terminated-options .text-button{grid-column:2}.remote-debugging-terminated-feedback-container{display:flex;flex-direction:column;align-items:center;margin-top:16px;padding:12px 16px;background-color:var(--color-background-elevation-1);border-radius:6px}.remote-debugging-terminated-feedback-label{font-size:14px;margin-bottom:8px}.remote-debugging-terminated-reason{--override-reason-color:#8b0000;color:var(--override-reason-color)}.theme-with-dark-background .reason,\n:host-context(.theme-with-dark-background) .reason{--override-reason-color:rgb(255 116 116)}\n/*# sourceURL=remoteDebuggingTerminatedScreen.css */\n");const yo={title:"DevTools is disconnected",debuggingConnectionWasClosed:"Debugging connection was closed. Reason: ",reconnectWhenReadyByReopening:"Reconnect when ready (will reload DevTools)",reconnectDevtools:"Reconnect `DevTools`",closeDialog:"Dismiss",closeDialogDetail:"Dismiss this dialog and continue using `DevTools` while disconnected",sendFeedbackMessage:"[FB-only] Please send feedback if this disconnection is unexpected.",sendFeedback:"Send feedback"},wo=t.i18n.registerUIStrings("ui/legacy/RemoteDebuggingTerminatedScreen.ts",yo),xo=t.i18n.getLocalizedString.bind(void 0,wo),{render:Eo,html:Io}=p;class So extends lt{constructor(e,t){super(!0),this.registerCSSFiles([vo]);const n=globalThis.FB_ONLY__reactNativeFeedbackLink;Eo(Io`
        <h1 class="remote-debugging-terminated-title">${xo(yo.title)}</h1>
        <div class="remote-debugging-terminated-message">
          <span>${xo(yo.debuggingConnectionWasClosed)}</span>
          <span class="remote-debugging-terminated-reason">${e}</span>
        </div>
        <div class="remote-debugging-terminated-options">
          <div class="remote-debugging-terminated-label">
            ${xo(yo.reconnectWhenReadyByReopening)}
          </div>
          ${Vi(xo(yo.reconnectDevtools),(()=>{window.location.reload()}),{className:"primary-button",jslogContext:"reconnect"})}
          <div class="remote-debugging-terminated-label">
            ${xo(yo.closeDialogDetail)}
          </div>
          ${Vi(xo(yo.closeDialog),t,{jslogContext:"dismiss"})}
        </div>
        ${null!=n?this.#o(n):null}
      `,this.contentElement,{host:this})}static show(e){const t=new ws("remote-debnugging-terminated");t.setSizeBehavior("MeasureContent"),t.setDimmed(!0),new So(e,(()=>t.hide())).show(t.contentElement),t.show(),r.rnPerfMetrics.remoteDebuggingTerminated(e)}#o(e){return Io`
      <div class="remote-debugging-terminated-feedback-container">
        <div class="remote-debugging-terminated-feedback-label">${xo(yo.sendFeedbackMessage)}</div>
        ${Vi(xo(yo.sendFeedback),(()=>{r.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(e)}),{jslogContext:"sendFeedback"})}
      </div>
    `}}var ko=Object.freeze({__proto__:null,RemoteDebuggingTerminatedScreen:So}),Co={cssContent:':host{background-color:var(--sys-color-cdt-base-container)}.report-content-box{background-color:var(--sys-color-cdt-base-container);overflow:auto}.report-content-box.no-scroll{overflow:visible}.report-header{border-bottom:1px solid var(--sys-color-divider);padding:12px 24px}.report-header .toolbar{margin-bottom:-8px;margin-top:5px;margin-left:-8px}.report-title{font-size:15px;white-space:nowrap;overflow:hidden;text-overflow:ellipsis;user-select:none}.report-url,\n.report-subtitle{font-size:12px;margin-top:10px}.report-section{display:flex;padding:12px;border-bottom:1px solid var(--sys-color-divider);flex-direction:column}.report-section-header{margin-left:18px;display:flex;flex-direction:row;align-items:center}.report-section-title{flex:auto;text-overflow:ellipsis;overflow:hidden;line-height:16px;font-weight:bold;color:var(--sys-color-on-surface)}.report-field{margin-top:8px;display:flex;line-height:28px}.report-row{margin:10px 0 2px 18px;&:has(span[is="dt-checkbox"]){margin-top:7px}}.report-field-name{color:var(--sys-color-on-surface-subtle);flex:0 0 128px;text-align:right;padding:0 6px;white-space:pre-wrap}.report-field-value{flex:auto;padding:0 6px;white-space:pre;user-select:text}.report-field-value-is-flexed{display:flex;white-space:pre-wrap}.report-field-value-subtitle{color:var(--sys-color-state-disabled);line-height:14px}.report-row-selectable{user-select:text}.image-wrapper,\n.image-wrapper img{max-width:200px;max-height:200px;display:block;object-fit:contain}.image-wrapper{height:fit-content;margin-right:8px}.show-mask img{clip-path:circle(40% at 50% 50%)}.show-mask .image-wrapper{background:var(--image-file-checker)}@media (forced-colors: active){.report-field-value .inline-icon{color:ButtonText}.report-field-value .multiline-value{color:ButtonText}}\n/*# sourceURL=reportView.css */\n'};class To extends lt{jslogContext;headerElement;headerButtons;titleElement;fieldList;fieldMap;constructor(e,t,n){super(),this.jslogContext=n,this.element.classList.add("report-section"),t&&this.element.classList.add(t),n&&this.element.setAttribute("jslog",`${s.section(n)}`),this.jslogContext=n,this.headerButtons=[],this.headerElement=this.element.createChild("div","report-section-header"),this.titleElement=this.headerElement.createChild("div","report-section-title"),this.setTitle(e),qs(this.titleElement,2),this.fieldList=this.element.createChild("div","vbox"),this.fieldMap=new Map}title(){return this.titleElement.textContent||""}getTitleElement(){return this.titleElement}getFieldElement(){return this.fieldList}appendFieldWithCustomView(e){this.fieldList.append(e)}setTitle(e,t){this.titleElement.textContent!==e&&(this.titleElement.textContent=e),wt.install(this.titleElement,t||""),this.titleElement.classList.toggle("hidden",!this.titleElement.textContent)}setUiGroupTitle(e){Ls(this.element),or(this.element,e)}appendButtonToHeader(e){this.headerButtons.push(e),this.headerElement.appendChild(e)}setHeaderButtonsState(e){this.headerButtons.map((t=>{t.disabled=e}))}appendField(e,t){let n=this.fieldMap.get(e);return n||(n=this.fieldList.createChild("div","report-field"),n.createChild("div","report-field-name").textContent=e,this.fieldMap.set(e,n),n.createChild("div","report-field-value")),t&&n.lastElementChild&&(n.lastElementChild.textContent=t),n.lastElementChild}appendFlexedField(e,t){const n=this.appendField(e,t);return n.classList.add("report-field-value-is-flexed"),n}removeField(e){const t=this.fieldMap.get(e);t&&t.remove(),this.fieldMap.delete(e)}setFieldVisible(e,t){const n=this.fieldMap.get(e);n&&n.classList.toggle("hidden",!t)}fieldValue(e){const t=this.fieldMap.get(e);return t?t.lastElementChild:null}appendRow(){return this.fieldList.createChild("div","report-row")}appendSelectableRow(){return this.fieldList.createChild("div","report-row report-row-selectable")}clearContent(){this.fieldList.removeChildren(),this.fieldMap.clear()}markFieldListAsGroup(){Ls(this.fieldList),or(this.fieldList,this.title())}setIconMasked(e){this.element.classList.toggle("show-mask",e)}}var Lo=Object.freeze({__proto__:null,ReportView:class extends lt{contentBox;headerElement;titleElement;sectionList;subtitleElement;urlElement;constructor(e){super(!0),this.registerRequiredCSS(Co),this.contentBox=this.contentElement.createChild("div","report-content-box"),this.headerElement=this.contentBox.createChild("div","report-header vbox"),this.titleElement=this.headerElement.createChild("div","report-title"),e?this.titleElement.textContent=e:this.headerElement.classList.add("hidden"),qs(this.titleElement,1),this.sectionList=this.contentBox.createChild("div","vbox")}getHeaderElement(){return this.headerElement}setTitle(e){this.titleElement.textContent!==e&&(this.titleElement.textContent=e,this.headerElement.classList.toggle("hidden",Boolean(e)))}setSubtitle(e){this.subtitleElement&&this.subtitleElement.textContent===e||(this.subtitleElement||(this.subtitleElement=this.headerElement.createChild("div","report-subtitle")),this.subtitleElement.textContent=e)}setURL(e){this.urlElement||(this.urlElement=this.headerElement.createChild("div","report-url link")),this.urlElement.removeChildren(),e&&this.urlElement.appendChild(e),this.urlElement.setAttribute("jslog",`${s.link("source-location").track({click:!0})}`)}createToolbar(){const e=new Xn("");return this.headerElement.appendChild(e.element),e}appendSection(e,t,n){const i=new To(e,t,n);return i.show(this.sectionList),i}sortSections(e){const t=this.children().slice();if(!t.every(((t,n,i)=>!n||e(i[n-1],i[n])<=0))){this.detachChildWidgets(),t.sort(e);for(const e of t)e.show(this.sectionList)}}setHeaderVisible(e){this.headerElement.classList.toggle("hidden",!e)}setBodyScrollable(e){this.contentBox.classList.toggle("no-scroll",!e)}},Section:To}),Mo={cssContent:".root-view{background-color:var(--sys-color-cdt-base-container);overflow:hidden;position:absolute!important;left:0;top:0;right:0;bottom:0}\n/*# sourceURL=rootView.css */\n"};var Po=Object.freeze({__proto__:null,RootView:class extends lt{window;constructor(){super(),this.markAsRoot(),this.element.classList.add("root-view"),this.registerRequiredCSS(Mo),this.element.setAttribute("spellcheck","false")}attachToDocument(e){e.defaultView&&e.defaultView.addEventListener("resize",this.doResize.bind(this),!1),this.window=e.defaultView,this.doResize(),this.show(e.body)}doResize(){if(this.window){const e=this.constraints().minimum,t=pt.instance().zoomFactor(),n=Math.min(0,this.window.innerWidth-e.width/t);this.element.style.marginRight=n+"px";const i=Math.min(0,this.window.innerHeight-e.height/t);this.element.style.marginBottom=i+"px"}super.doResize()}}}),Do={cssContent:'.search-bar{flex:0 0 33px;border-top:1px solid var(--sys-color-divider);devtools-icon[name="search"]{width:var(--sys-size-8);height:var(--sys-size-8);color:var(--sys-color-on-surface-subtle);margin-right:var(--sys-size-3)}&.replaceable{& devtools-icon[name="search"]{display:none}}&:not(.replaceable) .replace-element{display:none}.search-replace{appearance:none;color:var(--sys-color-on-surface);background-color:transparent;border:0;z-index:1;flex:1;&::placeholder{color:var(--sys-color-on-surface-subtle)}&:placeholder-shown + .clear-button{display:none}&::-webkit-search-cancel-button{display:none}}.search-input-background{grid-row:1/2}.icon-and-input{margin-left:var(--sys-size-5);grid-row:1/2;grid-column:1/2;display:inline-flex;&:hover ~ .search-input-background{background-color:var(--sys-color-state-hover-on-subtle)}&:has(.search-replace:placeholder-shown) ~ .search-config-buttons > .clear-button{display:none}}.toolbar-search{display:flex;margin:var(--sys-size-2)}.second-row-buttons{height:var(--sys-size-12);display:inline-flex;& > devtools-button{margin-right:var(--sys-size-3)}}.input-line{grid-column:1/3;display:inline-flex;padding:0 var(--sys-size-2) 0 var(--sys-size-5);border-radius:100px;height:var(--sys-size-10);position:relative;&:not(:has(devtools-button:hover)):hover{background-color:var(--sys-color-state-hover-on-subtle)}&::before{content:"";box-sizing:inherit;height:100%;width:100%;position:absolute;left:0;background:var(--sys-color-cdt-base);z-index:-10;border-radius:100px;padding:var(--sys-size-2)}& > devtools-button{width:var(--sys-size-11);justify-content:center}& devtools-button:last-child(devtools-button){margin-right:var(--sys-size-4)}}.search-inputs{display:grid;grid-template-columns:1fr min-content;grid-auto-rows:var(--sys-size-12);flex-grow:1;align-items:center;min-width:150px}.first-row-buttons{display:flex;justify-content:space-between}.toolbar{padding:0;height:var(--sys-size-12);display:flex;align-items:center}.search-config-buttons{margin:0 var(--sys-size-3) 0 auto;z-index:1;display:inline-flex;grid-row:1/2;grid-column:2/3}.toolbar-search-buttons{margin-left:var(--sys-size-3)}.replace-element:has(input:focus){box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}.search-inputs:has(input[type="search"]:focus) .search-input-background{box-shadow:inset 0 0 0 2px var(--sys-color-state-focus-ring)}}:host-context(#sources-panel-sources-view) .search-bar{flex-basis:auto}:host-context(#sources-panel-sources-view) .toolbar-search{flex-wrap:wrap}\n/*# sourceURL=searchableView.css */\n'};const Ao={replace:"Replace",enableFindAndReplace:"Find and replace",disableFindAndReplace:"Disable find and replace",findString:"Find",searchPrevious:"Show previous result",searchNext:"Show next result",enableCaseSensitive:"Enable case sensitive search",disableCaseSensitive:"Disable case sensitive search",enableRegularExpression:"Enable regular expressions",disableRegularExpression:"Disable regular expressions",closeSearchBar:"Close search bar",replaceAll:"Replace all",dOfD:"{PH1} of {PH2}",accessibledOfD:"Shows result {PH1} of {PH2}",matchString:"1 match",dMatches:"{PH1} matches",clearInput:"Clear"},Ro=t.i18n.registerUIStrings("ui/legacy/SearchableView.ts",Ao),Bo=t.i18n.getLocalizedString.bind(void 0,Ro);function zo(e){const t=new a.Button.Button;return t.data={variant:"icon",size:"SMALL",jslogContext:e,title:Bo(Ao.clearInput),iconName:"cross-circle-filled"},t.ariaLabel=Bo(Ao.clearInput),t.classList.add("clear-button"),t.tabIndex=-1,t}const Oo=new WeakMap;class Fo{query;caseSensitive;isRegex;constructor(e,t,n){this.query=e,this.caseSensitive=t,this.isRegex=n}toSearchRegex(e){let t=this.caseSensitive?"":"i";e&&(t+="g");const i=this.query.startsWith("/")&&this.query.endsWith("/"),s=this.isRegex&&!i?"/"+this.query+"/":this.query;let r,o=!1;try{/^\/.+\/$/.test(s)&&this.isRegex&&(r=new RegExp(s.substring(1,s.length-1),t),o=!0)}catch(e){}return r||(r=n.StringUtilities.createPlainTextSearchRegex(s,t)),{regex:r,fromQuery:o}}}var Ho=Object.freeze({__proto__:null,SearchableView:class extends lt{searchProvider;replaceProvider;setting;replaceable;footerElementContainer;footerElement;replaceToggleButton;searchInputElement;matchesElement;searchNavigationPrevElement;searchNavigationNextElement;replaceInputElement;caseSensitiveButton;regexButton;replaceButtonElement;replaceAllButtonElement;minimalSearchQuerySize;searchIsVisible;currentQuery;valueChangedTimeoutId;constructor(t,n,i){super(!0),this.registerRequiredCSS(Do),Oo.set(this.element,this),this.searchProvider=t,this.replaceProvider=n,this.setting=i?e.Settings.Settings.instance().createSetting(i,{}):null,this.replaceable=!1,this.contentElement.createChild("slot"),this.footerElementContainer=this.contentElement.createChild("div","search-bar hidden"),this.footerElementContainer.style.order="100",this.footerElement=this.footerElementContainer.createChild("div","toolbar-search"),this.footerElement.setAttribute("jslog",`${s.toolbar("search").track({resize:!0})}`);const r=new Xn("replace-toggle-toolbar",this.footerElement);this.replaceToggleButton=new ii(Bo(Ao.enableFindAndReplace),"replace",void 0,"replace"),or(this.replaceToggleButton.element,Bo(Ao.enableFindAndReplace)),this.replaceToggleButton.addEventListener("Click",this.toggleReplace,this),r.appendToolbarItem(this.replaceToggleButton);const o=this.footerElement.createChild("div","search-inputs"),c=o.createChild("div","icon-and-input"),d=l.Icon.create("search");c.appendChild(d),this.searchInputElement=Zr.create(),this.searchInputElement.type="search",this.searchInputElement.classList.add("search-replace","search"),this.searchInputElement.id="search-input-field",this.searchInputElement.autocomplete="off",this.searchInputElement.placeholder=Bo(Ao.findString),this.searchInputElement.setAttribute("jslog",`${s.textField("search").track({change:!0,keydown:"ArrowUp|ArrowDown|Enter|Escape"})}`),this.searchInputElement.addEventListener("keydown",this.onSearchKeyDown.bind(this),!0),this.searchInputElement.addEventListener("input",this.onInput.bind(this),!1),c.appendChild(this.searchInputElement);const h=o.createChild("div","replace-element input-line");this.replaceInputElement=h.createChild("input","search-replace"),this.replaceInputElement.addEventListener("keydown",this.onReplaceKeyDown.bind(this),!0),this.replaceInputElement.placeholder=Bo(Ao.replace),this.replaceInputElement.setAttribute("jslog",`${s.textField("replace").track({change:!0,keydown:"Enter"})}`);const u=zo("clear-replace-input");u.addEventListener("click",(()=>{this.replaceInputElement.value="",this.replaceInputElement.focus()})),h.appendChild(u);const p=o.createChild("div","search-config-buttons"),g=zo("clear-search-input");if(g.addEventListener("click",(()=>{this.searchInputElement.value="",this.clearSearch(),this.searchInputElement.focus()})),p.appendChild(g),this.searchProvider.supportsRegexSearch()){const e="regular-expression";this.regexButton=new a.Button.Button,this.regexButton.data={variant:"icon_toggle",size:"SMALL",iconName:e,toggledIconName:e,toggleType:"primary-toggle",toggled:!1,jslogContext:e,title:Bo(Ao.enableCaseSensitive)},this.regexButton.addEventListener("click",(()=>this.toggleRegexSearch())),p.appendChild(this.regexButton)}if(this.searchProvider.supportsCaseSensitiveSearch()){const e="match-case";this.caseSensitiveButton=new a.Button.Button,this.caseSensitiveButton.data={variant:"icon_toggle",size:"SMALL",iconName:e,toggledIconName:e,toggled:!1,toggleType:"primary-toggle",title:Bo(Ao.enableCaseSensitive),jslogContext:e},this.caseSensitiveButton.addEventListener("click",(()=>this.toggleCaseSensitiveSearch())),p.appendChild(this.caseSensitiveButton)}o.createChild("div","input-line search-input-background");const m=this.footerElement.createChild("div","toolbar-search-buttons"),b=m.createChild("div","first-row-buttons"),f=new Xn("toolbar-search-options",b);this.searchNavigationPrevElement=new Jn(Bo(Ao.searchPrevious),"chevron-up",void 0,"select-previous"),this.searchNavigationPrevElement.addEventListener("Click",(()=>this.onPrevButtonSearch())),f.appendToolbarItem(this.searchNavigationPrevElement),or(this.searchNavigationPrevElement.element,Bo(Ao.searchPrevious)),this.searchNavigationNextElement=new Jn(Bo(Ao.searchNext),"chevron-down",void 0,"select-next"),this.searchNavigationNextElement.addEventListener("Click",(()=>this.onNextButtonSearch())),or(this.searchNavigationNextElement.element,Bo(Ao.searchNext)),f.appendToolbarItem(this.searchNavigationNextElement);const v=new Zn;this.matchesElement=v.element,this.matchesElement.style.fontVariantNumeric="tabular-nums",this.matchesElement.style.color="var(--sys-color-on-surface-subtle)",this.matchesElement.style.padding="0 var(--sys-size-3)",this.matchesElement.classList.add("search-results-matches"),f.appendToolbarItem(v);const y=new a.Button.Button;y.data={variant:"toolbar",size:"REGULAR",iconName:"cross",title:Bo(Ao.closeSearchBar),jslogContext:"close-search"},y.classList.add("close-search-button"),y.addEventListener("click",(()=>this.closeSearch())),b.appendChild(y);const w=m.createChild("div","second-row-buttons replace-element");this.replaceButtonElement=Vi(Bo(Ao.replace),this.replace.bind(this),{className:"search-action-button",jslogContext:"replace"}),this.replaceButtonElement.disabled=!0,w.appendChild(this.replaceButtonElement),this.replaceAllButtonElement=Vi(Bo(Ao.replaceAll),this.replaceAll.bind(this),{className:"search-action-button",jslogContext:"replace-all"}),w.appendChild(this.replaceAllButtonElement),this.replaceAllButtonElement.disabled=!0,this.minimalSearchQuerySize=3,this.loadSetting()}static fromElement(e){let t=null;for(;e&&!t;)t=Oo.get(e)||null,e=e.parentElementOrShadowHost();return t}toggleCaseSensitiveSearch(){this.caseSensitiveButton&&(this.caseSensitiveButton.title=this.caseSensitiveButton.toggled?Bo(Ao.disableCaseSensitive):Bo(Ao.enableCaseSensitive)),this.saveSetting(),this.performSearch(!1,!0)}toggleRegexSearch(){this.regexButton&&(this.regexButton.title=this.regexButton.toggled?Bo(Ao.disableRegularExpression):Bo(Ao.enableRegularExpression)),this.saveSetting(),this.performSearch(!1,!0)}toggleReplace(){const e=!this.replaceToggleButton.toggled();this.replaceToggleButton.setToggled(e);const t=Bo(e?Ao.disableFindAndReplace:Ao.enableFindAndReplace);or(this.replaceToggleButton.element,t),this.replaceToggleButton.element.title=t,this.updateSecondRowVisibility()}saveSetting(){if(!this.setting)return;const e=this.setting.get()||{};this.caseSensitiveButton&&(e.caseSensitive=this.caseSensitiveButton.toggled),this.regexButton&&(e.isRegex=this.regexButton.toggled),this.setting.set(e)}loadSetting(){const e=this.setting&&this.setting.get()||{};if(this.searchProvider.supportsCaseSensitiveSearch()&&this.caseSensitiveButton){this.caseSensitiveButton.toggled=Boolean(e.caseSensitive);const t=e.caseSensitive?Bo(Ao.disableCaseSensitive):Bo(Ao.enableCaseSensitive);this.caseSensitiveButton.title=t,or(this.caseSensitiveButton,t)}if(this.searchProvider.supportsRegexSearch()&&this.regexButton){this.regexButton.toggled=Boolean(e.isRegex);const t=e.regular?Bo(Ao.disableRegularExpression):Bo(Ao.enableRegularExpression);this.regexButton.title=t,or(this.regexButton,t)}}setMinimalSearchQuerySize(e){this.minimalSearchQuerySize=e}setPlaceholder(e,t){this.searchInputElement.placeholder=e,t&&or(this.searchInputElement,t)}setReplaceable(e){this.replaceable=e}updateSearchMatchesCount(e){const t=this.searchProvider;t.currentSearchMatches!==e&&(t.currentSearchMatches=e,this.updateSearchMatchesCountAndCurrentMatchIndex(t.currentQuery?e:0,-1))}updateCurrentMatchIndex(e){const t=this.searchProvider;this.updateSearchMatchesCountAndCurrentMatchIndex(t.currentSearchMatches,e)}isSearchVisible(){return Boolean(this.searchIsVisible)}closeSearch(){this.cancelSearch(),this.footerElementContainer.hasFocus()&&this.focus(),this.searchProvider.onSearchClosed?.()}toggleSearchBar(e){this.footerElementContainer.classList.toggle("hidden",!e),this.doResize()}cancelSearch(){this.searchIsVisible&&(this.resetSearch(),delete this.searchIsVisible,this.toggleSearchBar(!1))}resetSearch(){this.clearSearch(),this.updateReplaceVisibility(),this.matchesElement.textContent=""}refreshSearch(){this.searchIsVisible&&(this.resetSearch(),this.performSearch(!1,!1))}handleFindNextShortcut(){return!!this.searchIsVisible&&(this.searchProvider.jumpToNextSearchResult(),!0)}handleFindPreviousShortcut(){return!!this.searchIsVisible&&(this.searchProvider.jumpToPreviousSearchResult(),!0)}handleFindShortcut(){return this.showSearchField(),!0}handleCancelSearchShortcut(){return!!this.searchIsVisible&&(this.closeSearch(),!0)}updateSearchNavigationButtonState(e){this.replaceButtonElement.disabled=!e,this.replaceAllButtonElement.disabled=!e,this.searchNavigationPrevElement.setEnabled(e),this.searchNavigationNextElement.setEnabled(e)}updateSearchMatchesCountAndCurrentMatchIndex(e,t){this.currentQuery?0===e||t>=0?(this.matchesElement.textContent=Bo(Ao.dOfD,{PH1:t+1,PH2:e}),or(this.matchesElement,Bo(Ao.accessibledOfD,{PH1:t+1,PH2:e}))):this.matchesElement.textContent=1===e?Bo(Ao.matchString):Bo(Ao.dMatches,{PH1:e}):this.matchesElement.textContent="",this.updateSearchNavigationButtonState(e>0)}showSearchField(){let e;if(this.searchIsVisible&&this.cancelSearch(),!this.searchInputElement.hasFocus()){const t=an.instance().element.window().getSelection();t&&t.rangeCount&&(e=t.toString().replace(/\r?\n.*/,""))}this.toggleSearchBar(!0),this.updateReplaceVisibility(),e&&(this.searchInputElement.value=e),this.performSearch(!1,!1),this.searchInputElement.focus(),this.searchInputElement.select(),this.searchIsVisible=!0}updateReplaceVisibility(){this.replaceToggleButton.setVisible(this.replaceable),this.replaceable||(this.replaceToggleButton.setToggled(!1),this.updateSecondRowVisibility())}onSearchKeyDown(e){const t=e;if(n.KeyboardUtilities.isEscKey(t))return this.closeSearch(),void t.consume(!0);"Enter"===t.key&&(this.currentQuery?this.jumpToNextSearchResult(t.shiftKey):this.performSearch(!0,!0,t.shiftKey))}onReplaceKeyDown(e){"Enter"===e.key&&this.replace()}jumpToNextSearchResult(e){this.currentQuery&&(e?this.searchProvider.jumpToPreviousSearchResult():this.searchProvider.jumpToNextSearchResult())}onNextButtonSearch(){this.jumpToNextSearchResult()}onPrevButtonSearch(){this.jumpToNextSearchResult(!0)}clearSearch(){const e=this.searchProvider;delete this.currentQuery,Boolean(e.currentQuery)&&(delete e.currentQuery,this.searchProvider.onSearchCanceled()),this.updateSearchMatchesCountAndCurrentMatchIndex(0,-1)}performSearch(e,t,n){const i=this.searchInputElement.value;if(!i||!e&&i.length<this.minimalSearchQuerySize&&!this.currentQuery)return void this.clearSearch();this.currentQuery=i,this.searchProvider.currentQuery=i;const s=this.currentSearchConfig();this.searchProvider.performSearch(s,t,n)}currentSearchConfig(){const e=this.searchInputElement.value,t=!!this.caseSensitiveButton&&this.caseSensitiveButton.toggled,n=!!this.regexButton&&this.regexButton.toggled;return new Fo(e,t,n)}updateSecondRowVisibility(){const e=this.replaceToggleButton.toggled();this.footerElementContainer.classList.toggle("replaceable",e),e?this.replaceInputElement.focus():this.searchInputElement.focus(),this.doResize()}replace(){if(!this.replaceProvider)throw new Error("No 'replacable' provided to SearchableView!");const e=this.currentSearchConfig();this.replaceProvider.replaceSelectionWith(e,this.replaceInputElement.value),delete this.currentQuery,this.performSearch(!0,!0)}replaceAll(){if(!this.replaceProvider)throw new Error("No 'replacable' provided to SearchableView!");const e=this.currentSearchConfig();this.replaceProvider.replaceAllWith(e,this.replaceInputElement.value)}onInput(){if(!e.Settings.Settings.instance().moduleSetting("search-as-you-type").get())return void this.clearSearch();this.valueChangedTimeoutId&&clearTimeout(this.valueChangedTimeoutId);const t=this.searchInputElement.value.length<3?200:0;this.valueChangedTimeoutId=window.setTimeout(this.onValueChanged.bind(this),t)}onValueChanged(){this.searchIsVisible&&(delete this.valueChangedTimeoutId,this.performSearch(!1,!0))}},SearchConfig:Fo}),Wo={cssContent:".item.disabled{opacity:50%}.item-list{background-color:var(--sys-color-cdt-base-container);box-shadow:var(--drop-shadow);overflow-x:hidden;overflow-y:auto;width:100%}.item.highlighted{background-color:var(--sys-color-state-hover-on-subtle)}@media (forced-colors: active){.item.disabled{opacity:100%}.item-list{border:1px solid ButtonText;background-color:ButtonFace}.item.highlighted{forced-color-adjust:none;color:HighlightText;background-color:Highlight}}\n/*# sourceURL=softDropDown.css */\n"},No={cssContent:"button.soft-dropdown{height:var(--sys-size-9);text-align:left;position:relative;border:none;background:none;max-width:120px;devtools-icon{top:1px}}button.soft-dropdown[disabled]{background:var(--sys-color-state-disabled-container);color:var(--sys-color-state-disabled)}button.soft-dropdown > .title{flex:0 1 auto;overflow:hidden;text-overflow:ellipsis}button.soft-dropdown:hover:not(:active) > .title{color:var(--sys-color-on-surface)}@media (forced-colors: active){button.soft-dropdown{border:1px solid ButtonText;background:ButtonFace;color:ButtonText}button.soft-dropdown[disabled]{opacity:100%}}\n/*# sourceURL=softDropDownButton.css */\n"};const jo={noItemSelected:"(no item selected)"},Vo=t.i18n.registerUIStrings("ui/legacy/SoftDropDown.ts",jo),Uo=t.i18n.getLocalizedString.bind(void 0,Vo);var _o=Object.freeze({__proto__:null,SoftDropDown:class{delegate;selectedItem;model;placeholderText;element;titleElement;glassPane;list;rowHeight;width;listWasShowing200msAgo;constructor(e,t,n){this.delegate=t,this.selectedItem=null,this.model=e,this.placeholderText=Uo(jo.noItemSelected),this.element=document.createElement("button"),n&&this.element.setAttribute("jslog",`${s.dropDown().track({click:!0,keydown:"ArrowUp|ArrowDown|Enter"}).context(n)}`),this.element.classList.add("soft-dropdown"),c.ThemeSupport.instance().appendStyle(this.element,No),this.titleElement=this.element.createChild("span","title");const i=l.Icon.create("triangle-down");this.element.appendChild(i),Qs(this.element,!1),this.glassPane=new ms,this.glassPane.setMarginBehavior("NoMargin"),this.glassPane.setAnchorBehavior("PreferBottom"),this.glassPane.setOutsideClickCallback(this.hide.bind(this)),this.glassPane.setPointerEventsBehavior("BlockedByGlassPane"),this.list=new Dn(e,this,Mn.EqualHeightItems),this.list.element.classList.add("item-list"),this.rowHeight=36,this.width=315,ds(this.glassPane.contentElement,{cssFile:Wo,delegatesFocus:void 0}).appendChild(this.list.element),Os(this.list.element),s.setMappedParent(this.list.element,this.element),this.list.element.setAttribute("jslog",`${s.menu().parent("mapped").track({resize:!0,keydown:"ArrowUp|ArrowDown|PageUp|PageDown"})}`),this.listWasShowing200msAgo=!1,this.element.addEventListener("mousedown",(e=>{this.listWasShowing200msAgo?this.hide(e):this.element.disabled||this.show(e)}),!1),this.element.addEventListener("keydown",this.onKeyDownButton.bind(this),!1),this.list.element.addEventListener("keydown",this.onKeyDownList.bind(this),!1),this.list.element.addEventListener("focusout",this.hide.bind(this),!1),this.list.element.addEventListener("mousedown",(e=>e.consume(!0)),!1),this.list.element.addEventListener("mouseup",(e=>{e.target!==this.list.element&&this.listWasShowing200msAgo&&(this.selectHighlightedItem(),e.target instanceof Element&&e.target?.parentElement&&s.logClick(e.target.parentElement,e),this.hide(e))}),!1),e.addEventListener("ItemsReplaced",this.itemsReplaced,this)}show(e){this.glassPane.isShowing()||(this.glassPane.setContentAnchorBox(this.element.boxInWindow()),this.glassPane.show(this.element.ownerDocument),this.list.element.focus(),Qs(this.element,!0),this.updateGlasspaneSize(),this.selectedItem&&this.list.selectItem(this.selectedItem),e.consume(!0),window.setTimeout((()=>{this.listWasShowing200msAgo=!0}),200))}updateGlasspaneSize(){const e=this.rowHeight*Math.min(this.model.length,9);this.glassPane.setMaxContentSize(new _(this.width,e)),this.list.viewportResized()}hide(e){window.setTimeout((()=>{this.listWasShowing200msAgo=!1}),200),this.glassPane.hide(),this.list.selectItem(null),Qs(this.element,!1),this.element.focus(),e.consume(!0)}onKeyDownButton(e){const t=e;let n=!1;switch(t.key){case"ArrowUp":this.show(t),this.list.selectItemNextPage(),n=!0;break;case"ArrowDown":this.show(t),this.list.selectItemPreviousPage(),n=!0;break;case"Enter":case" ":this.show(t),n=!0}n&&t.consume(!0)}onKeyDownList(e){const t=e;let n=!1;switch(t.key){case"ArrowLeft":n=this.list.selectPreviousItem(!1,!1);break;case"ArrowRight":n=this.list.selectNextItem(!1,!1);break;case"Home":for(let e=0;e<this.model.length;e++)if(this.isItemSelectable(this.model.at(e))){this.list.selectItem(this.model.at(e)),n=!0;break}break;case"End":for(let e=this.model.length-1;e>=0;e--)if(this.isItemSelectable(this.model.at(e))){this.list.selectItem(this.model.at(e)),n=!0;break}break;case"Escape":this.hide(t),n=!0;break;case"Tab":case"Enter":case" ":this.selectHighlightedItem(),this.hide(t),n=!0;break;default:if(1===t.key.length){const e=this.list.selectedIndex(),i=t.key.toUpperCase();for(let t=0;t<this.model.length;t++){const n=this.model.at((e+t+1)%this.model.length);if(this.delegate.titleFor(n).toUpperCase().startsWith(i)){this.list.selectItem(n);break}}n=!0}}n&&t.consume(!0)}setWidth(e){this.width=e,this.updateGlasspaneSize()}setRowHeight(e){this.rowHeight=e}setPlaceholderText(e){this.placeholderText=e,this.selectedItem||(this.titleElement.textContent=this.placeholderText)}itemsReplaced(e){const{removed:t}=e.data;this.selectedItem&&-1!==t.indexOf(this.selectedItem)&&(this.selectedItem=null,this.selectHighlightedItem()),this.updateGlasspaneSize()}getSelectedItem(){return this.selectedItem}selectItem(e){this.selectedItem=e,this.selectedItem?this.titleElement.textContent=this.delegate.titleFor(this.selectedItem):this.titleElement.textContent=this.placeholderText,this.delegate.itemSelected(this.selectedItem)}createElementForItem(e){const t=document.createElement("div");return t.classList.add("item"),t.addEventListener("mousemove",(t=>{(t.movementX||t.movementY)&&this.delegate.isItemSelectable(e)&&this.list.selectItem(e,!1,!0)})),t.classList.toggle("disabled",!this.delegate.isItemSelectable(e)),t.classList.toggle("highlighted",this.list.selectedItem()===e),Fs(t),t.appendChild(this.delegate.createElementForItem(e)),t}heightForItem(e){return this.rowHeight}isItemSelectable(e){return this.delegate.isItemSelectable(e)}selectedItemChanged(e,t,n,i){n&&n.classList.remove("highlighted"),i&&i.classList.add("highlighted"),lr(this.list.element,i),this.delegate.highlightedItemChanged(e,t,n&&n.firstElementChild,i&&i.firstElementChild)}updateSelectedItemARIA(e,t){return!1}selectHighlightedItem(){this.selectItem(this.list.selectedItem())}refreshItem(e){this.list.refreshItem(e)}}}),Ko={cssContent:".widget{padding:25px}.message{font-size:larger;white-space:pre;margin:5px}\n/*# sourceURL=targetCrashedScreen.css */\n"};const qo={devtoolsWasDisconnectedFromThe:"DevTools was disconnected from the page.",oncePageIsReloadedDevtoolsWill:"Once page is reloaded, DevTools will automatically reconnect."},Go=t.i18n.registerUIStrings("ui/legacy/TargetCrashedScreen.ts",qo),$o=t.i18n.getLocalizedString.bind(void 0,Go);var Xo=Object.freeze({__proto__:null,TargetCrashedScreen:class extends lt{hideCallback;constructor(e){super(!0),this.registerRequiredCSS(Ko),this.contentElement.createChild("div","message").textContent=$o(qo.devtoolsWasDisconnectedFromThe),this.contentElement.createChild("div","message").textContent=$o(qo.oncePageIsReloadedDevtoolsWill),this.hideCallback=e}willHide(){this.hideCallback.call(null)}}});var Yo=Object.freeze({__proto__:null,ThrottledWidget:class extends lt{updateThrottler;updateWhenVisible;lastUpdatePromise=Promise.resolve();constructor(t,n){super(t),this.updateThrottler=new e.Throttler.Throttler(void 0===n?100:n),this.updateWhenVisible=!1}doUpdate(){return Promise.resolve()}update(){this.updateWhenVisible=!this.isShowing(),this.updateWhenVisible||(this.lastUpdatePromise=this.updateThrottler.schedule((()=>this.isShowing()?this.doUpdate():(this.updateWhenVisible=!0,Promise.resolve()))))}wasShown(){super.wasShown(),this.updateWhenVisible&&this.update()}}}),Qo={cssContent:':host{flex:1 1 auto;padding:2px 0 0}.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow){min-width:100%;display:inline-block}.tree-outline{padding:0 0 4px 4px;margin:0;z-index:0;position:relative}.tree-outline:focus-visible{box-shadow:0 0 0 2px var(--sys-color-state-focus-ring) inset}.tree-outline li .selection{display:none;z-index:-1;margin-left:-10000px}.tree-outline:not(.hide-selection-when-blurred) li.selected{color:var(--sys-color-on-surface-subtle)}.tree-outline:not(.hide-selection-when-blurred) li.selected .selection{display:block;background-color:var(--sys-color-neutral-container)}.tree-outline:not(.hide-selection-when-blurred) li.elements-drag-over .selection{display:block;margin-top:-2px;border-top:2px solid;border-top-color:var(--sys-color-tonal-container)}.tree-outline li:hover:not(:has(span[is="dt-checkbox"])) .selection{display:block;background-color:var(--sys-color-state-hover-on-subtle)}.tree-outline:not(.hide-selection-when-blurred) li.hovered:not(.selected) .selection{display:block;left:3px;right:3px;background-color:var(--sys-color-state-hover-on-subtle);border-radius:5px}.tree-outline:not(.hide-selection-when-blurred) li.in-clipboard .highlight{outline:1px dotted var(--sys-color-neutral-outline)}ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection{background-color:var(--sys-color-tonal-container)}ol.tree-outline,\n.tree-outline ol{list-style-type:none}.tree-outline ol{padding-left:12px}.tree-outline li{text-overflow:ellipsis;white-space:nowrap;position:relative;display:flex;align-items:center;min-height:16px}ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus{color:var(--sys-color-on-tonal-container);& ::selection{background-color:var(--sys-color-state-focus-select)}& *:not(devtools-icon){color:inherit}}.tree-outline li .icons-container{align-self:center;display:flex;align-items:center}.tree-outline li .leading-icons{margin-right:4px}.tree-outline li .trailing-icons{margin-left:4px}.tree-outline li::before{user-select:none;mask-image:var(--image-file-triangle-right);background-color:var(--icon-default);content:"\\A0\\A0";text-shadow:none;margin-right:-2px;height:14px;width:14px;transition:transform 200ms}.tree-outline li:not(.parent)::before{background-color:transparent}.tree-outline li.parent.expanded::before{transform:rotate(90deg)}.tree-outline ol.children{display:none}.tree-outline ol.children.expanded{display:block}.tree-outline.tree-outline-dense li{margin-top:1px;min-height:12px}.tree-outline.tree-outline-dense li.parent{margin-top:0}.tree-outline.tree-outline-dense li.parent::before{top:0}.tree-outline.tree-outline-dense ol{padding-left:10px}.tree-outline.hide-selection-when-blurred .selected:focus-visible{background:var(--sys-color-state-focus-highlight);border-radius:2px}.tree-outline-disclosure:not(.tree-outline-disclosure-hide-overflow) .tree-outline.hide-selection-when-blurred .selected:focus-visible{width:fit-content;padding-right:3px}@media (forced-colors: active){.tree-outline-disclosure li.parent::before,\n  .tree-outline:not(.hide-selection-when-blurred) li.parent:not(.selected)::before{forced-color-adjust:none;background-color:ButtonText}.tree-outline li devtools-icon{forced-color-adjust:none;color:ButtonText}.tree-outline-disclosure li.parent:hover:not(.selected)::before,\n  .tree-outline:not(.hide-selection-when-blurred) li.parent:hover:not(.selected)::before{background-color:ButtonText}.tree-outline:not(.hide-selection-when-blurred) li.selected .selection{forced-color-adjust:none;background-color:ButtonText}ol.tree-outline:not(.hide-selection-when-blurred) li.selected:focus .selection,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible{forced-color-adjust:none;background-color:Highlight}ol.tree-outline:not(.hide-selection-when-blurred) li.parent.selected::before,\n  ol.tree-outline:not(.hide-selection-when-blurred) li.parent.selected:focus::before,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible::before{background-color:HighlightText}.tree-outline li:not(.parent)::before,\n  .tree-outline li:not(.parent):hover::before,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible:not(.parent)::before{forced-color-adjust:none;background-color:transparent}.tree-outline:not(.hide-selection-when-blurred) devtools-icon,\n  .tree-outline.hide-selection-when-blurred devtools-icon{color:ButtonText}.tree-outline li.selected devtools-icon,\n  .tree-outline li.selected:focus devtools-icon{color:HighlightText!important}ol.tree-outline:not(.hide-selection-when-blurred) li.selected,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected .tree-element-title,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected:focus,\n  .tree-outline:not(.hide-selection-when-blurred) li:focus-visible .tree-element-title,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected:focus .tree-element-title,\n  .tree-outline:not(.hide-selection-when-blurred) li.selected span,\n  .tree-outline.hide-selection-when-blurred .selected:focus-visible span{forced-color-adjust:none;color:HighlightText}.tree-outline:not(.hide-selection-when-blurred) li.selected:focus-visible devtools-adorner,\n  .tree-outline.hide-selection-when-blurred li.selected:focus-visible devtools-adorner{--override-adorner-background-color:Highlight;--override-adorner-border-color:HighlightText}}\n/*# sourceURL=treeoutline.css */\n'};const Zo=new WeakMap;var Jo;!function(e){e.ElementAttached="ElementAttached",e.ElementsDetached="ElementsDetached",e.ElementExpanded="ElementExpanded",e.ElementCollapsed="ElementCollapsed",e.ElementSelected="ElementSelected"}(Jo||(Jo={}));class ea extends e.ObjectWrapper.ObjectWrapper{rootElementInternal;renderSelection;selectedTreeElement;expandTreeElementsWhenArrowing;comparator;contentElement;preventTabOrder;showSelectionOnKeyboardFocus;focusable;element;useLightSelectionColor;treeElementToScrollIntoView;centerUponScrollIntoView;constructor(){super(),this.rootElementInternal=this.createRootElement(),this.renderSelection=!1,this.selectedTreeElement=null,this.expandTreeElementsWhenArrowing=!1,this.comparator=null,this.contentElement=this.rootElementInternal.childrenListNode,this.contentElement.addEventListener("keydown",this.treeKeyDown.bind(this),!1),this.preventTabOrder=!1,this.showSelectionOnKeyboardFocus=!1,this.focusable=!0,this.setFocusable(!0),this.element=this.contentElement,this.element.setAttribute("jslog",`${s.tree()}`),Rs(this.element),this.useLightSelectionColor=!1,this.treeElementToScrollIntoView=null,this.centerUponScrollIntoView=!1}setShowSelectionOnKeyboardFocus(e,t){this.contentElement.classList.toggle("hide-selection-when-blurred",e),this.preventTabOrder=Boolean(t),this.focusable&&(this.contentElement.tabIndex=Boolean(t)?-1:0),this.showSelectionOnKeyboardFocus=e}createRootElement(){const e=new na;return e.treeOutline=this,e.root=!0,e.selectable=!1,e.expanded=!0,e.childrenListNode.classList.remove("children"),e}rootElement(){return this.rootElementInternal}firstChild(){return this.rootElementInternal.firstChild()}lastDescendent(){let e=this.rootElementInternal.lastChild();for(;e&&e.expanded&&e.childCount();)e=e.lastChild();return e}appendChild(e,t){this.rootElementInternal.appendChild(e,t)}insertChild(e,t){this.rootElementInternal.insertChild(e,t)}removeChild(e){this.rootElementInternal.removeChild(e)}removeChildren(){this.rootElementInternal.removeChildren()}treeElementFromPoint(e,t){const n=ss(this.contentElement.ownerDocument,e,t);if(!n)return null;const i=is(n,["ol","li"]);return i&&(Zo.get(i)||ta.get(i))||null}treeElementFromEvent(e){return e?this.treeElementFromPoint(e.pageX,e.pageY):null}setComparator(e){this.comparator=e}setFocusable(e){this.focusable=e,this.updateFocusable()}updateFocusable(){this.focusable?(this.contentElement.tabIndex=this.preventTabOrder||Boolean(this.selectedTreeElement)?-1:0,this.selectedTreeElement&&this.selectedTreeElement.setFocusable(!0)):(this.contentElement.removeAttribute("tabIndex"),this.selectedTreeElement&&this.selectedTreeElement.setFocusable(!1))}focus(){this.selectedTreeElement?this.selectedTreeElement.listItemElement.focus():this.contentElement.focus()}setUseLightSelectionColor(e){this.useLightSelectionColor=e}getUseLightSelectionColor(){return this.useLightSelectionColor}bindTreeElement(e){e.treeOutline&&console.error("Binding element for the second time: "+(new Error).stack),e.treeOutline=this,e.onbind()}unbindTreeElement(e){e.treeOutline||console.error("Unbinding element that was not bound: "+(new Error).stack),e.deselect(),e.onunbind(),e.treeOutline=null}selectPrevious(){let e=this.selectedTreeElement&&this.selectedTreeElement.traversePreviousTreeElement(!0);for(;e&&!e.selectable;)e=e.traversePreviousTreeElement(!this.expandTreeElementsWhenArrowing);return!!e&&(e.select(!1,!0),!0)}selectNext(){let e=this.selectedTreeElement&&this.selectedTreeElement.traverseNextTreeElement(!0);for(;e&&!e.selectable;)e=e.traverseNextTreeElement(!this.expandTreeElementsWhenArrowing);return!!e&&(e.select(!1,!0),!0)}forceSelect(e=!1,t=!0){this.selectedTreeElement&&this.selectedTreeElement.deselect(),this.selectFirst(e,t)}selectFirst(e=!1,t=!0){let n=this.firstChild();for(;n&&!n.selectable;)n=n.traverseNextTreeElement(!0);return!!n&&(n.select(e,t),!0)}selectLast(){let e=this.lastDescendent();for(;e&&!e.selectable;)e=e.traversePreviousTreeElement(!0);return!!e&&(e.select(!1,!0),!0)}treeKeyDown(e){if(e.shiftKey||e.metaKey||e.ctrlKey||vi())return;let t=!1;this.selectedTreeElement?"ArrowUp"!==e.key||e.altKey?"ArrowDown"!==e.key||e.altKey?"ArrowLeft"===e.key?t=this.selectedTreeElement.collapseOrAscend(e.altKey):"ArrowRight"===e.key?this.selectedTreeElement.revealed()?t=this.selectedTreeElement.descendOrExpand(e.altKey):(this.selectedTreeElement.reveal(),t=!0):8===e.keyCode||46===e.keyCode?t=this.selectedTreeElement.ondelete():"Enter"===e.key?t=this.selectedTreeElement.onenter():e.keyCode===be.Space.code?t=this.selectedTreeElement.onspace():"Home"===e.key?t=this.selectFirst():"End"===e.key&&(t=this.selectLast()):t=this.selectNext():t=this.selectPrevious():"ArrowUp"!==e.key||e.altKey?"ArrowDown"!==e.key||e.altKey||(t=this.selectFirst()):t=this.selectLast(),t&&e.consume(!0)}deferredScrollIntoView(e,t){const n=()=>{if(!this.treeElementToScrollIntoView)return;const e=this.treeElementToScrollIntoView.listItemElement.getBoundingClientRect(),t=this.contentElement.getBoundingClientRect();let n=this.element;for(;"visible"===getComputedStyle(n).overflow&&n.parentElement;)n=n.parentElement;const i=n.getBoundingClientRect(),s=i.left-t.left,r=i.top-t.top+this.contentElement.offsetTop;let o=e.left-t.left;o>s&&o<s+i.width?o=s:this.centerUponScrollIntoView&&(o-=i.width/2);let a=e.top-t.top;a>r&&a<r+i.height?a=r:this.centerUponScrollIntoView&&(a-=i.height/2),n.scrollTo(o,a),this.treeElementToScrollIntoView=null};this.treeElementToScrollIntoView||this.element.window().requestAnimationFrame(n),this.treeElementToScrollIntoView=e,this.centerUponScrollIntoView=t}onStartedEditingTitle(e){}}const ta=new WeakMap;class na{treeOutline;parent;previousSibling;nextSibling;boundOnFocus;boundOnBlur;listItemNode;titleElement;titleInternal;childrenInternal;childrenListNode;expandLoggable={};hiddenInternal;selectableInternal;expanded;selected;expandable;#a=!0;collapsible;toggleOnClick;button;root;tooltipInternal;leadingIconsElement;trailingIconsElement;selectionElementInternal;disableSelectFocus;constructor(e,t,n){this.treeOutline=null,this.parent=null,this.previousSibling=null,this.nextSibling=null,this.boundOnFocus=this.onFocus.bind(this),this.boundOnBlur=this.onBlur.bind(this),this.listItemNode=document.createElement("li"),this.titleElement=this.listItemNode.createChild("span","tree-element-title"),ta.set(this.listItemNode,this),this.titleInternal="",e&&(this.title=e),this.listItemNode.addEventListener("mousedown",this.handleMouseDown.bind(this),!1),this.listItemNode.addEventListener("click",this.treeElementToggled.bind(this),!1),this.listItemNode.addEventListener("dblclick",this.handleDoubleClick.bind(this),!1),this.listItemNode.setAttribute("jslog",`${s.treeItem().parent("parentTreeItem").context(n).track({click:!0,keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Backspace|Delete|Enter|Space|Home|End"})}`),Bs(this.listItemNode),this.childrenInternal=null,this.childrenListNode=document.createElement("ol"),Zo.set(this.childrenListNode,this),this.childrenListNode.classList.add("children"),Ls(this.childrenListNode),this.hiddenInternal=!1,this.selectableInternal=!0,this.expanded=!1,this.selected=!1,this.setExpandable(t||!1),this.collapsible=!0,this.toggleOnClick=!1,this.button=null,this.root=!1,this.tooltipInternal="",this.leadingIconsElement=null,this.trailingIconsElement=null,this.selectionElementInternal=null,this.disableSelectFocus=!1}static getTreeElementBylistItemNode(e){return ta.get(e)}hasAncestor(e){if(!e)return!1;let t=this.parent;for(;t;){if(e===t)return!0;t=t.parent}return!1}hasAncestorOrSelf(e){return this===e||this.hasAncestor(e)}isHidden(){if(this.hidden)return!0;let e=this.parent;for(;e;){if(e.hidden)return!0;e=e.parent}return!1}children(){return this.childrenInternal||[]}childCount(){return this.childrenInternal?this.childrenInternal.length:0}firstChild(){return this.childrenInternal?this.childrenInternal[0]:null}lastChild(){return this.childrenInternal?this.childrenInternal[this.childrenInternal.length-1]:null}childAt(e){return this.childrenInternal?this.childrenInternal[e]:null}indexOfChild(e){return this.childrenInternal?this.childrenInternal.indexOf(e):-1}appendChild(e,t){let i;this.childrenInternal||(this.childrenInternal=[]),i=t?n.ArrayUtilities.lowerBound(this.childrenInternal,e,t):this.treeOutline&&this.treeOutline.comparator?n.ArrayUtilities.lowerBound(this.childrenInternal,e,this.treeOutline.comparator):this.childrenInternal.length,this.insertChild(e,i)}insertChild(e,t){if(this.childrenInternal||(this.childrenInternal=[]),!e)throw"child can't be undefined or null";console.assert(!e.parent,"Attempting to insert a child that is already in the tree, reparenting is not supported.");const n=t>0?this.childrenInternal[t-1]:null;n?(n.nextSibling=e,e.previousSibling=n):e.previousSibling=null;const i=this.childrenInternal[t];i?(i.previousSibling=e,e.nextSibling=i):e.nextSibling=null,this.childrenInternal.splice(t,0,e),this.setExpandable(!0),e.parent=this,this.treeOutline&&this.treeOutline.bindTreeElement(e);for(let t=e.firstChild();this.treeOutline&&t;t=t.traverseNextTreeElement(!1,e,!0))this.treeOutline.bindTreeElement(t);e.onattach(),e.ensureSelection(),this.treeOutline&&this.treeOutline.dispatchEventToListeners(Jo.ElementAttached,e);const s=e.nextSibling?e.nextSibling.listItemNode:null;this.childrenListNode.insertBefore(e.listItemNode,s),this.childrenListNode.insertBefore(e.childrenListNode,s),e.selected&&e.select(),e.expanded&&e.expand()}removeChildAtIndex(e){if(!this.childrenInternal||e<0||e>=this.childrenInternal.length)throw"childIndex out of range";const t=this.childrenInternal[e];this.childrenInternal.splice(e,1);const n=t.parent;this.treeOutline&&this.treeOutline.selectedTreeElement&&this.treeOutline.selectedTreeElement.hasAncestorOrSelf(t)&&(t.nextSibling?t.nextSibling.select(!0):t.previousSibling?t.previousSibling.select(!0):n&&n.select(!0)),t.previousSibling&&(t.previousSibling.nextSibling=t.nextSibling),t.nextSibling&&(t.nextSibling.previousSibling=t.previousSibling),t.parent=null,this.treeOutline&&this.treeOutline.unbindTreeElement(t);for(let e=t.firstChild();this.treeOutline&&e;e=e.traverseNextTreeElement(!1,t,!0))this.treeOutline.unbindTreeElement(e);t.detach(),this.treeOutline&&this.treeOutline.dispatchEventToListeners(Jo.ElementsDetached)}removeChild(e){if(!e)throw"child can't be undefined or null";if(e.parent!==this)return;const t=this.childrenInternal?this.childrenInternal.indexOf(e):-1;if(-1===t)throw"child not found in this node's children";this.removeChildAtIndex(t)}removeChildren(){if(!this.root&&this.treeOutline&&this.treeOutline.selectedTreeElement&&this.treeOutline.selectedTreeElement.hasAncestorOrSelf(this)&&this.select(!0),this.childrenInternal)for(const e of this.childrenInternal){e.previousSibling=null,e.nextSibling=null,e.parent=null,this.treeOutline&&this.treeOutline.unbindTreeElement(e);for(let t=e.firstChild();this.treeOutline&&t;t=t.traverseNextTreeElement(!1,e,!0))this.treeOutline.unbindTreeElement(t);e.detach()}this.childrenInternal=[],this.treeOutline&&this.treeOutline.dispatchEventToListeners(Jo.ElementsDetached)}get selectable(){return!this.isHidden()&&this.selectableInternal}set selectable(e){this.selectableInternal=e}get listItemElement(){return this.listItemNode}get childrenListElement(){return this.childrenListNode}get title(){return this.titleInternal}set title(e){this.titleInternal!==e&&(this.titleInternal=e,"string"==typeof e?(this.titleElement.textContent=e,this.tooltip=e):(this.titleElement=e,this.tooltip=""),this.listItemNode.removeChildren(),this.leadingIconsElement&&this.listItemNode.appendChild(this.leadingIconsElement),this.listItemNode.appendChild(this.titleElement),this.trailingIconsElement&&this.listItemNode.appendChild(this.trailingIconsElement),this.ensureSelection())}titleAsText(){return this.titleInternal?"string"==typeof this.titleInternal?this.titleInternal:this.titleInternal.textContent||"":""}startEditingTitle(e){to.startEditing(this.titleElement,e),this.treeOutline&&this.treeOutline.onStartedEditingTitle(this)}setLeadingIcons(e){if(this.leadingIconsElement||e.length){this.leadingIconsElement||(this.leadingIconsElement=document.createElement("div"),this.leadingIconsElement.classList.add("leading-icons"),this.leadingIconsElement.classList.add("icons-container"),this.listItemNode.insertBefore(this.leadingIconsElement,this.titleElement),this.ensureSelection()),this.leadingIconsElement.removeChildren();for(const t of e)this.leadingIconsElement.appendChild(t)}}setTrailingIcons(e){if(this.trailingIconsElement||e.length){this.trailingIconsElement||(this.trailingIconsElement=document.createElement("div"),this.trailingIconsElement.classList.add("trailing-icons"),this.trailingIconsElement.classList.add("icons-container"),this.listItemNode.appendChild(this.trailingIconsElement),this.ensureSelection()),this.trailingIconsElement.removeChildren();for(const t of e)this.trailingIconsElement.appendChild(t)}}get tooltip(){return this.tooltipInternal}set tooltip(e){this.tooltipInternal!==e&&(this.tooltipInternal=e,wt.install(this.listItemNode,e))}isExpandable(){return this.expandable}setExpandable(e){this.expandable!==e&&(this.expandable=e,this.listItemNode.classList.toggle("parent",e),e?(s.registerLoggable(this.expandLoggable,`${s.expand()}`,this.listItemNode),Qs(this.listItemNode,!1)):(this.collapse(),Zs(this.listItemNode)))}isExpandRecursively(){return this.#a}setExpandRecursively(e){this.#a=e}isCollapsible(){return this.collapsible}setCollapsible(e){this.collapsible!==e&&(this.collapsible=e,this.listItemNode.classList.toggle("always-parent",!e),e||this.expand())}get hidden(){return this.hiddenInternal}set hidden(e){if(this.hiddenInternal!==e&&(this.hiddenInternal=e,this.listItemNode.classList.toggle("hidden",e),this.childrenListNode.classList.toggle("hidden",e),e&&this.treeOutline&&this.treeOutline.selectedTreeElement&&this.treeOutline.selectedTreeElement.hasAncestorOrSelf(this))){const e=this.treeOutline.selectedTreeElement.listItemElement.hasFocus();this.treeOutline.forceSelect(!e,!1)}}invalidateChildren(){this.childrenInternal&&(this.removeChildren(),this.childrenInternal=null)}ensureSelection(){this.treeOutline&&this.treeOutline.renderSelection&&(this.selectionElementInternal||(this.selectionElementInternal=document.createElement("div"),this.selectionElementInternal.classList.add("selection"),this.selectionElementInternal.classList.add("fill")),this.listItemNode.insertBefore(this.selectionElementInternal,this.listItemElement.firstChild))}treeElementToggled(e){const t=e.currentTarget;if(!t||ta.get(t)!==this||t.hasSelection())return;console.assert(Boolean(this.treeOutline));const n=!!this.treeOutline&&this.treeOutline.showSelectionOnKeyboardFocus,i=this.toggleOnClick&&(n||!this.selectable),r=this.isEventWithinDisclosureTriangle(e);(i||r)&&(this.expanded?e.altKey?this.collapseRecursively():this.collapse():e.altKey?this.expandRecursively():this.expand(),s.logClick(this.expandLoggable,e),e.consume())}handleMouseDown(e){const t=e.currentTarget;t&&this.selectable&&ta.get(t)===this&&(this.isEventWithinDisclosureTriangle(e)||this.selectOnMouseDown(e))}handleDoubleClick(e){const t=e.currentTarget;if(!t||ta.get(t)!==this)return;this.ondblclick(e)||this.expandable&&!this.expanded&&this.expand()}detach(){this.listItemNode.remove(),this.childrenListNode.remove()}collapse(){if(!this.expanded||!this.collapsible)return;this.listItemNode.classList.remove("expanded"),this.childrenListNode.classList.remove("expanded"),Qs(this.listItemNode,!1),this.expanded=!1,this.oncollapse(),this.treeOutline&&this.treeOutline.dispatchEventToListeners(Jo.ElementCollapsed,this);const e=this.treeOutline&&this.treeOutline.selectedTreeElement;e&&e.hasAncestor(this)&&this.select(!0,!0)}collapseRecursively(){let e=this;for(;e;)e.expanded&&e.collapse(),e=e.traverseNextTreeElement(!1,this,!0)}collapseChildren(){if(this.childrenInternal)for(const e of this.childrenInternal)e.collapseRecursively()}expand(){!this.expandable||this.expanded&&this.childrenInternal||(this.expanded=!0,this.populateIfNeeded(),this.listItemNode.classList.add("expanded"),this.childrenListNode.classList.add("expanded"),Qs(this.listItemNode,!0),this.treeOutline&&(this.onexpand(),this.treeOutline.dispatchEventToListeners(Jo.ElementExpanded,this)))}async expandRecursively(e){let t=this;const n={depthChange:0};let i=0;(void 0===e||isNaN(e))&&(e=3);do{t.isExpandRecursively()&&(await t.populateIfNeeded(),i<e&&t.expand()),t=t.traverseNextTreeElement(!t.isExpandRecursively(),this,!0,n),i+=n.depthChange}while(null!==t)}collapseOrAscend(e){if(this.expanded&&this.collapsible)return e?this.collapseRecursively():this.collapse(),!0;if(!this.parent||this.parent.root)return!1;if(!this.parent.selectable)return this.parent.collapse(),!0;let t=this.parent;for(;t&&!t.selectable;)t=t.parent;return!!t&&(t.select(!1,!0),!0)}descendOrExpand(e){if(!this.expandable)return!1;if(!this.expanded)return e?this.expandRecursively():this.expand(),!0;let t=this.firstChild();for(;t&&!t.selectable;)t=t.nextSibling;return!!t&&(t.select(!1,!0),!0)}reveal(e){let t=this.parent;for(;t&&!t.root;)t.expanded||t.expand(),t=t.parent;this.treeOutline&&this.treeOutline.deferredScrollIntoView(this,Boolean(e))}revealed(){let e=this.parent;for(;e&&!e.root;){if(!e.expanded)return!1;e=e.parent}return!0}selectOnMouseDown(e){if(this.select(!1,!0)&&e.consume(!0),this.listItemNode.draggable&&this.selectionElementInternal&&this.treeOutline){const e=this.treeOutline.element.getBoundingClientRect().left-this.listItemNode.getBoundingClientRect().left-this.treeOutline.element.scrollLeft;this.selectionElementInternal.style.setProperty("margin-left",e+"px")}}select(e,t){if(e=e||this.disableSelectFocus,!this.treeOutline||!this.selectable||this.selected)return e||this.listItemElement.focus(),!1;const n=this.treeOutline.selectedTreeElement;return this.treeOutline.selectedTreeElement=null,this.treeOutline.rootElementInternal===this?(n&&n.deselect(),e||this.listItemElement.focus(),!1):(this.selected=!0,this.treeOutline.selectedTreeElement=this,this.treeOutline.updateFocusable(),e&&!this.treeOutline.contentElement.hasFocus()||this.listItemElement.focus(),this.listItemNode.classList.add("selected"),nr(this.listItemNode,!0),this.treeOutline.dispatchEventToListeners(Jo.ElementSelected,this),n&&n.deselect(),this.onselect(t))}setFocusable(e){e?(this.listItemNode.setAttribute("tabIndex",this.treeOutline&&this.treeOutline.preventTabOrder?"-1":"0"),this.listItemNode.addEventListener("focus",this.boundOnFocus,!1),this.listItemNode.addEventListener("blur",this.boundOnBlur,!1)):(this.listItemNode.removeAttribute("tabIndex"),this.listItemNode.removeEventListener("focus",this.boundOnFocus,!1),this.listItemNode.removeEventListener("blur",this.boundOnBlur,!1))}onFocus(){this.treeOutline&&!this.treeOutline.getUseLightSelectionColor()&&(this.treeOutline.contentElement.classList.contains("hide-selection-when-blurred")||this.listItemNode.classList.add("force-white-icons"))}onBlur(){this.treeOutline&&!this.treeOutline.getUseLightSelectionColor()&&(this.treeOutline.contentElement.classList.contains("hide-selection-when-blurred")||this.listItemNode.classList.remove("force-white-icons"))}revealAndSelect(e){this.reveal(!0),this.select(e)}deselect(){const e=this.listItemNode.hasFocus();this.selected=!1,this.listItemNode.classList.remove("selected"),ir(this.listItemNode),this.setFocusable(!1),this.treeOutline&&this.treeOutline.selectedTreeElement===this&&(this.treeOutline.selectedTreeElement=null,this.treeOutline.updateFocusable(),e&&this.treeOutline.focus())}async populateIfNeeded(){this.treeOutline&&this.expandable&&!this.childrenInternal&&(this.childrenInternal=[],await this.onpopulate())}async onpopulate(){}onenter(){return this.expandable&&!this.expanded?(this.expand(),!0):!(!this.collapsible||!this.expanded)&&(this.collapse(),!0)}ondelete(){return!1}onspace(){return!1}onbind(){}onunbind(){}onattach(){}onexpand(){}oncollapse(){}ondblclick(e){return!1}onselect(e){return!1}traverseNextTreeElement(e,t,n,i){n||this.populateIfNeeded(),i&&(i.depthChange=0);let s=e?this.revealed()?this.firstChild():null:this.firstChild();if(s&&(!e||e&&this.expanded))return i&&(i.depthChange=1),s;if(this===t)return null;if(s=e?this.revealed()?this.nextSibling:null:this.nextSibling,s)return s;for(s=this;s&&!s.root&&!(e?s.revealed()&&s.nextSibling:s.nextSibling)&&s.parent!==t;)i&&(i.depthChange-=1),s=s.parent;return!s||s.root?null:e?s.revealed()?s.nextSibling:null:s.nextSibling}traversePreviousTreeElement(e,t){let n=e?this.revealed()?this.previousSibling:null:this.previousSibling;for(!t&&n&&n.populateIfNeeded();n&&(e?n.revealed()&&n.expanded&&n.lastChild():n.lastChild());)t||n.populateIfNeeded(),n=e?n.revealed()&&n.expanded?n.lastChild():null:n.lastChild();return n||(!this.parent||this.parent.root?null:this.parent)}isEventWithinDisclosureTriangle(e){const t=window.getComputedStyle(this.listItemNode).paddingLeft;console.assert(t.endsWith("px"));const n=parseFloat(t),i=this.listItemNode.getBoundingClientRect().left+n;return e.pageX>=i&&e.pageX<=i+10&&this.expandable}setDisableSelectFocus(e){this.disableSelectFocus=e}}s.registerParentProvider("parentTreeItem",(function(e){const t=na.getTreeElementBylistItemNode(e),n=t?.parent?.listItemElement;return n?.isConnected&&n||t?.treeOutline?.contentElement}));var ia=Object.freeze({__proto__:null,get Events(){return Jo},TreeOutline:ea,TreeOutlineInShadow:class extends ea{element;shadowRoot;disclosureElement;renderSelection;constructor(){super(),this.contentElement.classList.add("tree-outline"),this.element=document.createElement("div"),this.shadowRoot=ds(this.element,{cssFile:Qo,delegatesFocus:void 0}),this.disclosureElement=this.shadowRoot.createChild("div","tree-outline-disclosure"),this.disclosureElement.appendChild(this.contentElement),this.renderSelection=!0}registerRequiredCSS(e){c.ThemeSupport.instance().appendStyle(this.shadowRoot,e)}registerCSSFiles(e){this.shadowRoot.adoptedStyleSheets=this.shadowRoot.adoptedStyleSheets.concat(e)}hideOverflow(){this.disclosureElement.classList.add("tree-outline-disclosure-hide-overflow")}makeDense(){this.contentElement.classList.add("tree-outline-dense")}onStartedEditingTitle(e){const t=this.shadowRoot.getSelection();t&&t.selectAllChildren(e.titleElement)}},treeElementBylistItemNode:ta,TreeElement:na});var sa=Object.freeze({__proto__:null,SimpleView:class extends lt{#l;#c;constructor(e,t,i){if(super(t),this.#l=e,i&&!n.StringUtilities.isExtendedKebabCase(i))throw new Error(`Invalid view ID '${i}'`);this.#c=i??n.StringUtilities.toKebabCase(e)}viewId(){return this.#c}title(){return this.#l}isCloseable(){return!1}isTransient(){return!1}toolbarItems(){return Promise.resolve([])}widget(){return Promise.resolve(this)}revealView(){return qt.instance().revealView(this)}disposeView(){}isPreviewFeature(){return!1}iconName(){}}});export{br as ARIAUtils,k as ActionRegistration,L as ActionRegistry,f as Context,fr as ContextFlavorListener,Sn as ContextMenu,xs as Dialog,Re as DockController,yr as DropTarget,Hr as EmptyWidget,Gr as FilterBar,$r as FilterSuggestionBuilder,Yr as ForwardedInputEventHandler,Dr as Fragment,q as Geometry,ys as GlassPane,Jr as HistoryInput,We as Infobar,io as InplaceEditor,hn as InspectorView,ve as KeyboardShortcut,An as ListControl,Bn as ListModel,lo as ListWidget,ho as Panel,mo as PopoverHelper,fo as ProgressIndicator,ko as RemoteDebuggingTerminatedScreen,Lo as ReportView,Ue as ResizerWidget,Po as RootView,Ho as SearchableView,Pn as SettingsUI,ne as ShortcutRegistry,fn as SoftContextMenu,_o as SoftDropDown,vt as SplitWidget,Nn as SuggestBox,Mt as TabbedPane,Xo as TargetCrashedScreen,_n as TextPrompt,Yo as ThrottledWidget,di as Toolbar,xt as Tooltip,ia as TreeOutline,gs as UIUtils,sa as View,tn as ViewManager,ht as Widget,Ge as XElement,Br as XLink,Qe as XWidget,gt as ZoomManager};
