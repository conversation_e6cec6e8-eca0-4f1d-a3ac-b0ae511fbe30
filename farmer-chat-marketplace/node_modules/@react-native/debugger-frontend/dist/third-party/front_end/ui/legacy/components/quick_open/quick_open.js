import*as e from"../../../../core/common/common.js";import*as t from"../../../../core/host/host.js";import*as i from"../../../../core/i18n/i18n.js";import*as s from"../../../../core/platform/platform.js";import*as r from"../../../../third_party/diff/diff.js";import*as o from"../../legacy.js";import*as n from"../../../../models/text_utils/text_utils.js";import*as l from"../../../components/text_prompt/text_prompt.js";import*as a from"../../../visual_logging/visual_logging.js";import*as d from"../../../components/icon_button/icon_button.js";const c=new CSSStyleSheet;c.replaceSync('.filtered-list-widget{display:flex;flex-direction:column;flex:auto;border:1px solid transparent}.hbox{flex:0 0 40px;align-items:center}.filtered-list-widget-hint{color:var(--sys-color-token-subtle);padding:12px}devtools-text-prompt{flex-grow:1;font-size:14px;font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;line-height:16px;padding:12px}.filtered-list-widget-progress{flex:none;background:rgb(0 0 0/20%);height:1px}.filtered-list-widget-progress-bar{background-color:var(--sys-color-primary-bright);height:2px;width:100%;transform:scaleX(0);transform-origin:top left;opacity:100%;transition:none}.filtered-widget-progress-fade{opacity:0%;transition:opacity 500ms}.filtered-list-widget .vbox > div.container{flex:auto;overflow-x:hidden;overflow-y:auto}.filtered-list-widget-item-wrapper{color:var(--sys-color-on-surface);display:flex;border-bottom:1px solid var(--sys-color-divider);font-family:".SFNSDisplay-Regular","Helvetica Neue","Lucida Grande",sans-serif;padding-left:8px;padding-right:8px}.filtered-list-widget-item-wrapper devtools-icon{align-self:center;flex:none;padding-right:8px}.filtered-list-widget-item-wrapper.selected{background-color:var(--sys-color-tonal-container)}.filtered-list-widget-item{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;align-self:center;font-size:12px;flex:auto}.filtered-list-widget-item.is-ignore-listed{opacity:50%}.filtered-list-widget-item.two-rows span.highlight{color:var(--sys-color-primary)}.filtered-list-widget-item.one-row span.highlight{font-weight:bold}.filtered-list-widget-item .filtered-list-widget-title{flex:initial;overflow:hidden;text-overflow:ellipsis}.filtered-list-widget-item .filtered-list-widget-subtitle{flex:none;overflow:hidden;text-overflow:ellipsis;color:var(--sys-color-token-subtle);display:flex;white-space:pre}.filtered-list-widget-item .filtered-list-widget-subtitle .first-part{flex-shrink:1000;overflow:hidden;text-overflow:ellipsis}.filtered-list-widget-item-wrapper .tag{padding:0 12px;border-radius:4px;line-height:24px;height:24px;align-self:center;flex-shrink:0}.filtered-list-widget-item-wrapper .deprecated-tag{font-size:11px;color:var(--sys-color-token-subtle)}.filtered-list-widget-item-wrapper.selected .deprecated-tag{color:var(--sys-color-on-tonal-container)}.filtered-list-widget-item-wrapper.selected .filtered-list-widget-item span.highlight{color:var(--sys-color-on-tonal-container);background-color:var(--sys-color-state-focus-select)}.filtered-list-widget-item-wrapper.selected .filtered-list-widget-title,\n.filtered-list-widget-item-wrapper.selected .filtered-list-widget-subtitle,\n.filtered-list-widget-item-wrapper.selected .tag{color:var(--sys-color-on-tonal-container)}.filtered-list-widget-item.one-row{height:36px;line-height:20px;padding-top:8px;padding-bottom:8px;display:flex}.filtered-list-widget-item.one-row .filtered-list-widget-title{padding-right:8px}.filtered-list-widget-item.two-rows{height:45px;padding-top:8px;padding-bottom:8px}.filtered-list-widget-item.two-rows .filtered-list-widget-title{font-weight:bold}.not-found-text{height:34px;line-height:34px;padding-left:8px;font-style:italic;color:var(--sys-color-state-disabled);background:var(--sys-color-state-disabled-container)}.quickpick-description{flex:none;overflow:hidden;text-overflow:ellipsis;color:var(--sys-color-state-disabled);padding-left:15px}@media (forced-colors: active){.filtered-list-widget{forced-color-adjust:none;border-color:ButtonText}.filtered-list-widget-item-wrapper .filtered-list-widget-title,\n  .filtered-list-widget-item-wrapper .filtered-list-widget-subtitle,\n  .quickpick-description{color:ButtonText}.filtered-list-widget-item-wrapper.selected{background-color:Highlight}.filtered-list-widget-item-wrapper.selected .filtered-list-widget-item .filtered-list-widget-title,\n  .filtered-list-widget-item-wrapper.selected .filtered-list-widget-item .filtered-list-widget-subtitle{color:HighlightText}devtools-text-prompt{border-color:ButtonText}}\n/*# sourceURL=filteredListWidget.css */\n');const h={quickOpenPrompt:"Quick open prompt",quickOpen:"Quick open",noResultsFound:"No results found",sItemSOfS:"{PH1}, item {PH2} of {PH3}"},m=i.i18n.registerUIStrings("ui/legacy/components/quick_open/FilteredListWidget.ts",h),g=i.i18n.getLocalizedString.bind(void 0,m);class p extends(e.ObjectWrapper.eventMixin(o.Widget.VBox)){promptHistory;scoringTimer;filterTimer;loadTimeout;refreshListWithCurrentResult;dialog;query="";inputBoxElement;hintElement;bottomElementsContainer;progressElement;progressBarElement;items;list;itemElementsContainer;notFoundElement;prefix="";provider;queryChangedCallback;constructor(e,t,i){super(!0),this.promptHistory=t||[],this.scoringTimer=0,this.filterTimer=0,this.loadTimeout=0,this.contentElement.classList.add("filtered-list-widget");const s=this.onKeyDown.bind(this);this.contentElement.addEventListener("keydown",s),o.ARIAUtils.markAsCombobox(this.contentElement);const r=this.contentElement.createChild("div","hbox");this.inputBoxElement=new l.TextPrompt.TextPrompt,this.inputBoxElement.data={ariaLabel:g(h.quickOpenPrompt),prefix:"",suggestion:""},this.inputBoxElement.addEventListener(l.TextPrompt.PromptInputEvent.eventName,this.onInput.bind(this),!1),this.inputBoxElement.setAttribute("jslog",`${a.textField().track({change:!0,keydown:"ArrowUp|ArrowDown|PageUp|PageDown|Enter|Tab|>|@|:|?|!"})}`),r.appendChild(this.inputBoxElement),this.hintElement=r.createChild("span","filtered-list-widget-hint"),this.bottomElementsContainer=this.contentElement.createChild("div","vbox"),this.progressElement=this.bottomElementsContainer.createChild("div","filtered-list-widget-progress"),this.progressBarElement=this.progressElement.createChild("div","filtered-list-widget-progress-bar"),this.items=new o.ListModel.ListModel,this.list=new o.ListControl.ListControl(this.items,this,o.ListControl.ListMode.EqualHeightItems),this.itemElementsContainer=this.list.element,this.itemElementsContainer.classList.add("container"),this.bottomElementsContainer.appendChild(this.itemElementsContainer),this.itemElementsContainer.addEventListener("click",this.onClick.bind(this),!1),this.itemElementsContainer.addEventListener("mousemove",this.onMouseMove.bind(this),!1),o.ARIAUtils.markAsListBox(this.itemElementsContainer),o.ARIAUtils.setControls(this.inputBoxElement,this.itemElementsContainer),o.ARIAUtils.setAutocomplete(this.inputBoxElement,"list"),this.notFoundElement=this.bottomElementsContainer.createChild("div","not-found-text"),this.notFoundElement.classList.add("hidden"),this.setDefaultFocusedElement(this.inputBoxElement),this.provider=e,this.queryChangedCallback=i}static highlightRanges(e,t,i){if(!t)return!1;function s(e,t){const i=r.Diff.DiffWrapper.charDiff(t,e);let s=0;const o=[];for(let e=0;e<i.length;++e){const t=i[e];if(t[0]===r.Diff.Operation.Equal)o.push(new n.TextRange.SourceRange(s,t[1].length));else if(t[0]!==r.Diff.Operation.Insert)return null;s+=t[1].length}return o}if(null===e.textContent)return!1;const l=e.textContent;let a=s(l,t);return a&&!i||(a=s(l.toUpperCase(),t.toUpperCase())),!!a&&(o.UIUtils.highlightRangesWithStyleClass(e,a,"highlight"),!0)}setCommandPrefix(e){this.inputBoxElement.setPrefix(e)}setCommandSuggestion(e){this.inputBoxElement.setSuggestion(e)}setHintElement(e){this.hintElement.textContent=e}setPromptTitle(e){o.ARIAUtils.setLabel(this.inputBoxElement,e)}showAsDialog(e){e||(e=g(h.quickOpen)),this.dialog=new o.Dialog.Dialog("quick-open"),o.ARIAUtils.setLabel(this.dialog.contentElement,e),this.dialog.setMaxContentSize(new o.Geometry.Size(504,340)),this.dialog.setSizeBehavior("SetExactWidthMaxHeight"),this.dialog.setContentPosition(null,22),this.dialog.contentElement.style.setProperty("border-radius","4px"),this.show(this.dialog.contentElement),o.ARIAUtils.setExpanded(this.contentElement,!0),this.dialog.once("hidden").then((()=>{this.dispatchEventToListeners("hidden")})),this.dialog.show()}setPrefix(e){this.prefix=e}setProvider(e){e!==this.provider&&(this.provider&&this.provider.detach(),this.clearTimers(),this.provider=e,this.isShowing()&&this.attachProvider())}setQuerySelectedRange(e,t){this.inputBoxElement.setSelectedRange(e,t)}attachProvider(){this.items.replaceAll([]),this.list.invalidateItemHeight(),this.provider&&(this.provider.setRefreshCallback(this.itemsLoaded.bind(this,this.provider)),this.provider.attach()),this.itemsLoaded(this.provider)}cleanValue(){return this.query.substring(this.prefix.length).trim()}wasShown(){this.registerCSSFiles([c]),this.attachProvider()}willHide(){this.provider&&this.provider.detach(),this.clearTimers(),o.ARIAUtils.setExpanded(this.contentElement,!1)}clearTimers(){clearTimeout(this.filterTimer),clearTimeout(this.scoringTimer),clearTimeout(this.loadTimeout),this.filterTimer=0,this.scoringTimer=0,this.loadTimeout=0,this.refreshListWithCurrentResult=void 0}onEnter(e){if(!this.provider)return;e.preventDefault();const t=this.list.selectedIndex();if(t<0)return;const i=this.list.elementAtIndex(t);i&&a.logClick(i,e);const s=this.provider.itemCount()?this.list.selectedItem():null;this.selectItem(s),this.dialog&&this.dialog.hide()}itemsLoaded(e){this.loadTimeout||e!==this.provider||(this.loadTimeout=window.setTimeout(this.updateAfterItemsLoaded.bind(this),0))}updateAfterItemsLoaded(){this.loadTimeout=0,this.filterItems()}createElementForItem(e){const t=document.createElement("div");t.className="filtered-list-widget-item-wrapper";const i=t.createChild("div"),s=this.provider&&this.provider.renderAsTwoRows();i.className="filtered-list-widget-item "+(s?"two-rows":"one-row");const r=i.createChild("div","filtered-list-widget-title"),n=i.createChild("div","filtered-list-widget-subtitle");return n.textContent="​",this.provider&&(this.provider.renderItem(e,this.cleanValue(),r,n),t.setAttribute("jslog",`${a.item(this.provider.jslogContextAt(e)).track({click:!0})}`)),o.ARIAUtils.markAsOption(i),t}heightForItem(e){return 0}isItemSelectable(e){return!0}selectedItemChanged(e,t,i,s){i&&i.classList.remove("selected"),s&&s.classList.add("selected"),o.ARIAUtils.setActiveDescendant(this.inputBoxElement,s)}onClick(e){const t=this.list.itemForNode(e.target);null!==t&&(e.consume(!0),this.selectItem(t),this.dialog&&this.dialog.hide())}onMouseMove(e){const t=this.list.itemForNode(e.target);if(null===t)return;this.list.selectItem(t);const i=this.list.elementAtIndex(this.list.selectedIndex())?.textContent;i&&o.ARIAUtils.alert(g(h.sItemSOfS,{PH1:i,PH2:this.list.selectedIndex()+1,PH3:this.items.length}))}setQuery(e){this.query=e,this.inputBoxElement.focus(),this.inputBoxElement.setText(e),this.queryChanged(),this.scheduleFilter()}tabKeyPressed(){const e=this.query;let t;for(let i=this.promptHistory.length-1;i>=0;i--)if(this.promptHistory[i]!==e&&this.promptHistory[i].startsWith(e)){t=this.promptHistory[i];break}if(t){const i=this.inputBoxElement.getComponentSelection();return i&&""!==i.toString().trim()?(this.setQuery(t),!0):(this.inputBoxElement.focus(),this.inputBoxElement.setText(t),this.setQuerySelectedRange(e.length,t.length),!0)}return this.list.selectNextItem(!0,!1)}itemsFilteredForTest(){}filterItems(){if(this.filterTimer=0,this.scoringTimer&&(clearTimeout(this.scoringTimer),this.scoringTimer=0,this.refreshListWithCurrentResult&&this.refreshListWithCurrentResult()),!this.provider)return this.bottomElementsContainer.classList.toggle("hidden",!0),void this.itemsFilteredForTest();this.bottomElementsContainer.classList.toggle("hidden",!1),this.progressBarElement.style.transform="scaleX(0)",this.progressBarElement.classList.remove("filtered-widget-progress-fade","hidden");const e=this.provider.rewriteQuery(this.cleanValue()),t=e?s.StringUtilities.filterRegex(e):null,i=[],r=[],o=[],n=100;let l=0;const a=[],d=window.performance.now(),c=s.NumberUtilities.clamp(10,500,this.provider.itemCount()/10|0);function h(e,t){return t-e}(function m(g){if(!this.provider)return;this.scoringTimer=0;let p,u=0;for(p=g;p<this.provider.itemCount()&&u<c;++p){if(t&&!t.test(this.provider.itemKeyAt(p)))continue;const d=this.provider.itemScoreAt(p,e);if(e&&u++,d>l||r.length<n){const e=s.ArrayUtilities.upperBound(r,d,h);if(r.splice(e,0,d),o.splice(e,0,p),r.length>n){const e=o[o.length-1];e&&a.push(e),r.length=n,o.length=n}const t=r[r.length-1];t&&(l=t)}else i.push(p)}if(this.refreshListWithCurrentResult=this.refreshList.bind(this,o,a,i),p<this.provider.itemCount())return this.scoringTimer=window.setTimeout(m.bind(this,p),0),void(window.performance.now()-d>50&&(this.progressBarElement.style.transform="scaleX("+p/this.provider.itemCount()+")"));window.performance.now()-d>100?(this.progressBarElement.style.transform="scaleX(1)",this.progressBarElement.classList.add("filtered-widget-progress-fade")):this.progressBarElement.classList.add("hidden");this.refreshListWithCurrentResult()}).call(this,0)}refreshList(e,t,i){this.refreshListWithCurrentResult=void 0,i=[...e,...t,...i],this.updateNotFoundMessage(Boolean(i.length));const s=this.list.element.offsetHeight;this.items.replaceAll(i),i.length&&this.list.selectItem(i[0]),this.list.element.offsetHeight!==s&&this.list.viewportResized(),this.itemsFilteredForTest()}updateNotFoundMessage(e){this.list.element.classList.toggle("hidden",!e),this.notFoundElement.classList.toggle("hidden",e),!e&&this.provider&&(this.notFoundElement.textContent=this.provider.notFoundText(this.cleanValue()),o.ARIAUtils.alert(this.notFoundElement.textContent))}onInput(e){this.query=e.data,this.queryChanged(),this.scheduleFilter()}async queryChanged(){this.hintElement.classList.toggle("hidden",Boolean(this.query)),this.queryChangedCallback&&await this.queryChangedCallback(this.query),this.provider&&this.provider.queryChanged(this.cleanValue())}updateSelectedItemARIA(e,t){return!1}onKeyDown(e){let t=!1;switch(e.key){case s.KeyboardUtilities.ENTER_KEY:return void this.onEnter(e);case s.KeyboardUtilities.TAB_KEY:if(e.shiftKey){t=this.list.selectPreviousItem(!0,!1);break}t=this.tabKeyPressed();break;case"ArrowUp":t=this.list.selectPreviousItem(!0,!1);break;case"ArrowDown":t=this.list.selectNextItem(!0,!1);break;case"PageUp":t=this.list.selectItemPreviousPage(!1);break;case"PageDown":t=this.list.selectItemNextPage(!1)}if(t){e.consume(!0);const t=this.list.elementAtIndex(this.list.selectedIndex())?.textContent;t&&o.ARIAUtils.alert(g(h.sItemSOfS,{PH1:t,PH2:this.list.selectedIndex()+1,PH3:this.items.length}))}}scheduleFilter(){this.filterTimer||(this.filterTimer=window.setTimeout(this.filterItems.bind(this),0))}selectItem(e){this.promptHistory.push(this.query),this.promptHistory.length>100&&this.promptHistory.shift(),this.provider&&this.provider.selectItem(e,this.cleanValue())}}class u{refreshCallback;jslogContext;constructor(e){this.jslogContext=e}setRefreshCallback(e){this.refreshCallback=e}attach(){}itemCount(){return 0}itemKeyAt(e){return""}itemScoreAt(e,t){return 1}renderItem(e,t,i,s){}jslogContextAt(e){return this.jslogContext}renderAsTwoRows(){return!1}selectItem(e,t){}refresh(){this.refreshCallback&&this.refreshCallback()}rewriteQuery(e){return e}queryChanged(e){}notFoundText(e){return g(h.noResultsFound)}detach(){}}const f=[];function w(e){f.push(e)}function x(){return f}var v=Object.freeze({__proto__:null,FilteredListWidget:p,Provider:u,registerProvider:w,getRegisteredProviders:x});const C={typeToSeeAvailableCommands:"Type ? to see available commands"},y=i.i18n.registerUIStrings("ui/legacy/components/quick_open/QuickOpen.ts",C),b=i.i18n.getLocalizedString.bind(void 0,y),E=[];class A{prefix;prefixes;providers;filteredListWidget;constructor(){this.prefix=null,this.prefixes=[],this.providers=new Map,this.filteredListWidget=null,x().forEach(this.addProvider.bind(this)),this.prefixes.sort(((e,t)=>t.length-e.length))}static show(e){const t=new this,i=new p(null,E,t.queryChanged.bind(t));t.filteredListWidget=i,i.setHintElement(b(C.typeToSeeAvailableCommands)),i.showAsDialog(),i.setQuery(e)}addProvider(e){const t=e.prefix;null!==t&&(this.prefixes.push(t),this.providers.set(t,{provider:e.provider,titlePrefix:e.titlePrefix,titleSuggestion:e.titleSuggestion}))}async queryChanged(e){const t=this.prefixes.find((t=>e.startsWith(t)));if("string"!=typeof t)return;if(!this.filteredListWidget)return;this.filteredListWidget.setPrefix(t);const i=this.providers.get(t)?.titlePrefix;this.filteredListWidget.setCommandPrefix(i?i():"");const s=e===t&&this.providers.get(t)?.titleSuggestion;if(this.filteredListWidget.setCommandSuggestion(s?s():""),this.prefix===t)return;this.prefix=t,this.filteredListWidget.setProvider(null);const r=this.providers.get(t)?.provider;if(!r)return;const o=await r();this.prefix===t&&this.filteredListWidget&&(this.filteredListWidget.setProvider(o),this.providerLoadedForTest(o))}providerLoadedForTest(e){}}var I=Object.freeze({__proto__:null,history:E,QuickOpenImpl:A,ShowActionDelegate:class{handleAction(e,t){return"quick-open.show"===t&&(A.show(""),!0)}}});const S={oneOrMoreSettingsHaveChanged:"One or more settings have changed which requires a reload to take effect.",noCommandsFound:"No commands found",run:"Run",command:"Command",deprecated:"— deprecated"},L=i.i18n.registerUIStrings("ui/legacy/components/quick_open/CommandMenu.ts",S),k=i.i18n.getLocalizedString.bind(void 0,L);let T;class P{commandsInternal;constructor(){this.commandsInternal=[],this.loadCommands()}static instance(e={forceNew:null}){const{forceNew:t}=e;return T&&!t||(T=new P),T}static createCommand(e){const{category:i,keys:s,title:r,shortcut:o,jslogContext:n,executeHandler:l,availableHandler:a,userActionCode:d,deprecationWarning:c,isPanelOrDrawer:h}=e;let m=l;if(d){const e=d;m=()=>{t.userMetrics.actionTaken(e),l()}}return new H(i,r,s,o,n,m,a,c,h)}static createSettingCommand(i,r,n){const l=i.category();if(!l)throw new Error(`Creating '${r}' setting command failed. Setting has no category.`);const a=i.tags()||"",d=Boolean(i.reloadRequired());return P.createCommand({category:e.Settings.getLocalizedSettingsCategory(l),keys:a,title:r,shortcut:"",jslogContext:s.StringUtilities.toKebabCase(`${i.name}-${n}`),executeHandler:()=>{!i.deprecation?.disabled||i.deprecation?.experiment&&!i.deprecation.experiment.isEnabled()?(i.set(n),"emulate-page-focus"===i.name&&t.userMetrics.actionTaken(t.UserMetrics.Action.ToggleEmulateFocusedPageFromCommandMenu),"show-web-vitals"===i.name&&t.userMetrics.actionTaken(t.UserMetrics.Action.ToggleShowWebVitals),d&&o.InspectorView.InspectorView.instance().displayReloadRequiredWarning(k(S.oneOrMoreSettingsHaveChanged))):e.Revealer.reveal(i)},availableHandler:function(){return i.get()!==n},userActionCode:void 0,deprecationWarning:i.deprecation?.warning})}static createActionCommand(e){const{action:t,userActionCode:i}=e,s=t.category();if(!s)throw new Error(`Creating '${t.title()}' action command failed. Action has no category.`);let r;"DRAWER"===s&&(r="DRAWER");const n=o.ShortcutRegistry.ShortcutRegistry.instance().shortcutTitleForAction(t.id())||"";return P.createCommand({category:o.ActionRegistration.getLocalizedActionCategory(s),keys:t.tags()||"",title:t.title(),shortcut:n,jslogContext:t.id(),executeHandler:t.execute.bind(t),userActionCode:i,availableHandler:void 0,isPanelOrDrawer:r})}static createRevealViewCommand(e){const{title:i,tags:s,category:r,userActionCode:n,id:l}=e;if(!r)throw new Error(`Creating '${i}' reveal view command failed. Reveal view has no category.`);let a;"PANEL"===r?a="PANEL":"DRAWER"===r&&(a="DRAWER");return P.createCommand({category:o.ViewManager.getLocalizedViewLocationCategory(r),keys:s,title:i,shortcut:"",jslogContext:l,executeHandler:()=>("issues-pane"===l&&t.userMetrics.issuesPanelOpenedFrom(5),o.ViewManager.ViewManager.instance().showView(l,!0)),userActionCode:n,availableHandler:void 0,isPanelOrDrawer:a})}loadCommands(){const t=new Map;for(const{category:e,name:i}of o.ViewManager.getRegisteredLocationResolvers())e&&i&&t.set(i,e);const i=o.ViewManager.getRegisteredViewExtensions(e.Settings.Settings.instance().getHostConfig());for(const e of i){const i=e.location(),s=i&&t.get(i);if(!s)continue;const r={title:e.commandPrompt(),tags:e.tags()||"",category:s,userActionCode:void 0,id:e.viewId()};this.commandsInternal.push(P.createRevealViewCommand(r))}const s=e.Settings.Settings.instance().getRegisteredSettings();for(const t of s){const i=t.options;if(i&&t.category)for(const s of i){const i=e.Settings.Settings.instance().moduleSetting(t.settingName);this.commandsInternal.push(P.createSettingCommand(i,s.title(),s.value))}}}commands(){return this.commandsInternal}}class R extends u{commands;constructor(e=[]){super("command"),this.commands=e}attach(){const e=P.instance().commands(),t=o.ActionRegistry.ActionRegistry.instance().availableActions();for(const e of t){if(!e.category())continue;const t={action:e,userActionCode:void 0};this.commands.push(P.createActionCommand(t))}for(const t of e)t.available()&&(this.commands.find((({title:e,category:i})=>e===t.title&&i===t.category))||this.commands.push(t));this.commands=this.commands.sort((function(e,t){const i=s.StringUtilities.compare(e.category,t.category);return i||s.StringUtilities.compare(e.title,t.title)}))}detach(){this.commands=[]}itemCount(){return this.commands.length}itemKeyAt(e){return this.commands[e].key}itemScoreAt(e,t){const i=this.commands[e];let s=r.Diff.DiffWrapper.characterScore(t.toLowerCase(),i.title.toLowerCase());return"PANEL"===i.isPanelOrDrawer?s+=2:"DRAWER"===i.isPanelOrDrawer&&(s+=1),s}renderItem(e,t,i,r){const n=this.commands[e];i.removeChildren(),o.UIUtils.createTextChild(i,n.title),p.highlightRanges(i,t,!0),r.textContent=n.shortcut;const l=n.deprecationWarning;if(l){const e=i.parentElement?.createChild("span","deprecated-tag");e&&(e.textContent=k(S.deprecated),e.title=l)}const a=i.parentElement?.parentElement?.createChild("span","tag");if(!a)return;const d=s.StringUtilities.hashCode(n.category)%F.length;a.style.backgroundColor=F[d],a.style.color="#fff",a.textContent=n.category}jslogContextAt(e){return this.commands[e].jslogContext}selectItem(e,i){null!==e&&(this.commands[e].execute(),t.userMetrics.actionTaken(t.UserMetrics.Action.SelectCommandFromCommandMenu))}notFoundText(){return k(S.noCommandsFound)}}const F=["#F44336","#E91E63","#9C27B0","#673AB7","#3F51B5","#03A9F4","#00BCD4","#009688","#4CAF50","#8BC34A","#CDDC39","#FFC107","#FF9800","#FF5722","#795548","#9E9E9E","#607D8B"];class H{category;title;key;shortcut;jslogContext;deprecationWarning;isPanelOrDrawer;#e;#t;constructor(e,t,i,s,r,o,n,l,a){this.category=e,this.title=t,this.key=e+"\0"+t+"\0"+i,this.shortcut=s,this.jslogContext=r,this.#e=o,this.#t=n,this.deprecationWarning=l,this.isPanelOrDrawer=a}available(){return!this.#t||this.#t()}execute(){return this.#e()}}w({prefix:">",iconName:"chevron-right",iconWidth:"20px",provider:()=>Promise.resolve(new R),titlePrefix:()=>k(S.run),titleSuggestion:()=>k(S.command)});var W=Object.freeze({__proto__:null,CommandMenu:P,CommandMenuProvider:R,MaterialPaletteColors:F,Command:H,ShowActionDelegate:class{handleAction(e,i){return t.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront(),A.show(">"),!0}}});class B extends u{providers;constructor(e){super(e),this.providers=[],x().forEach(this.addProvider.bind(this))}async addProvider(e){e.titleSuggestion&&this.providers.push({prefix:e.prefix||"",iconName:e.iconName,iconWidth:e.iconWidth,title:e.titlePrefix()+" "+e.titleSuggestion(),jslogContext:(await e.provider()).jslogContext})}itemCount(){return this.providers.length}itemKeyAt(e){return this.providers[e].prefix}itemScoreAt(e,t){return-this.providers[e].prefix.length}renderItem(e,t,i,s){const r=this.providers[e],n=new d.Icon.Icon;n.data={iconName:r.iconName,color:"var(--icon-default)",width:r.iconWidth},i.parentElement?.parentElement?.insertBefore(n,i.parentElement),o.UIUtils.createTextChild(i,r.title)}jslogContextAt(e){return this.providers[e].jslogContext}selectItem(e,t){null!==e&&A.show(this.providers[e].prefix)}renderAsTwoRows(){return!1}}w({prefix:"?",iconName:"help",iconWidth:"20px",provider:()=>Promise.resolve(new B("help")),titlePrefix:()=>"Help",titleSuggestion:void 0});var U=Object.freeze({__proto__:null,HelpQuickOpen:B});export{W as CommandMenu,v as FilteredListWidget,U as HelpQuickOpen,I as QuickOpen};
