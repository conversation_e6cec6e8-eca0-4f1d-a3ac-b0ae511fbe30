import*as e from"../../core/common/common.js";import*as t from"../../core/host/host.js";import*as n from"../components/render_coordinator/render_coordinator.js";import{assertNotNullOrUndefined as o}from"../../core/platform/platform.js";const i="jslog";function r(e){return e.hasAttribute(i)}function s(e){return a(e.getAttribute(i)||"")}var c;function a(e){const t=e.replace(/ /g,"").split(";"),n=e=>t.find((t=>t.startsWith(e)))?.substr(e.length),o=function(e){return c[e]??0}(t[0]);if(0===o)throw new Error("Unkown VE: "+e);const i={ve:o},r=n("context:");r&&(i.context=r);const s=n("parent:");s&&(i.parent=s);const a=n("track:");if(a){i.track={};for(const e of a.split(","))e.startsWith("keydown:")?i.track.keydown=e.substr(8):i.track[e]=!0}return i}function l(e,t){const n=[e];return void 0!==t&&n.push(`context: ${t}`),{context:function(e){return void 0!==e&&n.push(`context: ${e}`),this},parent:function(e){return n.push(`parent: ${e}`),this},track:function(e){return n.push(`track: ${Object.entries(e).map((([e,t])=>!0!==t?`${e}: ${t}`:e)).join(", ")}`),this},toString:function(){return n.join("; ")}}}!function(e){e[e.TreeItem=1]="TreeItem",e[e.Close=2]="Close",e[e.Counter=3]="Counter",e[e.Drawer=4]="Drawer",e[e.Resizer=5]="Resizer",e[e.Toggle=6]="Toggle",e[e.Tree=7]="Tree",e[e.TextField=8]="TextField",e[e.AnimationClip=9]="AnimationClip",e[e.Section=10]="Section",e[e.SectionHeader=11]="SectionHeader",e[e.Timeline=12]="Timeline",e[e.CSSRuleHeader=13]="CSSRuleHeader",e[e.Expand=14]="Expand",e[e.ToggleSubpane=15]="ToggleSubpane",e[e.ControlPoint=16]="ControlPoint",e[e.Toolbar=17]="Toolbar",e[e.Popover=18]="Popover",e[e.BreakpointMarker=19]="BreakpointMarker",e[e.DropDown=20]="DropDown",e[e.Adorner=21]="Adorner",e[e.Gutter=22]="Gutter",e[e.MetricsBox=23]="MetricsBox",e[e.MetricsBoxPart=24]="MetricsBoxPart",e[e.DOMBreakpoint=26]="DOMBreakpoint",e[e.Action=29]="Action",e[e.FilterDropdown=30]="FilterDropdown",e[e.Dialog=31]="Dialog",e[e.BezierCurveEditor=32]="BezierCurveEditor",e[e.BezierPresetCategory=34]="BezierPresetCategory",e[e.Preview=35]="Preview",e[e.Canvas=36]="Canvas",e[e.ColorEyeDropper=37]="ColorEyeDropper",e[e.Link=44]="Link",e[e.Item=46]="Item",e[e.PaletteColorShades=47]="PaletteColorShades",e[e.Panel=48]="Panel",e[e.ShowStyleEditor=50]="ShowStyleEditor",e[e.Slider=51]="Slider",e[e.CssColorMix=52]="CssColorMix",e[e.Value=53]="Value",e[e.Key=54]="Key",e[e.PieChart=59]="PieChart",e[e.PieChartSlice=60]="PieChartSlice",e[e.PieChartTotal=61]="PieChartTotal",e[e.ElementsBreadcrumbs=62]="ElementsBreadcrumbs",e[e.PanelTabHeader=66]="PanelTabHeader",e[e.Menu=67]="Menu",e[e.TableRow=68]="TableRow",e[e.TableHeader=69]="TableHeader",e[e.TableCell=70]="TableCell",e[e.Pane=72]="Pane",e[e.ResponsivePresets=73]="ResponsivePresets",e[e.DeviceModeRuler=74]="DeviceModeRuler",e[e.MediaInspectorView=75]="MediaInspectorView"}(c||(c={}));const d=new WeakMap;function u(){const e=new Int32Array(1);return crypto.getRandomValues(e),e[0]}function p(e,t,n){if(t.parent&&h.has(t.parent)&&e instanceof Element)for(n=h.get(t.parent)?.(e);n instanceof Element&&!r(n);)n=n.parentElementOrShadowHost()??void 0;if(d.has(e)){const t=d.get(e);return n&&t.parent!==g(n)&&(t.parent=g(n)),t}const o={impressionLogged:!1,processed:!1,config:t,veid:u(),parent:n?g(n):null,size:new DOMRect(0,0,0,0)};return d.set(e,o),o}function g(e){return d.get(e)||null}const h=new Map;function f(e,t){if(h.has(e))throw new Error(`Parent provider with the name '${e} is already registered'`);h.set(e,t)}const b=new WeakMap;function v(e,t){b.set(e,t)}f("mapped",(e=>b.get(e)));let m=!1,w=null;const y=new WeakMap;function E(e){const t=g(e);m&&t&&!t.processedForDebugging&&(e instanceof Element?function(e,t){"OPTION"===e.tagName?t.parent?.selectOpen&&w&&(w.innerHTML+="<br>"+D(t.config),t.processedForDebugging=!0):(e.style.outline="solid 1px red",e.addEventListener("mouseenter",(()=>{o(w);const e=[t];let n=t.parent;for(;n;)e.push(n),n=n.parent;!function(e){if(!w)return;w.style.display="block",w.innerHTML=e}(e.map((e=>D(e.config))).join("<br>"))}),{capture:!0}),e.addEventListener("mouseleave",(()=>{o(w),w.style.display="none"}),{capture:!0}),t.processedForDebugging=!0)}(e,t):function(e,t){let n=y.get(e);n||(n=document.createElement("div"),n.classList.add("ve-debug"),n.style.background="black",n.style.color="white",n.style.zIndex="100000",n.textContent=D(t.config),y.set(e,n),setTimeout((()=>{t.size?.width&&t.size?.height||(n?.parentElement?.removeChild(n),y.delete(e))}),1e4));const i=parent instanceof HTMLElement?parent:y.get(parent)||w;o(i),i.classList.contains("ve-debug")?(n.style.marginLeft="10px",i.appendChild(n)):(n.style.position="absolute",i.insertBefore(n,i.firstChild))}(e,t))}function T(e,t,n){const o=localStorage.getItem("veDebugLoggingEnabled");if(o)switch(o){case F.Intuitive:!function(e,t,n){const o={event:e,ve:t?c[t?.config.ve]:void 0,veid:t?.veid,context:t?.config.context,time:Date.now()-z,...n};k(o),x(o)}(e,t,n);break;case F.Test:!function(e,t,n){M=null,x({interaction:`${e}: ${I.get(t?.veid||0)||""}`})}(e,t);break;case F.AdHocAnalysis:!function(e,t,n){const o=t?S.get(t.veid):null;if(o){const t={time:Date.now()-z,type:e,...n};k(t),o.interactions.push(t)}}(e,t,n)}}function k(e){for(const t in e){const n=t;void 0===e[n]&&delete e[n]}}function C(e){switch(localStorage.getItem("veDebugLoggingEnabled")){case F.Intuitive:!function(e){const t=new Map;for(const n of e){const e={event:"Impression",ve:c[n.config.ve],context:n?.config.context,width:n.size.width,height:n.size.height,veid:n.veid};if(k(e),t.set(n.veid,e),n.parent&&t.has(n.parent?.veid)){const o=t.get(n.parent?.veid);o.children=o.children||[],o.children.push(e)}else e.parent=n.parent?.veid}const n=[...t.values()].filter((e=>"parent"in e));1===n.length?(n[0].time=Date.now()-z,x(n[0])):x({event:"Impression",children:n,time:Date.now()-z})}(e);break;case F.Test:!function(e){M||(M={impressions:[]},L.push(M));for(const t of e){let e="";t.parent&&(e=(I.get(t.parent.veid)||"<UNKNOWN>")+" > "),e+=c[t.config.ve],t.config.context&&(e+=": "+t.config.context),I.set(t.veid,e),M.impressions.push(e)}}(e);break;case F.AdHocAnalysis:!function(e){for(const t of e){const e=t=>{const n={ve:c[t.config.ve],veid:t.veid,width:t.size?.width,height:t.size?.height,context:t.config.context};return k(n),t.parent&&(n.parent=e(t.parent)),n},n={...e(t),interactions:[],time:Date.now()-z};S.set(t.veid,n),x(n)}}(e)}}globalThis.setVeDebuggingEnabled=function(e){m=e,e&&!w&&(w=document.createElement("div"),w.classList.add("ve-debug"),w.style.position="absolute",w.style.bottom="100px",w.style.left="100px",w.style.background="black",w.style.color="white",w.style.zIndex="100000",document.body.appendChild(w))};const I=new Map;let M=null;const S=new Map;function D(e){const t=[c[e.ve]];return e.context&&t.push(`context: ${e.context}`),e.parent&&t.push(`parent: ${e.parent}`),e.track&&t.push(`track: ${Object.entries(e.track).map((([e,t])=>`${e}${"string"==typeof t?`: ${t}`:""}`)).join(", ")}`),t.join("; ")}const L=[];function x(e){const t=localStorage.getItem("veDebugLoggingEnabled");t&&(L.push(e),t===F.Intuitive&&console.info("VE Debug:",e))}var F;function $(e,t=F.Intuitive){e?localStorage.setItem("veDebugLoggingEnabled",t):localStorage.removeItem("veDebugLoggingEnabled")}function A(e,t){return[...t.strings.map((t=>e[t]?`"${e[t]}"`:"$NullString")),...t.numerics.map((t=>e[t]??"null")),...t.booleans.map((t=>e[t]??"$NullBool"))].join(", ")}!function(e){e.Intuitive="Intuitive",e.Test="Test",e.AdHocAnalysis="AdHocAnalysis"}(F||(F={}));class P{#e=new Map;add(e){this.#e.set(e.veid,e)}get(e){return this.#e.get(e)}getArea(e){let t=(e.width||0)*(e.height||0);const n=e.parent?this.#e.get(e.parent?.veid):null;if(!n)return t;const o=this.getArea(n);return t>o&&(t=o),t}get data(){return[...this.#e.values()].filter((e=>this.getArea(e))).sort(((e,t)=>this.getArea(t)-this.getArea(e)))}}function R(e,t,n,o){let i=e;for(const e of t.data){if(!("children"in i))return;let t=i.children[i.children.length-1];("Impression"===t?.type?t.veid:null)!==e.veid&&(i.children.push(...o),o.length=0,t={type:"Impression",ve:e.ve,veid:e.veid,context:e.context,time:n,children:[]},i.children.push(t)),i=t}}function H(e){if("Impression"===e.type){for(;1===e.children.length;)"Impression"===e.children[0].type&&(e.children=e.children[0].children);for(const t of e.children)H(t)}}let z=Date.now();globalThis.setVeDebugLoggingEnabled=$,globalThis.veDebugEventsLog=L,globalThis.findVeDebugImpression=function(e,t){const n=o=>{if("Impression"===o.event&&o.veid===e)return o;let i=0;for(const e of o.children||[]){const r=n(e);if(r){if(t){const e=[];return e[i]=r,{...o,children:e}}return r}++i}};return n({children:L})},globalThis.exportAdHocAnalysisLogForSql=function(){const e={strings:["ve","context"],numerics:["veid","width","height"],booleans:[]},t={strings:["type","context"],numerics:["width","height","mouseButton","time"],booleans:["width","height","mouseButton","time"]},n=e=>e.map(((e,t)=>`$${t+1} as ${e}`)).join(", "),o=t=>`$VeFields(${A(t,e)}, ${t.parent?`STRUCT(${o(t.parent)})`:null})`,i=e=>`$Interaction(${A(e,t)})`,r=L;console.log(`\nDEFINE MACRO NullString CAST(null AS STRING);\nDEFINE MACRO NullBool CAST(null AS BOOL);\nDEFINE MACRO VeFields ${n([...e.strings,...e.numerics,"parent"])};\nDEFINE MACRO Interaction STRUCT(${n([...t.strings,...t.numerics,...t.booleans])});\nDEFINE MACRO Entry STRUCT($1, $2 AS interactions, $3 AS time);\n\n// This fake entry put first fixes nested struct fiels names being lost\nDEFINE MACRO FakeVeFields $VeFields("", $NullString, 0, 0, 0, $1);\nDEFINE MACRO FakeVe STRUCT($FakeVeFields($1));\nDEFINE MACRO FakeEntry $Entry($FakeVeFields($FakeVe($FakeVe($FakeVe($FakeVe($FakeVe($FakeVe($FakeVe(null)))))))), ([]), 0);\n\nWITH\n  processed_logs AS (\n      SELECT * FROM UNNEST([\n        $FakeEntry,\n        ${r.map((e=>`$Entry(${o(e)}, ([${e.interactions.map(i).join(", ")}]), ${e.time})`)).join(", \n")}\n      ])\n    )\n\n\n\nSELECT * FROM processed_logs;`)},globalThis.buildStateFlow=function(){const e=function(){const e=[];for(const t of L){e.push(t);const n=t.veid;for(const o of t.interactions)e.push({...o,veid:n})}return e.sort(((e,t)=>e.time-t.time)),e}(),t=new P,n={type:"Session",children:[]};let o=e[0].time;const i=[];for(const r of e){if(r.time>o+1e3&&(R(n,t,o,i),i.length=0),"type"in r)if("Resize"===r.type){const e=t.get(r.veid);if(!e)continue;const n=t.getArea(e);e.width=r.width,e.height=r.height,0!==t.getArea(e)&&0!==n&&i.push(r)}else i.push(r);else t.add(r);o=r.time}return R(n,t,o,i),H(n),n},globalThis.getVeDebugEventsLog=async function(){return await Promise.all([ne,oe,ie,re,se,ce].map((async e=>{for(let t=0;e.process&&t<3;++t)await e.processCompleted}))).then((()=>{})),M=null,L};const O=10;function B(e,t){const n=function(e,t){const n=Math.max(e.left,t.left),o=Math.min(e.left+e.width,t.left+t.width);if(n<=o){const i=Math.max(e.top,t.top),r=Math.min(e.top+e.height,t.top+t.height);if(i<=r)return new DOMRect(n,i,o-n,r-i)}return null}(t,e.getBoundingClientRect());return!n||n.width<O||n.height<O?null:n}async function V(e){const n=await Promise.all(e.map((async e=>{const t=g(e);o(t);const n={id:t.veid,type:t.config.ve};return void 0!==t.config.context&&(n.context=await _(t.config.context)),t.parent&&(n.parent=t.parent.veid),t.size&&(n.width=Math.round(t.size.width),n.height=Math.round(t.size.height)),n})));n.length&&(t.InspectorFrontendHost.InspectorFrontendHostInstance.recordImpression({impressions:n}),C(e.map((e=>g(e)))))}const N=(e,n)=>{const o=g(e);if(!o)return;o.size=n;const i={veid:o.veid,width:o.size.width,height:o.size.height};t.InspectorFrontendHost.InspectorFrontendHostInstance.recordResize(i),T("Resize",o,{width:Math.round(n.width),height:Math.round(n.height)})},j=e=>(n,o,i)=>{const r=g(n);if(!r)return;const s={veid:r.veid,doubleClick:Boolean(i?.doubleClick)};o instanceof MouseEvent&&"sourceCapabilities"in o&&o.sourceCapabilities&&(s.mouseButton=o.button),e.schedule((async()=>{t.InspectorFrontendHost.InspectorFrontendHostInstance.recordClick(s),T("Click",r,{mouseButton:s.mouseButton,doubleClick:s.doubleClick})}))},K=e=>async n=>{const i=g(n.currentTarget);o(i);const r={veid:i.veid};await e.schedule((async()=>{})),e.schedule((async()=>{t.InspectorFrontendHost.InspectorFrontendHostInstance.recordHover(r),T("Hover",i)}))},W=e=>async n=>{const i=g(n.currentTarget);o(i);const r={veid:i.veid};await e.schedule((async()=>{})),e.schedule((async()=>{t.InspectorFrontendHost.InspectorFrontendHostInstance.recordDrag(r),T("Drag",i)}))};let U=null;const G=e=>async(n,o,i)=>{if(!(o instanceof KeyboardEvent))return;const r=n?g(n):null,s="string"==typeof r?.config.track?.keydown?r.config.track.keydown:"";if(s.length&&!s.split("|").includes(o.code)&&!s.split("|").includes(o.key))return;const c={veid:r?.veid};!i&&s?.length&&(i=function(e){if(!(e instanceof KeyboardEvent))return;const t=e.key,n=t.toLowerCase(),o=[];e.shiftKey&&t!==n&&o.push("shift");e.ctrlKey&&o.push("ctrl");e.altKey&&o.push("alt");e.metaKey&&o.push("meta");return o.push(n),o.join("-")}(o)),i&&(c.context=await _(i)),U&&i&&U!==i&&e.process?.(),U=i||null,e.schedule((async()=>{t.InspectorFrontendHost.InspectorFrontendHostInstance.recordKeyDown(c),T("KeyDown",r,{context:i}),U=null}))};async function _(e){if(void 0===e)return;const t=parseInt(e,10);if(!isNaN(t))return t;if(!crypto.subtle)return 3735928559;const n=(new TextEncoder).encode(e),o=await crypto.subtle.digest("SHA-1",n);return new DataView(o).getInt32(0,!0)}let X=new WeakMap;function Y(e){return X.get(e||Z)||[]}function q(e){return X.has(e||Z)}function J(e){return[...Y(e)]}function Q(e){X.delete(e||Z)}const Z={},ee=50,te={schedule:async()=>{}};let ne=te,oe=te,ie=te,re=te,se=te,ce=te;const ae=new MutationObserver(Ee),le=new ResizeObserver($e),de=new IntersectionObserver($e),ue=[],pe=new Map,ge=new Set;function he(e){for(const t of e)ae.observe(t,{attributes:!0,childList:!0,subtree:!0})}let fe=!1;async function be(t){fe=!0,ne=t?.processingThrottler||new e.Throttler.Throttler(500),oe=t?.keyboardLogThrottler||new e.Throttler.Throttler(3e3),ie=t?.hoverLogThrottler||new e.Throttler.Throttler(1e3),re=t?.dragLogThrottler||new e.Throttler.Throttler(1250),se=t?.clickLogThrottler||new e.Throttler.Throttler(500),ce=t?.resizeLogThrottler||new e.Throttler.Throttler(200),z=Date.now(),localStorage.getItem("veDebugLoggingEnabled")===F.Intuitive&&x({event:"SessionStart"}),await ve(document)}async function ve(e){ue.push(e),["interactive","complete"].includes(e.readyState)&&await Ce(),e.addEventListener("visibilitychange",Ee),e.addEventListener("scroll",Ee),he([e.body])}function me(){fe=!1,X=new WeakMap;for(const e of ue)e.removeEventListener("visibilitychange",Ee),e.removeEventListener("scroll",Ee);ae.disconnect(),le.disconnect(),de.disconnect(),ue.length=0,Te.clear(),ne=te,pe.clear(),ge.clear()}async function we(){for(;se.process;)await se.processCompleted;for(;oe.process;)await oe.processCompleted}function ye(){for(const e of ge)Ie(e)}async function Ee(){ne&&ne.schedule((()=>n.RenderCoordinator.RenderCoordinator.instance().read("processForLogging",Ce)))}const Te=new Map,ke=e=>{const t=e.ownerDocument,n=Te.get(t)||new DOMRect(0,0,t.defaultView?.innerWidth||0,t.defaultView?.innerHeight||0);return Te.set(t,n),n};async function Ce(){if(document.hidden)return;const e=performance.now(),{loggables:n,shadowRoots:o}=function(e){const t=[],n=[],o=[],i=(e,t,n)=>{for(const i of e)o.push({element:i,parent:t,slot:n})};for(const t of e)i(t.body.children);let s=0;for(;;){const e=o[s++];if(!e)break;const{element:c,slot:a}=e;let{parent:l}=e;c.assignedSlot&&c.assignedSlot!==a||(r(c)&&(t.push({element:c,parent:l}),l=c),"slot"===c.localName&&c.assignedElements().length?i(c.assignedElements(),l,c):i(c.children,l),c.shadowRoot&&(n.push(c.shadowRoot),i(c.shadowRoot.children,l)))}return{loggables:t,shadowRoots:n}}(ue),i=[];he(o);const c=[void 0];for(const{element:e,parent:o}of n){const n=p(e,s(e),o);if(!n.impressionLogged){const t=B(e,ke(e)),o="OPTION"===e.tagName&&n.parent?.selectOpen;(t||o)&&(t&&(n.size=t),i.push(e),n.impressionLogged=!0)}if(n.impressionLogged&&q(e)&&c.push(e),!n.processed){const o=e=>t=>{const n=t.currentTarget;j(se)(n,t,{doubleClick:e})};n.config.track?.click&&(e.addEventListener("click",o(!1),{capture:!0}),e.addEventListener("auxclick",o(!1),{capture:!0}),e.addEventListener("contextmenu",o(!1),{capture:!0})),n.config.track?.dblclick&&e.addEventListener("dblclick",o(!0),{capture:!0});const i=n.config.track?.hover;i&&(e.addEventListener("mouseover",K(ie),{capture:!0}),e.addEventListener("mouseout",(()=>ie.schedule(Me,"AsSoonAsPossible")),{capture:!0}));const r=n.config.track?.drag;r&&(e.addEventListener("pointerdown",Le,{capture:!0}),document.addEventListener("pointerup",xe,{capture:!0}),document.addEventListener("dragend",xe,{capture:!0})),n.config.track?.change&&(e.addEventListener("input",(t=>{t instanceof InputEvent&&(n.lastInputEventType&&n.lastInputEventType!==t.inputType&&Ie(e),n.lastInputEventType=t.inputType,ge.add(e))}),{capture:!0}),e.addEventListener("change",(()=>Ie(e)),{capture:!0}),e.addEventListener("focusout",(()=>{n.lastInputEventType&&Ie(e)}),{capture:!0}));const s=n.config.track?.keydown;if(s&&e.addEventListener("keydown",(e=>G(oe)(e.currentTarget,e)),{capture:!0}),n.config.track?.resize&&(le.observe(e),de.observe(e)),"SELECT"===e.tagName){const o=t=>{j(se)(e,t),n.selectOpen||(n.selectOpen=!0,Ee())};e.addEventListener("click",o,{capture:!0}),e.addEventListener("keydown",(e=>{const n=e;(!t.Platform.isMac()&&!n.altKey||"ArrowDown"!==n.code&&"ArrowUp"!==n.code)&&(n.altKey||n.ctrlKey||"F4"!==n.code)||o(e)}),{capture:!0}),e.addEventListener("keypress",(e=>{const n=e;(" "===n.key||!t.Platform.isMac()&&"\r"===n.key)&&o(e)}),{capture:!0}),e.addEventListener("change",(t=>{for(const n of e.selectedOptions)g(n)?.config.track?.click&&j(se)(n,t)}),{capture:!0})}n.processed=!0}E(e)}for(let e=0;e<c.length;++e){const t=c[e];for(const{loggable:e,config:n,parent:o}of J(t)){const t=p(e,n,o);E(e),i.push(e),t.impressionLogged=!0,q(e)&&c.push(e)}Q(t)}i.length&&(await we(),ye(),await V(i)),t.userMetrics.visualLoggingProcessingDone(performance.now()-e)}function Ie(e){const n=g(e);n&&(!async function(e){const n=g(e);o(n);const i={veid:n.veid},r=n.lastInputEventType;r&&(i.context=await _(r)),t.InspectorFrontendHost.InspectorFrontendHostInstance.recordChange(i),T("Change",n,{context:r})}(e),delete n.lastInputEventType,ge.delete(e))}async function Me(){}let Se=0,De=0;function Le(e){e instanceof MouseEvent&&(Se=e.screenX,De=e.screenY,W(re)(e))}function xe(e){e instanceof MouseEvent&&(Math.abs(e.screenX-Se)>=ee||Math.abs(e.screenY-De)>=ee||re.schedule(Me,"AsSoonAsPossible"))}function Fe(e,t){for(;t;){if(t===e)return!0;t=t.parent}return!1}async function $e(e){for(const t of e){const e=t.target,n=g(e),o=B(e,ke(e))||new DOMRect(0,0,0,0);if(!n?.size)continue;let i=!1;for(const t of pe.keys()){if(t===e)continue;const o=g(t);if(Fe(o,n)){i=!0;break}Fe(n,o)&&pe.delete(t)}i||(pe.set(e,o),ce.schedule((async()=>{pe.size&&(await we(),ye());for(const[e,t]of pe.entries()){const n=g(e);n&&((Math.abs(t.width-n.size.width)>=50||Math.abs(t.height-n.size.height)>=50)&&N(e,t))}pe.clear()}),"Delayed"))}}const Ae=(e,t)=>j(se)(e,t),Pe=(e,t)=>N(e,t),Re=async(e,t,n)=>G(oe)(e,t,n);function He(e,t,n){fe&&(!function(e,t,n){const o=Y(n);o.push({loggable:e,config:t,parent:n}),X.set(n||Z,o)}(e,a(t),n||void 0),Ee())}const ze=l.bind(null,"Action"),Oe=l.bind(null,"Adorner"),Be=l.bind(null,"AnimationClip"),Ve=l.bind(null,"BezierCurveEditor"),Ne=l.bind(null,"BezierPresetCategory"),je=l.bind(null,"BreakpointMarker"),Ke=l.bind(null,"Canvas"),We=l.bind(null,"Close"),Ue=l.bind(null,"ColorEyeDropper"),Ge=l.bind(null,"Counter"),_e=l.bind(null,"ControlPoint"),Xe=l.bind(null,"CssColorMix"),Ye=l.bind(null,"CSSRuleHeader"),qe=l.bind(null,"DeviceModeRuler"),Je=l.bind(null,"DOMBreakpoint"),Qe=l.bind(null,"Drawer"),Ze=l.bind(null,"DropDown"),et=l.bind(null,"ElementsBreadcrumbs"),tt=l.bind(null,"Expand"),nt=l.bind(null,"FilterDropdown"),ot=l.bind(null,"Gutter"),it=l.bind(null,"Dialog"),rt=l.bind(null,"Item"),st=l.bind(null,"Key"),ct=l.bind(null,"Link"),at=l.bind(null,"MediaInspectorView"),lt=l.bind(null,"Menu"),dt=l.bind(null,"MetricsBox"),ut=l.bind(null,"PaletteColorShades"),pt=l.bind(null,"Pane"),gt=l.bind(null,"Panel"),ht=l.bind(null,"PanelTabHeader"),ft=l.bind(null,"PieChart"),bt=l.bind(null,"PieChartSlice"),vt=l.bind(null,"PieChartTotal"),mt=l.bind(null,"Popover"),wt=l.bind(null,"Preview"),yt=l.bind(null,"Resizer"),Et=l.bind(null,"ResponsivePresets"),Tt=l.bind(null,"ShowStyleEditor"),kt=l.bind(null,"Slider"),Ct=l.bind(null,"Section"),It=l.bind(null,"SectionHeader"),Mt=l.bind(null,"TableRow"),St=l.bind(null,"TableCell"),Dt=l.bind(null,"TableHeader"),Lt=l.bind(null,"TextField"),xt=l.bind(null,"Timeline"),Ft=l.bind(null,"Toggle"),$t=l.bind(null,"Toolbar"),At=l.bind(null,"ToggleSubpane"),Pt=l.bind(null,"Tree"),Rt=l.bind(null,"TreeItem"),Ht=l.bind(null,"Value");export{F as DebugLoggingFormat,ze as action,ve as addDocument,Oe as adorner,Be as animationClip,Ve as bezierCurveEditor,Ne as bezierPresetCategory,je as breakpointMarker,Ke as canvas,We as close,Ue as colorEyeDropper,_e as controlPoint,Ge as counter,Xe as cssColorMix,Ye as cssRuleHeader,qe as deviceModeRuler,it as dialog,Je as domBreakpoint,Qe as drawer,Ze as dropDown,et as elementsBreadcrumbs,tt as expand,nt as filterDropdown,ot as gutter,rt as item,st as key,ct as link,Ae as logClick,V as logImpressions,Re as logKeyDown,Pe as logResize,at as mediaInspectorView,lt as menu,dt as metricsBox,ut as paletteColorShades,pt as pane,gt as panel,ht as panelTabHeader,ft as pieChart,bt as pieChartSlice,vt as pieChartTotal,mt as popover,wt as preview,He as registerLoggable,f as registerParentProvider,yt as resizer,Et as responsivePresets,Ct as section,It as sectionHeader,v as setMappedParent,$ as setVeDebugLoggingEnabled,Tt as showStyleEditor,kt as slider,be as startLogging,me as stopLogging,St as tableCell,Dt as tableHeader,Mt as tableRow,Lt as textField,xt as timeline,Ft as toggle,At as toggleSubpane,$t as toolbar,Pt as tree,Rt as treeItem,Ht as value};
