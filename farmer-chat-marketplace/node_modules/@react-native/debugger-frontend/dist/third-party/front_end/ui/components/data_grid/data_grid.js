import*as e from"../../../core/host/host.js";import*as t from"../../../core/platform/platform.js";import*as o from"../../legacy/legacy.js";import*as s from"../../lit-html/lit-html.js";import*as r from"../../visual_logging/visual_logging.js";import*as i from"../render_coordinator/render_coordinator.js";import*as n from"../icon_button/icon_button.js";import*as l from"../../../core/i18n/i18n.js";import*as a from"../../../core/common/common.js";const c=new CSSStyleSheet;c.replaceSync(':host{height:100%;display:block;position:relative}.wrapping-container{overflow-y:auto;height:100%}.wrapping-container:not(.show-scrollbar)::-webkit-scrollbar{display:none}table{border-spacing:0;width:100%;height:100%;table-layout:fixed}tr{outline:none}thead tr{height:27px}tbody tr{background-color:var(--override-data-grid-row-background-color,--sys-color-surface)}tbody tr:nth-child(odd){background-color:var(--sys-color-surface1)}tbody tr:hover:not(.selected){background-color:var(--sys-color-state-hover-on-subtle)}tbody tr.selected{background-color:var(--sys-color-neutral-container)}tbody tr.selected:focus-within{background-color:var(--sys-color-tonal-container)}.wrapping-container.striped tr:nth-child(odd):not(.selected):not(.padding-row):not(:hover){background-color:var(--sys-color-surface1)}td,\nth{box-sizing:border-box;padding:1px 4px;border-left:1px solid var(--sys-color-divider);color:var(--sys-color-on-surface);height:var(--table-row-height);user-select:text;white-space:nowrap;text-overflow:ellipsis;overflow:hidden}th{font-weight:normal;text-align:left;border-bottom:1px solid var(--sys-color-divider);position:sticky;top:0;z-index:2;background-color:var(--sys-color-surface1)}td:focus,\nth:focus{outline:var(--sys-color-primary) auto 1px}th:hover.sortable{background-color:var(--sys-color-surface2)}.cell-resize-handle{top:0;height:100%;z-index:3;width:20px;cursor:col-resize;position:absolute}td.firstVisibleColumn,\nth.firstVisibleColumn{border-left-width:0}.hidden{display:none}.filler-row td{height:100%;pointer-events:none;padding:0}.filler-row.empty-table td{padding:1px}tbody tr.selected:focus-within td{border-left-color:var(--sys-color-divider-on-tonal-container)}[aria-sort="ascending"],\n[aria-sort="descending"]{padding-right:20px}[aria-sort="descending"]::after{content:"";width:0;border-left:0.4em solid transparent;border-right:0.4em solid transparent;border-top:0.4em solid var(--sys-color-token-subtle);position:absolute;right:0.5em;top:0.85em}[aria-sort="ascending"]::after{content:"";width:0;border-bottom:0.4em solid var(--sys-color-token-subtle);border-left:0.4em solid transparent;border-right:0.4em solid transparent;position:absolute;right:0.5em;top:0.7em}@media (forced-colors: active){th.sortable{outline:Highlight auto 1px}th:hover.sortable{background-color:Highlight}[aria-sort="ascending"]::after{forced-color-adjust:none;border-bottom:0.4em solid buttonText}[aria-sort="descending"]::after{forced-color-adjust:none;border-top:0.4em solid buttonText}}\n/*# sourceURL=dataGrid.css */\n');class d extends Event{static eventName="columnheaderclick";data;constructor(e,t){super(d.eventName),this.data={column:e,columnIndex:t}}}class h extends Event{static eventName="contextmenucolumnsortclick";data;constructor(e){super(h.eventName),this.data={column:e}}}class u extends Event{static eventName="contextmenuheaderresetclick";constructor(){super(u.eventName)}}class m extends Event{static eventName="newuserfiltertext";data;constructor(e){super(m.eventName,{composed:!0}),this.data={filterText:e}}}class p extends Event{static eventName="cellfocused";data;constructor(e,t){super(p.eventName,{composed:!0}),this.data={cell:e,row:t}}}class g extends Event{static eventName="rowmouseenter";data;constructor(e){super(g.eventName,{composed:!0}),this.data={row:e}}}class w extends Event{static eventName="rowmouseleave";data;constructor(e){super(w.eventName,{composed:!0}),this.data={row:e}}}var f=Object.freeze({__proto__:null,ColumnHeaderClickEvent:d,ContextMenuColumnSortClickEvent:h,ContextMenuHeaderResetClickEvent:u,NewUserFilterTextEvent:m,BodyCellFocusedEvent:p,RowMouseEnterEvent:g,RowMouseLeaveEvent:w});function b(e,t){const o=!t.visible,s=e.data.columns.map((e=>(e===t&&(e.visible=o),e)));e.data={...e.data,columns:s}}function v(e,t){const{columns:o}=e.data;for(const s of o)s.hideable&&t.defaultSection().appendCheckboxItem(s.title,(()=>{b(e,s)}),{checked:s.visible,jslogContext:s.id})}function C(e,t){const o=e.data.columns.filter((e=>!0===e.sortable));if(o.length>0)for(const s of o)t.defaultSection().appendItem(s.title,(()=>{e.dispatchEvent(new h(s))}),{jslogContext:s.id})}const S=e=>s.html`${e}`;var x=Object.freeze({__proto__:null,primitiveRenderer:S,codeBlockRenderer:e=>{if(!e)return s.nothing;const t=String(e);return s.html`<code>${t}</code>`},iconRenderer:e=>e?s.html`<div style="display: flex; justify-content: center;">${e}</div>`:s.nothing});function R(e){return JSON.stringify(e.map((e=>e.value instanceof n.Icon.Icon?null:e.value))).toLowerCase()}function y(e,t){const o=e.cells.find((e=>e.columnId===t));if(void 0===o)throw new Error(`Found a row that was missing an entry for column ${t}.`);return o}function M(e){return e.renderer?e.renderer(e.value):S(e.value)}function I(e,t){const o=e.filter((e=>e.visible)).reduce(((e,t)=>e+t.widthWeighting),0),s=e.find((e=>e.id===t));if(!s)throw new Error(`Could not find column with ID ${t}`);if(s.widthWeighting<1)throw new Error(`Error with column ${t}: width weightings must be >= 1.`);return s.visible?Math.round(s.widthWeighting/o*100):0}function T(e){const{key:o,currentFocusedCell:s,columns:r,rows:i}=e,[n,l]=s;switch(o){case"ArrowLeft":{if(n===r.findIndex((e=>e.visible)))return[n,l];let e=n;for(let t=e-1;t>=0;t--){if(r[t].visible){e=t;break}}return[e,l]}case"ArrowRight":{let e=n;for(let t=e+1;t<r.length;t++){if(r[t].visible){e=t;break}}return[e,l]}case"ArrowUp":{const e=r.some((e=>!0===e.sortable))?0:1;if(l===e)return[n,l];let t=l;for(let o=l-1;o>=e;o--){if(0===o){t=0;break}if(!i[o-1].hidden){t=o;break}}return[n,t]}case"ArrowDown":{if(0===l){const e=i.findIndex((e=>!e.hidden));return e>-1?[n,e+1]:[n,l]}let e=l;for(let t=e+1;t<i.length+1;t++){if(!i[t-1].hidden){e=t;break}}return[n,e]}default:return t.assertNever(o,`Unknown arrow key: ${o}`)}}const $=e=>{const{columns:t,rows:o}=e,s=t.some((e=>!0===e.sortable))?0:o.findIndex((e=>!e.hidden))+1;return[t.findIndex((e=>e.visible)),s]},H=e=>e.length<25?e:e.substr(0,20)+"…";var k=Object.freeze({__proto__:null,getStringifiedCellValues:R,getRowEntryForColumnId:y,renderCellValue:M,calculateColumnWidthPercentageFromWeighting:I,handleArrowKeyNavigation:T,calculateFirstFocusableCell:$,getCellTitleFromCellContent:H});const D=i.RenderCoordinator.RenderCoordinator.instance(),O={sortBy:"Sort By",resetColumns:"Reset Columns",headerOptions:"Header Options",enterToSort:"Column sort state: {PH1}. Press enter to apply sorting filter",sortAsc:"ascending",sortDesc:"descending",sortNone:"none"},F=l.i18n.registerUIStrings("ui/components/data_grid/DataGrid.ts",O),z=l.i18n.getLocalizedString.bind(void 0,F),E=new Set([" ","Enter"]),A=20;class U extends HTMLElement{static litTagName=s.literal`devtools-data-grid`;#e=this.attachShadow({mode:"open"});#t=[];#o=[];#s=null;#r=!1;#i="NOT_SCROLLED";#n=void 0;#l=void 0;#a=10;#c=!1;#d=!1;#h=!0;#u=null;#m=new WeakMap;#p=new ResizeObserver((()=>{this.#g()}));#w=this.#f.bind(this);#b=[0,1];#v=null;#C=!1;#S=!1;#x=!1;connectedCallback(){this.#e.adoptedStyleSheets=[c],this.style.setProperty("--table-row-height","20px"),this.#R()}get data(){return{columns:this.#t,rows:this.#o,activeSort:this.#s,contextMenus:this.#n,autoScrollToBottom:this.#h,label:this.#l,paddingRowsCount:this.#a,showScrollbar:this.#c,striped:this.#d}}set data(e){if(this.#t=e.columns,this.#o=e.rows,this.#o.forEach(((e,t)=>{this.#m.set(e,t)})),this.#s=e.activeSort,this.#n=e.contextMenus,this.#l=e.label,this.#c=e.showScrollbar,this.#d=e.striped,"boolean"==typeof e.autoScrollToBottom&&(this.#h=e.autoScrollToBottom),this.#C||(this.#b=$({columns:this.#t,rows:this.#o})),void 0!==e.paddingRowsCount&&(this.#a=e.paddingRowsCount),this.#C&&this.#y()){const[e,t]=this.#M(),o=e>this.#t.length,s=t>this.#o.length;(o||s)&&(this.#v=[o?this.#t.length:e,s?this.#o.length:t])}this.#R()}#I(){return!!this.#h&&("SCROLLED_TO_BOTTOM"===this.#i||!this.#S&&"MANUAL_SCROLL_NOT_BOTTOM"!==this.#i)}#T(){if(!1===this.#C||!this.#I())return;const e=this.#e.querySelector(".wrapping-container");e&&D.scroll((()=>{const t=e.scrollHeight;e.scrollTo(0,t)}))}#$(){this.#C||this.#p.observe(this.#e.host)}#y(){return null!==this.#v}#H(){if(!this.#v)return null;const[e,t]=this.#v;return this.#e.querySelector(`[data-row-index="${t}"][data-col-index="${e}"]`)}#k(e){e.focus()}#D([e,t]){if(this.#S=!0,this.#v&&this.#v[0]===e&&this.#v[1]===t)return;this.#v=[e,t],this.#R();const s=this.#H();if(s&&(this.#k(s),0===t&&this.#t[e].sortable)){const t=this.#O(this.#t[e]);o.ARIAUtils.alert(z(O.enterToSort,{PH1:t||""}))}}#O(e){switch(this.#F(e)){case"ascending":return O.sortAsc;case"descending":return O.sortDesc;case"none":return O.sortNone}}#z(e){const o=e.key;if(!this.#v)return;if(E.has(o)){const[e,t]=this.#v,o=this.#t[e];0===t&&o&&o.sortable&&this.#E(o,e)}if(!t.KeyboardUtilities.keyIsArrowKey(o))return;const s=T({key:o,currentFocusedCell:this.#v,columns:this.#t,rows:this.#o});e.preventDefault(),this.#D(s)}#E(e,t){this.dispatchEvent(new d(e,t))}#F(e){return!e.sortable||this.#s&&this.#s.columnId===e.id?this.#s&&this.#s.columnId===e.id?"ASC"===this.#s.direction?"ascending":"descending":void 0:"none"}#A(e){const t=this.#t.map(((e,t)=>{if(!e.visible)return s.nothing;const o=s.Directives.classMap({firstVisibleColumn:0===t});return s.html`<td aria-hidden="true" class=${o} data-filler-row-column-index=${t}></td>`})),o=s.Directives.classMap({"filler-row":!0,"padding-row":!0,"empty-table":0===e});return s.html`<tr aria-hidden="true" class=${o}>${t}</tr>`}#U(){this.#u&&(this.#u.documentForCursorChange.body.style.cursor=this.#u.cursorToRestore,this.#u=null,this.#g())}#L(t){if(1!==t.buttons||e.Platform.isMac()&&t.ctrlKey)return;t.preventDefault();const o=t.target;if(!o)return;const s=o.dataset.columnIndex;if(!s)return;const r=globalThis.parseInt(s,10),i=this.#t.findIndex(((e,t)=>t>r&&!0===e.visible)),n=this.#e.querySelector(`td[data-filler-row-column-index="${r}"]`),l=this.#e.querySelector(`td[data-filler-row-column-index="${i}"]`);if(!n||!l)return;const a=this.#e.querySelector(`col[data-col-column-index="${r}"]`),c=this.#e.querySelector(`col[data-col-column-index="${i}"]`);if(!a||!c)return;const d=t.target.ownerDocument;d&&(this.#u={leftCellCol:a,rightCellCol:c,leftCellColInitialPercentageWidth:globalThis.parseInt(a.style.width,10),rightCellColInitialPercentageWidth:globalThis.parseInt(c.style.width,10),initialLeftCellWidth:n.clientWidth,initialRightCellWidth:l.clientWidth,initialMouseX:t.x,documentForCursorChange:d,cursorToRestore:o.style.cursor},d.body.style.cursor="col-resize",o.setPointerCapture(t.pointerId),o.addEventListener("pointermove",this.#w))}#f(e){if(e.preventDefault(),!this.#u)return;const o=this.#u.leftCellColInitialPercentageWidth+this.#u.rightCellColInitialPercentageWidth-10,s=e.x-this.#u.initialMouseX,r=Math.abs(s)/(this.#u.initialLeftCellWidth+this.#u.initialRightCellWidth)*100;let i,n;s>0?(i=t.NumberUtilities.clamp(this.#u.leftCellColInitialPercentageWidth+r,10,o),n=t.NumberUtilities.clamp(this.#u.rightCellColInitialPercentageWidth-r,10,o)):s<0&&(i=t.NumberUtilities.clamp(this.#u.leftCellColInitialPercentageWidth-r,10,o),n=t.NumberUtilities.clamp(this.#u.rightCellColInitialPercentageWidth+r,10,o)),i&&n&&(this.#u.leftCellCol.style.width=i.toFixed(2)+"%",this.#u.rightCellCol.style.width=n.toFixed(2)+"%")}#_(e){e.preventDefault();const t=e.target;t&&(t.releasePointerCapture(e.pointerId),t.removeEventListener("pointermove",this.#w),this.#U())}#B(e,t){const[o]=t;return o!==this.#N()&&e.visible?s.html`<span class="cell-resize-handle"
     @pointerdown=${this.#L}
     @pointerup=${this.#_}
     data-column-index=${o}
    ></span>`:s.nothing}#N(){let e=this.#t.length-1;for(;e>-1;e--){if(this.#t[e].visible)break}return e}#P(e){if(2!==e.button&&-1!==e.button)return;const t=new o.ContextMenu.ContextMenu(e);v(this,t);C(this,t.defaultSection().appendSubMenuItem(z(O.sortBy),!1,"sort-by")),t.defaultSection().appendItem(z(O.resetColumns),(()=>{this.dispatchEvent(new u)}),{jslogContext:"reset-columns"}),this.#n&&this.#n.headerRow&&this.#n.headerRow(t,this.#t),t.show()}#j(e){if(2!==e.button&&-1!==e.button)return;if(!(e.target&&e.target instanceof HTMLElement))return;const t=e.target.dataset.rowIndex;if(!t)return;const s=parseInt(t,10),r=this.#o[s-1],i=new o.ContextMenu.ContextMenu(e);C(this,i.defaultSection().appendSubMenuItem(z(O.sortBy),!1,"sort-by"));const n=i.defaultSection().appendSubMenuItem(z(O.headerOptions),!1,"header-options");v(this,n),n.defaultSection().appendItem(z(O.resetColumns),(()=>{this.dispatchEvent(new u)}),{jslogContext:"reset-columns"}),this.#n&&this.#n.bodyRow&&this.#n.bodyRow(i,this.#t,r,this.#o),i.show()}#W(e){const t=e.target;if(!t)return;const o=Math.round(t.scrollTop+t.clientHeight)===Math.round(t.scrollHeight);this.#i=o?"SCROLLED_TO_BOTTOM":"MANUAL_SCROLL_NOT_BOTTOM",this.#R()}#g(){return D.read((()=>{const e=this.#e.querySelectorAll("th:not(.hidden)"),t=this.#e.querySelectorAll(".cell-resize-handle");this.#e.querySelector("table")&&e.forEach((async(e,o)=>{const s=e.clientWidth,r=e.offsetLeft;if(t[o]){const e=t[o].clientWidth;D.write((()=>{t[o].style.left=r+s-e+"px"}))}}))}))}#G(){return D.read((()=>{const e=this.#e.querySelector(".wrapping-container");let t=0,o=window.innerHeight;e&&(t=e.scrollTop,o=e.clientHeight);const s=A*this.#a;let r=Math.floor((t-s)/A),i=Math.ceil((t+o+s)/A);return r=Math.max(0,r),i=Math.min(this.#o.filter((e=>!e.hidden)).length,i),{topVisibleRow:r,bottomVisibleRow:i}}))}#q(){this.#S=!1}#M(){return this.#v||this.#b}async#R(){if(!this.isConnected)return;if(this.#r)return void(this.#x=!0);this.#r=!0;const{topVisibleRow:e,bottomVisibleRow:t}=await this.#G(),o=this.#o.filter((e=>!e.hidden)),i=o.filter(((o,s)=>s>=e&&s<=t)),n=this.#t.findIndex((e=>e.visible)),l=this.#t.some((e=>!0===e.sortable)),a={"wrapping-container":!0,"show-scrollbar":!0===this.#c,striped:!0===this.#d};await D.write((()=>{s.render(s.html`
      ${this.#t.map(((e,t)=>this.#B(e,[t,0])))}
      <div class=${s.Directives.classMap(a)} @scroll=${this.#W} @focusout=${this.#q}>
        <table
          aria-label=${s.Directives.ifDefined(this.#l)}
          aria-rowcount=${this.#o.length}
          aria-colcount=${this.#t.length}
          @keydown=${this.#z}
        >
          <colgroup>
            ${this.#t.map(((e,t)=>{const o=`width: ${I(this.#t,e.id)}%`;return e.visible?s.html`<col style=${o} data-col-column-index=${t}>`:s.nothing}))}
          </colgroup>
          <thead>
            <tr @contextmenu=${this.#P}>
              ${this.#t.map(((e,t)=>{const o=s.Directives.classMap({hidden:!e.visible,firstVisibleColumn:t===n,sortable:l}),i=this.#M(),a=l&&t===i[0]&&0===i[1];return s.html`<th class=${o}
                  jslog=${r.tableHeader().track({click:l,resize:!0}).context(e.id)}
                  style=${s.Directives.ifDefined(e.styles?s.Directives.styleMap(e.styles):void 0)}
                  data-grid-header-cell=${e.id}
                  @focus=${()=>{this.#D([t,0])}}
                  @click=${()=>{this.#E(e,t)}}
                  title=${e.title}
                  aria-sort=${s.Directives.ifDefined(this.#F(e))}
                  aria-colindex=${t+1}
                  data-row-index='0'
                  data-col-index=${t}
                  tabindex=${s.Directives.ifDefined(l?a?"0":"-1":void 0)}
                >${e.titleElement||e.title}</th>`}))}
            </tr>
          </thead>
          <tbody>
            <tr class="filler-row-top padding-row" style=${s.Directives.styleMap({height:e*A+"px"})} aria-hidden="true"></tr>
            ${s.Directives.repeat(i,(e=>this.#m.get(e)),(e=>{const t=this.#m.get(e);if(void 0===t)throw new Error("Trying to render a row that has no index in the rowIndexMap");const o=this.#M(),i=t+1,l=!!this.#v&&i===this.#v[1],a=s.Directives.classMap({selected:l,hidden:!0===e.hidden});return s.html`
                <tr
                  aria-rowindex=${t+1}
                  class=${a}
                  style=${s.Directives.ifDefined(e.styles?s.Directives.styleMap(e.styles):void 0)}
                  jslog=${r.tableRow().track({keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Enter|Space"})}
                  @contextmenu=${this.#j}
                  @mouseenter=${()=>{this.dispatchEvent(new g(e))}}
                  @mouseleave=${()=>{this.dispatchEvent(new w(e))}}
                >${this.#t.map(((t,l)=>{const a=y(e,t.id),c=s.Directives.classMap({hidden:!t.visible,firstVisibleColumn:l===n}),d=l===o[0]&&i===o[1],h=t.visible?M(a):null;return s.html`<td
                    class=${c}
                    jslog=${r.tableCell().track({click:!0})}).context(col.id)}
                    style=${s.Directives.ifDefined(t.styles?s.Directives.styleMap(t.styles):void 0)}
                    tabindex=${d?"0":"-1"}
                    aria-colindex=${l+1}
                    title=${a.title||H(String(a.value))}
                    data-row-index=${i}
                    data-col-index=${l}
                    data-grid-value-cell-for-column=${t.id}
                    @focus=${()=>{this.#D([l,i]),this.dispatchEvent(new p(a,e))}}
                  >${h}</td>`}))}
              `}))}
            ${this.#A(i.length)}
            <tr class="filler-row-bottom padding-row" style=${s.Directives.styleMap({height:Math.max(0,o.length-t)*A+"px"})} aria-hidden="true"></tr>
          </tbody>
        </table>
      </div>
      `,this.#e,{host:this})}));const c=this.#M()[1],d=this.#H();this.#S&&c>0&&d&&this.#k(d),this.#T(),this.#$(),this.#C&&this.#g(),this.#r=!1,this.#C=!0,this.#x&&(this.#x=!1,this.#R())}}customElements.define("devtools-data-grid",U);var L=Object.freeze({__proto__:null,DataGrid:U});const _=new CSSStyleSheet;_.replaceSync(":host{display:block;height:100%;overflow:hidden}\n/*# sourceURL=dataGridController.css */\n");const B={sortInAscendingOrder:"{PH1} sorted in ascending order",sortInDescendingOrder:"{PH1} sorted in descending order",sortingCanceled:"{PH1} sorting canceled"},N=l.i18n.registerUIStrings("ui/components/data_grid/DataGridController.ts",B),P=l.i18n.getLocalizedString.bind(void 0,N);class j extends HTMLElement{static litTagName=s.literal`devtools-data-grid-controller`;#e=this.attachShadow({mode:"open"});#C=!1;#t=[];#o=[];#n=void 0;#l=void 0;#c=!1;#d=!1;#V=[];#K=[];#s=null;#X=[];#h=!0;#a;connectedCallback(){this.#e.adoptedStyleSheets=[_]}get data(){return{columns:this.#V,rows:this.#K,filters:this.#X,autoScrollToBottom:this.#h,contextMenus:this.#n,label:this.#l,paddingRowsCount:this.#a,showScrollbar:this.#c,striped:this.#d}}set data(e){this.#V=e.columns,this.#K=e.rows,this.#n=e.contextMenus,this.#X=e.filters||[],this.#n=e.contextMenus,this.#l=e.label,this.#c=e.showScrollbar,this.#d=e.striped,"boolean"==typeof e.autoScrollToBottom&&(this.#h=e.autoScrollToBottom),this.#t=[...this.#V],this.#o=this.#J(e.rows,this.#X),!this.#C&&e.initialSort&&(this.#s=e.initialSort),this.#s&&this.#Q(this.#s),this.#a=e.paddingRowsCount,this.#R()}#Y(e,t,o){let s=!1;const{key:r,text:i,negative:n,regex:l}=t;let a;return a=R(r?[y(e,r)]:e.cells.filter((e=>o.has(e.columnId)))),l?s=l.test(a):i&&(s=a.includes(i.toLowerCase())),n?!s:s}#J(e,t){if(0===t.length)return[...e];const o=new Set(this.#t.filter((e=>e.visible)).map((e=>e.id)));return e.map((e=>{let s=!0;for(const r of t){if(!this.#Y(e,r,o)){s=!1;break}}return{...e,hidden:!s}}))}#Q(e){const{columnId:t,direction:o}=e;this.#o.sort(((e,s)=>{const r=y(e,t),i=y(s,t),n="number"==typeof r.value?r.value:String(r.value).toUpperCase(),l="number"==typeof i.value?i.value:String(i.value).toUpperCase();return n<l?"ASC"===o?-1:1:n>l?"ASC"===o?1:-1:0})),this.#R()}#E(e){const{column:t}=e.data;t.sortable&&this.#Z(t)}#Z(e){if(this.#s&&this.#s.columnId===e.id){const{columnId:e,direction:t}=this.#s;this.#s="DESC"===t?null:{columnId:e,direction:"DESC"}}else this.#s={columnId:e.id,direction:"ASC"};const t=e.title;this.#s?(this.#Q(this.#s),o.ARIAUtils.alert("ASC"===this.#s.direction?P(B.sortInAscendingOrder,{PH1:t||""}):P(B.sortInDescendingOrder,{PH1:t||""}))):(this.#o=this.#J(this.#K,this.#X),this.#R(),o.ARIAUtils.alert(P(B.sortingCanceled,{PH1:t||""})))}#ee(e){this.#Z(e.data.column)}#te(){this.#s=null,this.#o=[...this.#K],this.#R()}#R(){s.render(s.html`
      <${U.litTagName} .data=${{columns:this.#t,rows:this.#o,activeSort:this.#s,contextMenus:this.#n,label:this.#l,paddingRowsCount:this.#a,showScrollbar:this.#c,striped:this.#d,autoScrollToBottom:this.#h}}
        @columnheaderclick=${this.#E}
        @contextmenucolumnsortclick=${this.#ee}
        @contextmenuheaderresetclick=${this.#te}
     ></${U.litTagName}>
    `,this.#e,{host:this}),this.#C=!0}}customElements.define("devtools-data-grid-controller",j);var W=Object.freeze({__proto__:null,DataGridController:j});class G extends o.Widget.VBox{dataGrid;#oe;#se;constructor(e){super(!0,!0),this.dataGrid=new j,this.dataGrid.data=e,this.#se=e,this.contentElement.appendChild(this.dataGrid),this.#oe=new a.Throttler.Throttler(0)}data(){return this.#se}update(e){this.#se=e,this.#oe.schedule((async()=>{this.dataGrid.data=e}))}}var q=Object.freeze({__proto__:null,DataGridControllerIntegrator:G});export{L as DataGrid,W as DataGridController,q as DataGridControllerIntegrator,f as DataGridEvents,x as DataGridRenderers,k as DataGridUtils};
