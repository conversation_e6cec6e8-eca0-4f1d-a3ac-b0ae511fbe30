import*as e from"../helpers/helpers.js";import*as n from"../../lit-html/lit-html.js";const i=new CSSStyleSheet;i.replaceSync('\n  :host {\n    --current-main-area-size: 50%;\n    --resizer-size: 3px;\n    --min-main-area-size: 200px;\n    --min-sidebar-size: 150px;\n    --main-area-size: calc(max(var(--current-main-area-size), var(--min-main-area-size)));\n\n    height: 100%;\n    width: 100%;\n    display: block;\n    overflow: auto;\n  }\n\n  .wrapper {\n    display: flex;\n    flex-direction: row;\n    height: 100%;\n    width: 100%;\n    container: sidebar / size; /* stylelint-disable-line property-no-unknown */\n  }\n\n  .container {\n    --resizer-position: calc(min(var(--main-area-size), calc(100% - var(--min-sidebar-size))));\n    --min-container-size: calc(var(--min-sidebar-size) + var(--min-main-area-size) + var(--resizer-size));\n\n    display: flex;\n    flex-direction: row;\n    height: 100%;\n    width: 100%;\n    position: relative;\n    gap: var(--resizer-size);\n\n    min-width: var(--min-container-size);\n  }\n\n  #resizer {\n    background-color: var(--sys-color-surface1);\n    position: absolute;\n    user-select: none;\n\n    /* horizontal */\n    width: var(--resizer-size);\n    cursor: col-resize;\n    left: var(--resizer-position);\n    bottom: 0;\n    top: 0;\n  }\n\n  slot {\n    overflow: auto;\n    display: block;\n  }\n\n  slot[name="main"] {\n\n    /* horizontal */\n    width: var(--resizer-position);\n    min-width: var(--min-main-area-size);\n  }\n\n  slot[name="sidebar"] {\n    flex: 1 0 0;\n\n    min-width: var(--min-sidebar-size);\n  }\n\n  .horizontal .container {\n    flex-direction: column;\n    min-height: var(--min-container-size);\n    min-width: auto;\n  }\n\n  .horizontal #resizer {\n    width: auto;\n    height: var(--resizer-size);\n    cursor: row-resize;\n    top: var(--resizer-position);\n    left: 0;\n    right: 0;\n  }\n\n  .horizontal slot[name="main"] {\n    width: auto;\n    min-width: auto;\n    height: var(--resizer-position);\n    min-height: var(--min-main-area-size);\n  }\n\n  .horizontal slot[name="sidebar"] {\n    min-width: auto;\n    min-height: var(--min-sidebar-size);\n  }\n');class s extends HTMLElement{static litTagName=n.literal`devtools-split-view`;#e=this.attachShadow({mode:"open"});#n=[0,0];#i=0;#s=[0,0];#t;#o=!1;connectedCallback(){this.style.setProperty("--current-main-area-size","60%"),this.#e.adoptedStyleSheets=[i],this.#t=new ResizeObserver((e=>this.#r(e[0].contentRect))),this.#t.observe(this),this.#a()}get horizontal(){return this.#o}set horizontal(n){this.#o=n,e.ScheduledRender.scheduleRender(this,this.#a)}#r=n=>{const i=this.#i;n.width<=600&&n.height>=600||this.#o?this.#i=1:this.#i=0,this.#i!==i&&e.ScheduledRender.scheduleRender(this,this.#a)};#h=e=>{const n=this.#e.querySelector("slot[name=main]");if(!n)throw new Error("Main slot not found");const i=n.getBoundingClientRect();this.#s=[i.width,i.height],this.#n=[e.clientX,e.clientY],window.addEventListener("mousemove",this.#l,!0),window.addEventListener("mouseup",this.#d,!0)};#d=()=>{window.removeEventListener("mousemove",this.#l,!0),window.removeEventListener("mouseup",this.#d,!0)};#l=e=>{const n=[e.clientX,e.clientY][this.#i]-this.#n[this.#i],i=this.getBoundingClientRect(),s=[i.width,i.height],t=100*(this.#s[this.#i]+n)/s[this.#i];this.style.setProperty("--current-main-area-size",t+"%")};#a=()=>{n.render(n.html`
        <div class="wrapper ${1===this.#i?"horizontal":""}">
          <div class="container">
            <slot name="main"></slot>
            <div id="resizer" @mousedown=${this.#h}></div>
            <slot name="sidebar"></slot>
          </div>
        </div>
      `,this.#e,{host:this})}}customElements.define("devtools-split-view",s);var t=Object.freeze({__proto__:null,SplitView:s});export{t as SplitView};
