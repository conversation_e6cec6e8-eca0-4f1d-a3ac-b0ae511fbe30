import*as t from"../../../../core/common/common.js";import*as e from"../../../../core/platform/platform.js";import*as o from"../../../../core/host/host.js";import*as s from"../../../../core/i18n/i18n.js";import*as r from"../../../../core/root/root.js";import*as n from"../../../components/icon_button/icon_button.js";import*as a from"../../legacy.js";import*as i from"../../../../core/sdk/sdk.js";import*as l from"../../../components/srgb_overlay/srgb_overlay.js";import*as c from"../../../visual_logging/visual_logging.js";const h=t=>t.map((t=>e.StringUtilities.stringifyWithPrecision(t,2))),d=t=>`${t[0]} ${t[1]} ${t[2]} / ${t[3]}`,p={rgb:{label:"RGBA",toValues:function(t){return h(t.as("rgba").canonicalRGBA())},fromValues:function(e){return t.Color.parse(`rgb(${d(e)})`)}},hsl:{label:"HSLA",toValues:function(t){const e=h(t.as("hsla").canonicalHSLA());return e[1]=e[1]+"%",e[2]=e[2]+"%",e},fromValues:function(e){return t.Color.parse(`hsl(${d(e)})`)}},hwb:{label:"HWBA",toValues:function(t){const e=h(t.as("hwba").canonicalHWBA());return e[1]=e[1]+"%",e[2]=e[2]+"%",e},fromValues:function(e){return t.Color.parse(`hwb(${d(e)})`)}},lch:{label:"lchA",toValues:function(t){const e=t.as("lch");return h([e.l,e.c,e.h,e.alpha??1])},fromValues:function(e){return t.Color.parse(`lch(${d(e)})`)}},oklch:{label:"lchA",toValues:function(t){const e=t.as("oklch");return h([e.l,e.c,e.h,e.alpha??1])},fromValues:function(e){return t.Color.parse(`oklch(${d(e)})`)}},lab:{label:"labA",toValues:function(t){const e=t.as("lab");return h([e.l,e.a,e.b,e.alpha??1])},fromValues:function(e){return t.Color.parse(`lab(${d(e)})`)}},oklab:{label:"labA",toValues:function(t){const e=t.as("oklab");return h([e.l,e.a,e.b,e.alpha??1])},fromValues:function(e){return t.Color.parse(`oklab(${d(e)})`)}},srgb:{label:"RGBA",toValues:function(t){const e=t.as("srgb");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(srgb ${d(e)})`)}},"srgb-linear":{label:"RGBA",toValues:function(t){const e=t.as("srgb-linear");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(srgb-linear ${d(e)})`)}},"display-p3":{label:"RGBA",toValues(t){const e=t.as("display-p3");return h([e.p0,e.p1,e.p2,1])},fromValues:e=>t.Color.parse(`color(display-p3 ${d(e)})`)},"a98-rgb":{label:"RGBA",toValues:function(t){const e=t.as("a98-rgb");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(a98-rgb ${d(e)})`)}},"prophoto-rgb":{label:"RGBA",toValues:function(t){const e=t.as("prophoto-rgb");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(prophoto-rgb ${d(e)})`)}},rec2020:{label:"RGBA",toValues:function(t){const e=t.as("rec2020");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(rec2020 ${d(e)})`)}},xyz:{label:"xyzA",toValues:function(t){const e=t.as("xyz");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(xyz ${d(e)})`)}},"xyz-d50":{label:"xyzA",toValues:function(t){const e=t.as("xyz-d50");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(xyz-d50 ${d(e)})`)}},"xyz-d65":{label:"xyzA",toValues:function(t){const e=t.as("xyz-d65");return h([e.p0,e.p1,e.p2,e.alpha??1])},fromValues:function(e){return t.Color.parse(`color(xyz-d65 ${d(e)})`)}}};var u=Object.freeze({__proto__:null,colorFormatSpec:p});const g={noContrastInformationAvailable:"No contrast information available",contrastRatio:"Contrast ratio",showMore:"Show more",pickBackgroundColor:"Pick background color",toggleBackgroundColorPicker:"Toggle background color picker",useSuggestedColorStoFixLow:"Use suggested color {PH1}to fix low contrast",apca:"APCA",aa:"AA",placeholderWithColon:": {PH1}",aaa:"AAA",showLess:"Show less"},m=s.i18n.registerUIStrings("ui/legacy/components/color_picker/ContrastDetails.ts",g),C=s.i18n.getLocalizedString.bind(void 0,m);class b extends t.ObjectWrapper.ObjectWrapper{contrastInfo;elementInternal;toggleMainColorPicker;expandedChangedCallback;colorSelectedCallback;expandedInternal;passesAA;contrastUnknown;visibleInternal;noContrastInfoAvailable;contrastValueBubble;contrastValue;contrastValueBubbleIcons;expandButton;expandedDetails;contrastThresholds;contrastAA;contrastPassFailAA;contrastAAA;contrastPassFailAAA;contrastAPCA;contrastPassFailAPCA;chooseBgColor;bgColorPickerButton;bgColorPickedBound;bgColorSwatch;constructor(t,e,o,s,r){super(),this.contrastInfo=t,this.elementInternal=e.createChild("div","spectrum-contrast-details collapsed"),this.toggleMainColorPicker=o,this.expandedChangedCallback=s,this.colorSelectedCallback=r,this.expandedInternal=!1,this.passesAA=!0,this.contrastUnknown=!1,this.visibleInternal=!1,this.noContrastInfoAvailable=e.createChild("div","no-contrast-info-available"),this.noContrastInfoAvailable.textContent=C(g.noContrastInformationAvailable),this.noContrastInfoAvailable.classList.add("hidden");const i=this.elementInternal.createChild("div");i.addEventListener("click",this.topRowClicked.bind(this));const l=i.createChild("div","container");a.UIUtils.createTextChild(l,C(g.contrastRatio)),this.contrastValueBubble=l.createChild("span","contrast-details-value"),this.contrastValue=this.contrastValueBubble.createChild("span"),this.contrastValueBubbleIcons=[],this.contrastValueBubbleIcons.push(this.contrastValueBubble.appendChild(n.Icon.create("checkmark"))),this.contrastValueBubbleIcons.push(this.contrastValueBubble.appendChild(n.Icon.create("check-double"))),this.contrastValueBubbleIcons.push(this.contrastValueBubble.appendChild(n.Icon.create("clear"))),this.contrastValueBubbleIcons.forEach((t=>t.addEventListener("click",(t=>{b.showHelp(),t.consume(!1)}))));const c=new a.Toolbar.Toolbar("expand",l);this.expandButton=new a.Toolbar.ToolbarButton(C(g.showMore),"chevron-down"),this.expandButton.addEventListener("Click",this.expandButtonClicked.bind(this)),a.ARIAUtils.setExpanded(this.expandButton.element,!1),c.appendToolbarItem(this.expandButton),this.expandedDetails=this.elementInternal.createChild("div","expanded-details"),a.ARIAUtils.setControls(this.expandButton.element,this.expandedDetails),this.contrastThresholds=this.expandedDetails.createChild("div","contrast-thresholds"),this.contrastAA=this.contrastThresholds.createChild("div","contrast-threshold"),this.contrastPassFailAA=this.contrastAA.createChild("div","contrast-pass-fail"),this.contrastAAA=this.contrastThresholds.createChild("div","contrast-threshold"),this.contrastPassFailAAA=this.contrastAAA.createChild("div","contrast-pass-fail"),this.contrastAPCA=this.contrastThresholds.createChild("div","contrast-threshold"),this.contrastPassFailAPCA=this.contrastAPCA.createChild("div","contrast-pass-fail"),this.chooseBgColor=this.expandedDetails.createChild("div","contrast-choose-bg-color"),this.chooseBgColor.textContent=C(g.pickBackgroundColor);const h=this.expandedDetails.createChild("div","background-color"),d=new a.Toolbar.Toolbar("spectrum-eye-dropper",h);this.bgColorPickerButton=new a.Toolbar.ToolbarToggle(C(g.toggleBackgroundColorPicker),"color-picker","color-picker-filled"),this.bgColorPickerButton.addEventListener("Click",this.toggleBackgroundColorPickerInternal.bind(this,void 0,!0)),d.appendToolbarItem(this.bgColorPickerButton),this.bgColorPickedBound=this.bgColorPicked.bind(this),this.bgColorSwatch=new f(h),this.contrastInfo.addEventListener("ContrastInfoUpdated",this.update.bind(this))}showNoContrastInfoAvailableMessage(){this.noContrastInfoAvailable.classList.remove("hidden")}hideNoContrastInfoAvailableMessage(){this.noContrastInfoAvailable.classList.add("hidden")}computeSuggestedColor(e){const o=this.contrastInfo.color(),s=this.contrastInfo.bgColor();if(!o||!s)return;if("APCA"===e){const e=this.contrastInfo.contrastRatioAPCAThreshold();if(null===e)return;return t.Color.findFgColorForContrastAPCA(o,s,e+1)}const r=this.contrastInfo.contrastRatioThreshold(e);return r?t.Color.findFgColorForContrast(o,s,r+.1):void 0}onSuggestColor(t){const e=this.computeSuggestedColor(t);e&&this.colorSelectedCallback(e)}createFixColorButton(t,e){const o=t.createChild("button","contrast-fix-button"),s=e.asString(this.contrastInfo.colorFormat()),r=s?s+" ":"",n=C(g.useSuggestedColorStoFixLow,{PH1:r});return a.ARIAUtils.setLabel(o,n),a.Tooltip.Tooltip.install(o,n),o.tabIndex=0,o.style.backgroundColor=r,o}update(){if(this.contrastInfo.isNull())return this.showNoContrastInfoAvailableMessage(),void this.setVisible(!1);this.setVisible(!0),this.hideNoContrastInfoAvailableMessage();const t=r.Runtime.experiments.isEnabled("apca"),o=this.contrastInfo.color(),s=this.contrastInfo.bgColor();if(t){const t=this.contrastInfo.contrastRatioAPCA();if(null===t||!s||!o)return this.contrastUnknown=!0,this.contrastValue.textContent="",this.contrastValueBubble.classList.add("contrast-unknown"),this.chooseBgColor.classList.remove("hidden"),this.contrastThresholds.classList.add("hidden"),void this.showNoContrastInfoAvailableMessage();this.contrastUnknown=!1,this.chooseBgColor.classList.add("hidden"),this.contrastThresholds.classList.remove("hidden"),this.contrastValueBubble.classList.remove("contrast-unknown"),this.contrastValue.textContent=`${e.NumberUtilities.floor(t,2)}%`;const r=this.contrastInfo.contrastRatioAPCAThreshold(),a=!(!t||!r)&&Math.abs(t)>=r;this.contrastPassFailAPCA.removeChildren();const i=this.contrastPassFailAPCA.createChild("span","contrast-link-label");if(i.textContent=C(g.apca),null!==r&&(this.contrastPassFailAPCA.createChild("span").textContent=`: ${r.toFixed(2)}%`),a){const t=new n.Icon.Icon;t.data={iconName:"checkmark",color:"var(--icon-checkmark-green)",width:"20px",height:"14px"},this.contrastPassFailAPCA.appendChild(t)}else{const t=new n.Icon.Icon;t.data={iconName:"clear",color:"var(--icon-error)",width:"14px",height:"14px"},this.contrastPassFailAPCA.appendChild(t);const e=this.computeSuggestedColor("APCA");if(e){this.createFixColorButton(this.contrastPassFailAPCA,e).addEventListener("click",(()=>this.onSuggestColor("APCA")))}}return i.addEventListener("click",(t=>b.showHelp())),this.elementInternal.classList.toggle("contrast-fail",!a),this.contrastValueBubble.classList.toggle("contrast-aa",a),void this.bgColorSwatch.setColors(o,s)}const a=this.contrastInfo.contrastRatio();if(!a||!s||!o)return this.contrastUnknown=!0,this.contrastValue.textContent="",this.contrastValueBubble.classList.add("contrast-unknown"),this.chooseBgColor.classList.remove("hidden"),this.contrastThresholds.classList.add("hidden"),void this.showNoContrastInfoAvailableMessage();this.contrastUnknown=!1,this.chooseBgColor.classList.add("hidden"),this.contrastThresholds.classList.remove("hidden"),this.contrastValueBubble.classList.remove("contrast-unknown"),this.contrastValue.textContent=String(e.NumberUtilities.floor(a,2)),this.bgColorSwatch.setColors(o,s);const i=this.contrastInfo.contrastRatioThreshold("aa")||0;this.passesAA=(this.contrastInfo.contrastRatio()||0)>=i,this.contrastPassFailAA.removeChildren();const l=this.contrastPassFailAA.createChild("span","contrast-link-label");if(l.textContent=C(g.aa),this.contrastPassFailAA.createChild("span").textContent=C(g.placeholderWithColon,{PH1:i.toFixed(1)}),this.passesAA){const t=new n.Icon.Icon;t.data={iconName:"checkmark",color:"var(--icon-checkmark-green)",width:"20px",height:"14px"},this.contrastPassFailAA.appendChild(t)}else{const t=new n.Icon.Icon;t.data={iconName:"clear",color:"var(--icon-error)",width:"14px",height:"14px"},this.contrastPassFailAA.appendChild(t);const e=this.computeSuggestedColor("aa");if(e){this.createFixColorButton(this.contrastPassFailAA,e).addEventListener("click",(()=>this.onSuggestColor("aa")))}}const c=this.contrastInfo.contrastRatioThreshold("aaa")||0,h=(this.contrastInfo.contrastRatio()||0)>=c;this.contrastPassFailAAA.removeChildren();const d=this.contrastPassFailAAA.createChild("span","contrast-link-label");if(d.textContent=C(g.aaa),this.contrastPassFailAAA.createChild("span").textContent=C(g.placeholderWithColon,{PH1:c.toFixed(1)}),h){const t=new n.Icon.Icon;t.data={iconName:"checkmark",color:"var(--icon-checkmark-green)",width:"20px",height:"14px"},this.contrastPassFailAAA.appendChild(t)}else{const t=new n.Icon.Icon;t.data={iconName:"clear",color:"var(--icon-error)",width:"14px",height:"14px"},this.contrastPassFailAAA.appendChild(t);const e=this.computeSuggestedColor("aaa");if(e){this.createFixColorButton(this.contrastPassFailAAA,e).addEventListener("click",(()=>this.onSuggestColor("aaa")))}}[l,d].forEach((t=>t.addEventListener("click",(()=>b.showHelp())))),this.elementInternal.classList.toggle("contrast-fail",!this.passesAA),this.contrastValueBubble.classList.toggle("contrast-aa",this.passesAA&&!h),this.contrastValueBubble.classList.toggle("contrast-aaa",h)}static showHelp(){o.InspectorFrontendHost.InspectorFrontendHostInstance.openInNewTab(a.UIUtils.addReferrerToURL("https://web.dev/color-and-contrast-accessibility/"))}setVisible(t){this.visibleInternal=t,this.elementInternal.classList.toggle("hidden",!t)}visible(){return this.visibleInternal}element(){return this.elementInternal}expandButtonClicked(){const t=this.contrastValueBubble.getComponentSelection();t&&t.empty(),this.toggleExpanded()}topRowClicked(t){const e=this.contrastValueBubble.getComponentSelection();e&&e.empty(),this.toggleExpanded(),t.consume(!0)}toggleExpanded(){this.expandedInternal=!this.expandedInternal,a.ARIAUtils.setExpanded(this.expandButton.element,this.expandedInternal),this.elementInternal.classList.toggle("collapsed",!this.expandedInternal),this.expandedInternal?(this.toggleMainColorPicker(!1),this.expandButton.setGlyph("chevron-up"),this.expandButton.setTitle(C(g.showLess)),this.contrastUnknown&&this.toggleBackgroundColorPickerInternal(!0)):(this.toggleBackgroundColorPickerInternal(!1),this.expandButton.setGlyph("chevron-down"),this.expandButton.setTitle(C(g.showMore))),this.expandedChangedCallback()}collapse(){this.elementInternal.classList.remove("expanded"),this.toggleBackgroundColorPickerInternal(!1),this.toggleMainColorPicker(!1)}expanded(){return this.expandedInternal}backgroundColorPickerEnabled(){return this.bgColorPickerButton.toggled()}toggleBackgroundColorPicker(t){this.toggleBackgroundColorPickerInternal(t,!1)}toggleBackgroundColorPickerInternal(t,e=!0){void 0===t&&(t=!this.bgColorPickerButton.toggled()),this.bgColorPickerButton.setToggled(t),e&&this.dispatchEventToListeners("BackgroundColorPickerWillBeToggled",t),o.InspectorFrontendHost.InspectorFrontendHostInstance.setEyeDropperActive(t),t?o.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(o.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.bgColorPickedBound):o.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(o.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.bgColorPickedBound)}bgColorPicked({data:e}){const s=[e.r,e.g,e.b,(e.a/2.55|0)/100],r=t.Color.Legacy.fromRGBA(s);this.contrastInfo.setBgColor(r),this.toggleBackgroundColorPickerInternal(!1),o.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront()}}class f{parentElement;swatchElement;swatchInnerElement;textPreview;constructor(t){this.parentElement=t,this.swatchElement=t.createChild("span","swatch contrast swatch-inner-white"),this.swatchInnerElement=this.swatchElement.createChild("span","swatch-inner"),this.textPreview=this.swatchElement.createChild("div","text-preview"),this.textPreview.textContent="Aa"}setColors(t,e){this.textPreview.style.color=t.asString("rgba"),this.swatchInnerElement.style.backgroundColor=e.asString("rgba"),this.swatchElement.classList.toggle("swatch-inner-white",e.as("hsl").l>.9)}}var v=Object.freeze({__proto__:null,ContrastDetails:b,Swatch:f});class x extends t.ObjectWrapper.ObjectWrapper{isNullInternal;contrastRatioInternal;contrastRatioAPCAInternal;contrastRatioThresholds;contrastRationAPCAThreshold;fgColor;bgColorInternal;colorFormatInternal;constructor(e){if(super(),this.isNullInternal=!0,this.contrastRatioInternal=null,this.contrastRatioAPCAInternal=null,this.contrastRatioThresholds=null,this.contrastRationAPCAThreshold=0,this.fgColor=null,this.bgColorInternal=null,!e)return;if(!e.computedFontSize||!e.computedFontWeight||!e.backgroundColors||1!==e.backgroundColors.length)return;this.isNullInternal=!1,this.contrastRatioThresholds=t.ColorUtils.getContrastThreshold(e.computedFontSize,e.computedFontWeight),this.contrastRationAPCAThreshold=t.ColorUtils.getAPCAThreshold(e.computedFontSize,e.computedFontWeight);const o=e.backgroundColors[0],s=t.Color.parse(o)?.asLegacyColor();s&&this.setBgColorInternal(s)}isNull(){return this.isNullInternal}setColor(t,e){this.fgColor=t,this.colorFormatInternal=e,this.updateContrastRatio(),this.dispatchEventToListeners("ContrastInfoUpdated")}colorFormat(){return this.colorFormatInternal}color(){return this.fgColor}contrastRatio(){return this.contrastRatioInternal}contrastRatioAPCA(){return this.contrastRatioAPCAInternal}contrastRatioAPCAThreshold(){return this.contrastRationAPCAThreshold}setBgColor(t){this.setBgColorInternal(t),this.dispatchEventToListeners("ContrastInfoUpdated")}setBgColorInternal(e){if(this.bgColorInternal=e,!this.fgColor)return;const o=this.fgColor.rgba();if(e.hasAlpha()){const s=t.ColorUtils.blendColors(e.rgba(),o);this.bgColorInternal=new t.Color.Legacy(s,"rgba")}this.contrastRatioInternal=t.ColorUtils.contrastRatio(o,this.bgColorInternal.rgba()),this.contrastRatioAPCAInternal=t.ColorUtils.contrastRatioAPCA(this.fgColor.rgba(),this.bgColorInternal.rgba())}bgColor(){return this.bgColorInternal}updateContrastRatio(){this.bgColorInternal&&this.fgColor&&(this.contrastRatioInternal=t.ColorUtils.contrastRatio(this.fgColor.rgba(),this.bgColorInternal.rgba()),this.contrastRatioAPCAInternal=t.ColorUtils.contrastRatioAPCA(this.fgColor.rgba(),this.bgColorInternal.rgba()))}contrastRatioThreshold(t){return this.contrastRatioThresholds?this.contrastRatioThresholds[t]:null}}var w=Object.freeze({__proto__:null,ContrastInfo:x});class A{contrastInfo;visible;contrastRatioSVG;contrastRatioLines;width;height;contrastRatioLineBuilder;contrastRatioLinesThrottler;drawContrastRatioLinesBound;constructor(e,o){this.contrastInfo=e,this.visible=!1,this.contrastRatioSVG=a.UIUtils.createSVGChild(o,"svg","spectrum-contrast-container fill"),this.contrastRatioLines=new Map,r.Runtime.experiments.isEnabled("apca")?this.contrastRatioLines.set("APCA",a.UIUtils.createSVGChild(this.contrastRatioSVG,"path","spectrum-contrast-line")):(this.contrastRatioLines.set("aa",a.UIUtils.createSVGChild(this.contrastRatioSVG,"path","spectrum-contrast-line")),this.contrastRatioLines.set("aaa",a.UIUtils.createSVGChild(this.contrastRatioSVG,"path","spectrum-contrast-line"))),this.width=0,this.height=0,this.contrastRatioLineBuilder=new y(this.contrastInfo),this.contrastRatioLinesThrottler=new t.Throttler.Throttler(0),this.drawContrastRatioLinesBound=this.drawContrastRatioLines.bind(this),this.contrastInfo.addEventListener("ContrastInfoUpdated",this.update.bind(this))}update(){this.visible&&!this.contrastInfo.isNull()&&(r.Runtime.experiments.isEnabled("apca")&&null===this.contrastInfo.contrastRatioAPCA()||this.contrastInfo.contrastRatio()&&this.contrastRatioLinesThrottler.schedule(this.drawContrastRatioLinesBound))}setDimensions(t,e){this.width=t,this.height=e,this.update()}setVisible(t){this.visible=t,this.contrastRatioSVG.classList.toggle("hidden",!t),this.update()}async drawContrastRatioLines(){for(const[t,e]of this.contrastRatioLines){const o=this.contrastRatioLineBuilder.drawContrastRatioLine(this.width,this.height,t);o?e.setAttribute("d",o):e.removeAttribute("d")}}}class y{contrastInfo;constructor(t){this.contrastInfo=t}drawContrastRatioLine(e,o,s){const n=r.Runtime.experiments.isEnabled("apca"),a=n?this.contrastInfo.contrastRatioAPCAThreshold():this.contrastInfo.contrastRatioThreshold(s);if(!e||!o||null===a)return null;const i=.02,l=this.contrastInfo.color(),c=this.contrastInfo.bgColor();if(!l||!c)return null;const h=l.rgba(),d=l.as("hsl").hsva(),p=c.rgba(),u=t.ColorUtils.luminance(p);let g=t.ColorUtils.blendColors(h,p);const m=t.ColorUtils.luminance(g)>u,C=n?t.ColorUtils.desiredLuminanceAPCA(u,a,m):t.Color.desiredLuminance(u,a,m);if(n&&Math.abs(Math.round(t.ColorUtils.contrastRatioByLuminanceAPCA(C,u)))<a)return null;let b=d[2],f=0;const v=[d[0],0,0,d[3]];let x=[];const w=t.Color.hsva2rgba(v);g=t.ColorUtils.blendColors(w,p);let A,y=e=>t.ColorUtils.luminance(t.ColorUtils.blendColors(t.Color.Legacy.fromHSVA(e).rgba(),p));for(r.Runtime.experiments.isEnabled("apca")&&(y=e=>t.ColorUtils.luminanceAPCA(t.ColorUtils.blendColors(t.Color.Legacy.fromHSVA(e).rgba(),p))),A=0;A<1.02;A+=i){A=Math.min(1,A),v[1]=A,v[2]=b+f*i;const s=t.Color.approachColorValue(v,p,2,C,y);if(null===s)break;f=0===A?0:(s-b)/i,b=s,x.push(x.length?"L":"M"),x.push((A*e).toFixed(2)),x.push(((1-s)*o).toFixed(2))}return A<1.02&&(A-=i,v[2]=1,A=t.Color.approachColorValue(v,p,1,C,y),null!==A&&(x=x.concat(["L",(A*e).toFixed(2),"-0.1"]))),0===x.length?null:x.join(" ")}}var k=Object.freeze({__proto__:null,ContrastOverlay:A,ContrastRatioLineBuilder:y});const E={colorClippedTooltipText:"This color was clipped to match the format's gamut. The actual result was {PH1}"},I=s.i18n.registerUIStrings("ui/legacy/components/color_picker/FormatPickerContextMenu.ts",E),P=s.i18n.getLocalizedString.bind(void 0,I);class F{#t;constructor(t){this.#t=t}async show(e,o){let s;const r=new Promise((t=>{s=t})),n=["hex","hexa","rgb","rgba","hsl","hwb"],i=["lch","oklch","lab","oklab","srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"],l=new a.ContextMenu.ContextMenu(e,{onSoftMenuClosed:()=>s?.()}),c=l.section("legacy"),h=l.section("wide"),d=l.section("color-function").appendSubMenuItem("color()",!1,"color").section();if(!(this.#t instanceof t.Color.Nickname)){const t=this.#t.asLegacyColor().nickname();t&&this.addColorToSection(t,c,o)}if(!(this.#t instanceof t.Color.ShortHex)){const t=this.#t.as(1===(this.#t.alpha??1)?"hex":"hexa").shortHex();t&&this.addColorToSection(t,c,o)}for(const e of[...n,...i]){if(e===this.#t.format())continue;const s=this.#t.as(e),r=n.includes(e)?c:s instanceof t.Color.ColorFunction?d:h;this.addColorToSection(s,r,o)}await l.show(),await r}addColorToSection(e,o,s){if(e instanceof t.Color.Legacy){const t=1!==(this.#t.alpha??1);if(null!==e.alpha!==t)return}const r=e.asString();if(!r)return;let a;e.isGamutClipped()&&(a=new n.Icon.Icon,a.data={iconName:"warning",color:"var(--icon-default)",width:"16px",height:"16px"},a.style.marginLeft="1px",a.style.marginTop="-1px",a.style.minWidth="16px",a.style.minHeight="16px");const i=a?P(E.colorClippedTooltipText,{PH1:e.getAsRawString()??"none"}):void 0;o.appendItem(r,(()=>s(e)),{additionalElement:a,tooltip:i,jslogContext:e.isGamutClipped()?"color":"clipped-color"})}}var B=Object.freeze({__proto__:null,FormatPickerContextMenu:F});const S=new CSSStyleSheet;S.replaceSync(':host{width:232px;height:319px;user-select:none;overflow:hidden}:selection{background-color:var(--sys-color-tonal-container);color:var(--sys-color-on-tonal-container)}.spectrum-color{position:relative;width:232px;height:127px;border-radius:2px 2px 0 0;overflow:hidden;flex:none;touch-action:none}.spectrum-dragger,\n.spectrum-slider{user-select:none}.spectrum-dragger{border-radius:12px;height:12px;width:12px;border:1px solid var(--sys-color-surface);cursor:move;z-index:1;position:absolute;top:0;left:0;background:var(--sys-color-inverse-surface);box-shadow:var(--drop-shadow)}.spectrum-slider{position:absolute;top:-1px;cursor:ew-resize;width:13px;height:13px;border-radius:13px;background-color:var(--sys-color-neutral-container);box-shadow:var(--drop-shadow)}.spectrum-color:focus .spectrum-dragger{border:1px solid var(--sys-color-state-focus-ring)}.spectrum-tools{position:relative;height:110px;width:100%;flex:none}.spectrum-hue{top:16px;background:linear-gradient(to left,#f00 0%,#ff0 17%,#0f0 33%,#0ff 50%,#00f 67%,#f0f 83%,#f00 100%)}.spectrum-hue.display-p3{background:linear-gradient(to left,color(display-p3 1 0 0) 0%,color(display-p3 1 1 0) 17%,color(display-p3 0 1 0) 33%,color(display-p3 0 1 1) 50%,color(display-p3 0 0 1) 67%,color(display-p3 1 0 1) 83%,color(display-p3 1 0 0) 100%)}.spectrum-alpha{top:35px;background-image:var(--image-file-checker);background-size:12px 11px}.spectrum-alpha-background{height:100%;border-radius:2px}.spectrum-hue,\n.spectrum-alpha{position:absolute;left:86px;width:130px;height:11px;border-radius:2px;touch-action:none}.spectrum-hue:focus-visible .spectrum-slider,\n.spectrum-alpha:focus-visible .spectrum-slider{border:1px solid var(--sys-color-state-focus-ring);width:14px;height:14px;border-radius:14px}.spectrum-sat,\n.-theme-preserve{background-image:linear-gradient(to right,#fff,rgb(204 154 129/0%))}.spectrum-val,\n.-theme-preserve{background-image:linear-gradient(to top,#000,rgb(204 154 129/0%))}.spectrum-contrast-details{position:relative;background-color:var(--sys-color-cdt-base-container);width:100%;height:83px;top:0;font-size:13px;color:var(--sys-color-on-surface);border-top:1px solid var(--sys-color-divider);line-height:initial;overflow:hidden;flex:none}.spectrum-contrast-details.collapsed{height:36px;flex:none}.spectrum-contrast-details div.toolbar.expand{position:absolute;right:6px;top:6px;margin:0}.spectrum-contrast-details.visible{display:initial}.spectrum-contrast-details div.container{margin:10px}.spectrum-contrast-details .expanded-details{display:flex;margin:12px 12px 0 4px}.spectrum-contrast-details.collapsed .expanded-details{display:none}.contrast-pass-fail{margin-left:0.5em;display:flex;align-items:center}.contrast-choose-bg-color{margin:8px 0 0 5px;font-style:italic}.spectrum-contrast-details .contrast-choose-bg-color,\n.spectrum-contrast-details .contrast-thresholds{width:150px}.contrast-threshold:first-child{margin-bottom:5px}.contrast-fix-button{cursor:pointer;font-size:13px;padding:0;margin:0 0 0 10px;background:0;width:12px;height:12px;border:1px solid var(--sys-color-neutral-outline);display:inline-block;position:relative}.contrast-fix-button::after{content:" ";width:13px;height:13px;background-image:var(--image-file-refresh);background-size:contain;position:absolute;left:5.5px;top:3.5px;background-color:var(--sys-color-cdt-base-container);border-radius:50%}.contrast-fix-button:hover,\n.contrast-fix-button:focus{border:1px solid var(--sys-color-state-focus-ring);transform:scale(1.2)}.contrast-link-label{cursor:pointer}.contrast-link-label:hover{text-decoration:underline}.spectrum-contrast-details .background-color{position:absolute;flex:none;right:12px}.spectrum-eye-dropper{width:32px;height:24px;position:relative;left:8px;top:17px;cursor:pointer}.spectrum-contrast-details .spectrum-eye-dropper{top:2px;right:34px;position:absolute;left:auto}.contrast-details-value{color:var(--sys-color-on-surface);margin:1px 5px;user-select:text}.contrast-pass-fail devtools-icon{margin-left:5px}.contrast-details-value devtools-icon{display:none;margin-left:5px;color:var(--sys-color-on-surface)}.spectrum-contrast-details .toolbar-state-on devtools-icon{color:var(--sys-color-token-subtle)}devtools-icon.clear{transform:scale(0.7);color:var(--icon-error)}devtools-icon.checkmark,\ndevtools-icon.check-double{color:var(--icon-checkmark-green)}.spectrum-contrast-details .contrast-details-value.contrast-unknown{background-color:var(--sys-color-cdt-base-container);color:var(--sys-color-on-surface);width:3em;text-align:center}.contrast-details-value .clear,\n.contrast-details-value .checkmark,\n.contrast-details-value .check-double{cursor:pointer;vertical-align:-5px}.spectrum-contrast-details.contrast-fail .contrast-details-value .clear,\n.contrast-details-value.contrast-aa .checkmark,\n.contrast-details-value.contrast-aaa .check-double{display:inline-block}.swatch{width:32px;height:32px;margin:0;position:absolute;top:15px;left:44px;background-image:var(--image-file-checker);border-radius:16px}.swatch-inner,\n.swatch-overlay{position:absolute;width:100%;height:100%;display:inline-block;border-radius:16px}.swatch-inner-white{border:1px solid var(--sys-color-neutral-outline)}.swatch-overlay{cursor:pointer;opacity:0%;padding:4px}.swatch-overlay:hover,\n.swatch-overlay:focus-visible{background-color:var(--color-background-inverted-opacity-30);opacity:100%}.swatch-overlay:active{background-color:var(--color-background-inverted-opacity-50)}devtools-icon.copy-color-icon{color:var(--sys-color-cdt-base-container);margin-top:2px;margin-left:2px}.spectrum-text{position:absolute;top:60px;left:16px}.spectrum-text-value{display:inline-block;width:40px;overflow:hidden;text-align:center;margin-right:6px;line-height:20px;padding:0;color:var(--sys-color-on-surface);border:1px solid var(--sys-color-neutral-outline);white-space:nowrap}.spectrum-text-label{letter-spacing:39.5px;margin-top:8px;display:block;color:var(--sys-color-state-disabled);margin-left:16px;width:174px}.spectrum-text-hex > .spectrum-text-value{width:178px}.spectrum-text-hex > .spectrum-text-label{letter-spacing:normal;margin-left:0;text-align:center}.spectrum-switcher{border-radius:2px;height:20px;width:20px;padding:2px;border:none;background:none;margin:0}.spectrum-display-switcher{top:72px;position:absolute;right:10px}.spectrum-switcher:hover{background-color:var(--sys-color-state-hover-on-subtle)}.spectrum-switcher:focus-visible{background-color:var(--sys-color-state-focus-highlight)}.spectrum-palette-container{border-top:1px solid var(--sys-color-divider);position:relative;width:100%;padding:6px 24px 6px 6px;display:flex;flex-wrap:wrap}.spectrum-palette{display:flex;flex-wrap:wrap;width:198px}.spectrum-palette-color{width:12px;height:12px;flex:0 0 12px;border-radius:2px;margin:6px;cursor:pointer;position:relative;border:1px solid var(--sys-color-divider);background-position:-1px!important;z-index:14}.spectrum-palette-color-shadow{position:absolute;opacity:0%;margin:0;top:-5px;left:3px;border:0;border-radius:1px;width:11px;height:11px}.spectrum-palette-color:hover:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow,\n.spectrum-palette-color:focus:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow{opacity:20%}.spectrum-palette-color:hover:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow:first-child,\n.spectrum-palette-color:focus:not(.spectrum-shades-shown) > .spectrum-palette-color-shadow:first-child{opacity:60%;top:-3px;left:1px}.palette-color-shades{position:absolute;background-color:var(--sys-color-cdt-base-container);height:228px;width:28px;box-shadow:var(--drop-shadow);z-index:14;border-radius:2px;transform-origin:0 228px;margin-top:16px;margin-left:-8px}.spectrum-palette > .spectrum-palette-color.spectrum-shades-shown{z-index:15}.palette-color-shades > .spectrum-palette-color{margin:8px 0 0;margin-left:8px;width:12px}.spectrum-palette > .spectrum-palette-color{transition:transform 100ms cubic-bezier(0,0,0.2,1);will-change:transform;z-index:13}.palette-preview > .spectrum-palette-color{margin-top:1px}.spectrum-palette > .spectrum-palette-color.empty-color{border-color:transparent}.spectrum-palette-color:not(.has-material-shades):focus{border:1px solid var(--sys-color-state-focus-ring);transform:scale(1.4)}.palette-color-shades > .spectrum-palette-color:not(.empty-color):hover,\n.spectrum-palette > .spectrum-palette-color:not(.empty-color):not(.has-material-shades):hover{transform:scale(1.15)}.add-color-toolbar{margin-left:-3px;margin-top:-1px}.spectrum-palette-switcher{right:10px;top:0;margin-top:9px;position:absolute}.palette-panel{width:100%;position:absolute;top:100%;display:flex;flex-direction:column;background-color:var(--sys-color-cdt-base-container);z-index:14;transition:transform 200ms cubic-bezier(0,0,0.2,1),visibility 0s 200ms;border-top:1px solid var(--sys-color-divider);visibility:hidden}.palette-panel-showing > .palette-panel{transform:translateY(-100%);transition-delay:0s;visibility:visible}.palette-panel > div.toolbar{position:absolute;right:6px;top:6px}.palette-panel > div:not(.toolbar){flex:0 0 38px;border-bottom:1px solid var(--sys-color-divider);padding:12px;line-height:14px;color:var(--sys-color-on-surface)}.palette-panel > div.palette-title{font-size:14px;line-height:16px;color:var(--sys-color-on-surface);flex-basis:40px}div.palette-preview{display:flex;cursor:pointer}.palette-preview-title{flex:0 0 84px}.palette-preview:focus-visible,\n.palette-preview:hover{background-color:var(--sys-color-state-hover-on-subtle)}.spectrum-overlay{z-index:13;visibility:hidden;background-color:hsl(0deg 0% 0%/50%);opacity:0%;transition:opacity 100ms cubic-bezier(0,0,0.2,1),visibility 0s 100ms}.palette-panel-showing > .spectrum-overlay{transition-delay:0s;visibility:visible;opacity:100%}.spectrum-contrast-container{width:100%;height:100%}.spectrum-contrast-line,\n:host-context(.theme-with-dark-background) .spectrum-contrast-line{fill:none;stroke:#fff;opacity:70%;stroke-width:1.5px}.delete-color-toolbar{position:absolute;right:0;top:0;background-color:var(--sys-color-cdt-base-container);visibility:hidden;z-index:3;width:36px;display:flex;align-items:center;padding-left:4px;bottom:2px;border-bottom-right-radius:2px}@keyframes showDeleteToolbar{from{opacity:0%}to{opacity:100%}}.delete-color-toolbar.dragging{visibility:visible;animation:showDeleteToolbar 100ms 150ms cubic-bezier(0,0,0.2,1) backwards}.delete-color-toolbar-active{background-color:var(--sys-color-state-hover-on-subtle);color:var(--sys-color-cdt-base-container)}.swatch.contrast{width:30px;height:30px;position:absolute;top:0;right:0;left:auto;background-image:var(--image-file-checker);border-radius:15px;display:flex;align-items:center;justify-content:center}.swatch.contrast .swatch-overlay{padding:0}.background-color .text-preview{color:var(--sys-color-inverse-surface);font-size:16px;position:relative;padding-bottom:2px}.swatch.contrast devtools-icon{margin:-2px}.no-contrast-info-available{border-top:1px solid var(--sys-color-divider);position:relative;width:100%;padding:10px;justify-content:center;display:flex;flex-wrap:wrap}@media (forced-colors: active){:host{border:1px solid canvastext!important}.spectrum-color{forced-color-adjust:none}.spectrum-switcher:hover,\n  .spectrum-switcher:focus-visible{forced-color-adjust:none;background-color:Highlight!important}:host-context(.theme-with-dark-background) .spectrum-switcher{filter:unset}.spectrum-switcher:hover svg,\n  .spectrum-switcher:focus-visible svg{fill:HighlightText}.swatch{forced-color-adjust:none}.swatch-inner,\n  .swatch-overlay,\n  .swatch-inner-white{border:1px solid ButtonText}.swatch-overlay:hover,\n  .swatch-overlay:focus-visible{background-color:canvas!important}.spectrum-slider{forced-color-adjust:none;background-color:ButtonText!important;box-shadow:0 1px 4px 0 ButtonFace!important}}\n/*# sourceURL=spectrum.css */\n');const L={toggleColorPicker:"Eye dropper [{PH1}]",changeHue:"Change hue",changeAlpha:"Change alpha",hex:"HEX",changeColorFormat:"Change color format",previewPalettes:"Preview palettes",addToPalette:"Add to palette",colorPalettes:"Color Palettes",returnToColorPicker:"Return to color picker",colorS:"Color {PH1}",longclickOrLongpressSpaceToShow:"Long-click or long-press space to show alternate shades of {PH1}",removeColor:"Remove color",removeAllToTheRight:"Remove all to the right",clearPalette:"Clear palette",sInS:"{PH1} in {PH2}",copyColorToClipboard:"Copy color to clipboard",pressArrowKeysMessage:"Press arrow keys with or without modifiers to move swatch position. Arrow key with Shift key moves position largely, with Ctrl key it is less and with Alt key it is even less"},T=s.i18n.registerUIStrings("ui/legacy/components/color_picker/Spectrum.ts",L),D=s.i18n.getLocalizedString.bind(void 0,T),R=new WeakMap,U=new WeakMap,H=["srgb","rgb","hex","hsl","hwb"],V="EyeDropper"in window;function M(t){return!H.includes(t)}function O(e,o){const s=t.Color.Legacy.fromHSVA(o);switch(e){case"display-p3":{const e=t.Color.hsva2rgba(o);return new t.Color.ColorFunction("display-p3",e[0],e[1],e[2],e[3],void 0)}case"srgb":return s}}class z extends(t.ObjectWrapper.eventMixin(a.Widget.VBox)){colorInternal;gamut="srgb";colorElement;colorDragElement;dragX;dragY;colorPickerButton;swatch;hueElement;hueSlider;alphaElement;alphaElementBackground;alphaSlider;displayContainer;textValues;textLabels;hexContainer;hexValue;contrastInfo;srgbOverlay;contrastOverlay;contrastDetails;contrastDetailsBackgroundColorPickedToggledBound;palettes;palettePanel;palettePanelShowing;paletteSectionContainer;paletteContainer;shadesContainer;deleteIconToolbar;deleteButton;addColorToolbar;colorPickedBound;hsv;hueAlphaWidth;dragWidth;dragHeight;colorDragElementHeight;slideHelperWidth;numPaletteRowsShown;selectedColorPalette;customPaletteSetting;colorOffset;closeButton;paletteContainerMutable;shadesCloseHandler;dragElement;dragHotSpotX;dragHotSpotY;colorNameInternal;colorFormat="rgb";eyeDropperAbortController=null;isFormatPickerShown=!1;colorStringInternal;constructor(t){super(!0),this.contentElement.tabIndex=0,this.contentElement.setAttribute("jslog",`${c.dialog("colorPicker").parent("mapped").track({keydown:"Enter|Escape"})}`),this.colorElement=this.contentElement.createChild("div","spectrum-color"),this.colorElement.tabIndex=0,this.colorElement.setAttribute("jslog",`${c.canvas("color").track({click:!0,drag:!0,keydown:"ArrowLeft|ArrowRight|ArrowDown|ArrowUp"})}`),this.setDefaultFocusedElement(this.colorElement),this.colorElement.addEventListener("keydown",this.onSliderKeydown.bind(this,v.bind(this)));const o=D(L.pressArrowKeysMessage);a.ARIAUtils.setLabel(this.colorElement,o),a.ARIAUtils.markAsApplication(this.colorElement),this.colorDragElement=this.colorElement.createChild("div","spectrum-sat fill").createChild("div","spectrum-val fill").createChild("div","spectrum-dragger"),this.dragX=0,this.dragY=0;const s=this.contentElement.createChild("div","spectrum-tools"),r=new a.Toolbar.Toolbar("spectrum-eye-dropper",s),i=a.ShortcutRegistry.ShortcutRegistry.instance().shortcutsForAction("elements.toggle-eye-dropper"),h=i[0]?.descriptors.flatMap((t=>t.name.split(" + ")))[0];this.colorPickerButton=new a.Toolbar.ToolbarToggle(D(L.toggleColorPicker,{PH1:h||""}),"color-picker","color-picker-filled","color-eye-dropper"),this.colorPickerButton.setToggled(!0),this.colorPickerButton.addEventListener("Click",this.toggleColorPicker.bind(this,void 0)),r.appendToolbarItem(this.colorPickerButton),this.colorPickerButton.element.setAttribute("jslog",`${c.colorEyeDropper().track({click:!0})}`),this.swatch=new Y(s),this.hueElement=s.createChild("div","spectrum-hue"),this.hueElement.setAttribute("jslog",`${c.slider("hue").track({click:!0,drag:!0,keydown:"ArrowLeft|ArrowRight|ArrowDown|ArrowUp"})}`),this.hueElement.tabIndex=0,this.hueElement.addEventListener("keydown",this.onSliderKeydown.bind(this,C.bind(this))),a.ARIAUtils.setLabel(this.hueElement,D(L.changeHue)),a.ARIAUtils.markAsSlider(this.hueElement,0,360),this.hueSlider=this.hueElement.createChild("div","spectrum-slider"),this.alphaElement=s.createChild("div","spectrum-alpha"),this.alphaElement.setAttribute("jslog",`${c.slider("alpha").track({click:!0,drag:!0,keydown:"ArrowLeft|ArrowRight|ArrowDown|ArrowUp"})}`),this.alphaElement.tabIndex=0,this.alphaElement.addEventListener("keydown",this.onSliderKeydown.bind(this,f.bind(this))),a.ARIAUtils.setLabel(this.alphaElement,D(L.changeAlpha)),a.ARIAUtils.markAsSlider(this.alphaElement,0,1),this.alphaElementBackground=this.alphaElement.createChild("div","spectrum-alpha-background"),this.alphaSlider=this.alphaElement.createChild("div","spectrum-slider"),this.displayContainer=s.createChild("div","spectrum-text source-code"),a.ARIAUtils.markAsPoliteLiveRegion(this.displayContainer,!0),this.textValues=[];for(let t=0;t<4;++t){const e=a.UIUtils.createInput("spectrum-text-value");e.setAttribute("jslog",`${c.value().track({change:!0,keydown:"ArrowUp|ArrowDown"}).context(t)}`),this.displayContainer.appendChild(e),e.maxLength=4,this.textValues.push(e),e.addEventListener("keydown",this.inputChanged.bind(this),!1),e.addEventListener("input",this.inputChanged.bind(this),!1),e.addEventListener("wheel",this.inputChanged.bind(this),!1),e.addEventListener("paste",this.pasted.bind(this),!1)}this.textLabels=this.displayContainer.createChild("div","spectrum-text-label"),this.hexContainer=s.createChild("div","spectrum-text spectrum-text-hex source-code"),a.ARIAUtils.markAsPoliteLiveRegion(this.hexContainer,!0),this.hexValue=a.UIUtils.createInput("spectrum-text-value"),this.hexValue.setAttribute("jslog",`${c.value("hex").track({keydown:"ArrowUp|ArrowDown",change:!0})}`),this.hexContainer.appendChild(this.hexValue),this.hexValue.maxLength=9,this.hexValue.addEventListener("keydown",this.inputChanged.bind(this),!1),this.hexValue.addEventListener("input",this.inputChanged.bind(this),!1),this.hexValue.addEventListener("wheel",this.inputChanged.bind(this),!1),this.hexValue.addEventListener("paste",this.pasted.bind(this),!1);const d=this.hexContainer.createChild("div","spectrum-text-label");d.textContent=D(L.hex),a.ARIAUtils.setLabel(this.hexValue,d.textContent);const p=s.createChild("button","spectrum-display-switcher spectrum-switcher");p.setAttribute("jslog",`${c.dropDown("color-format").track({click:!0})}`),x(p),a.UIUtils.setTitle(p,D(L.changeColorFormat)),p.tabIndex=0,p.addEventListener("click",(t=>{this.showFormatPicker(t)})),a.UIUtils.installDragHandle(this.hueElement,this.dragStart.bind(this,C.bind(this)),C.bind(this),null,"ew-resize","crosshair"),a.UIUtils.installDragHandle(this.alphaElement,this.dragStart.bind(this,f.bind(this)),f.bind(this),null,"ew-resize","crosshair"),a.UIUtils.installDragHandle(this.colorElement,this.dragStart.bind(this,v.bind(this)),v.bind(this),null,"move","crosshair"),t&&(this.contrastInfo=t,this.contrastOverlay=new A(this.contrastInfo,this.colorElement),this.contrastDetails=new b(this.contrastInfo,this.contentElement,this.toggleColorPicker.bind(this),this.contrastPanelExpandedChanged.bind(this),this.colorSelected.bind(this)),this.contrastDetailsBackgroundColorPickedToggledBound=this.contrastDetailsBackgroundColorPickedToggled.bind(this)),this.element.classList.add("flex-none"),this.palettes=new Map,this.palettePanel=this.contentElement.createChild("div","palette-panel"),this.palettePanel.setAttribute("jslog",`${c.section("palette-panel")}`),this.palettePanelShowing=!1,this.paletteSectionContainer=this.contentElement.createChild("div","spectrum-palette-container"),this.paletteContainer=this.paletteSectionContainer.createChild("div","spectrum-palette"),this.paletteContainer.addEventListener("contextmenu",this.showPaletteColorContextMenu.bind(this,-1)),this.shadesContainer=this.contentElement.createChild("div","palette-color-shades hidden"),this.shadesContainer.setAttribute("jslog",`${c.paletteColorShades()}`),a.UIUtils.installDragHandle(this.paletteContainer,this.paletteDragStart.bind(this),this.paletteDrag.bind(this),this.paletteDragEnd.bind(this),"default");const u=this.paletteSectionContainer.createChild("div","spectrum-palette-switcher spectrum-switcher");u.setAttribute("jslog",`${c.dropDown("palette-switcher").track({click:!0})}`),x(u),a.UIUtils.setTitle(u,D(L.previewPalettes)),a.ARIAUtils.markAsButton(u),u.tabIndex=0,self.onInvokeElement(u,(t=>{this.togglePalettePanel(!0),t.consume(!0)})),this.deleteIconToolbar=new a.Toolbar.Toolbar("delete-color-toolbar"),this.deleteButton=new a.Toolbar.ToolbarButton("","bin"),this.deleteIconToolbar.appendToolbarItem(this.deleteButton);this.contentElement.createChild("div","spectrum-overlay fill").addEventListener("click",this.togglePalettePanel.bind(this,!1)),this.addColorToolbar=new a.Toolbar.Toolbar("add-color-toolbar");const g=new a.Toolbar.ToolbarButton(D(L.addToPalette),"plus",void 0,"add-color");function m(t,e){const o=e,s=t.getBoundingClientRect();switch(o.key){case"ArrowLeft":case"ArrowDown":return s.left-1;case"ArrowRight":case"ArrowUp":return s.right+1;default:return e.x}}function C(t){const o=this.hsv.slice(),s=1-(m(this.hueSlider,t)-this.hueElement.getBoundingClientRect().left)/this.hueAlphaWidth;o[0]=e.NumberUtilities.clamp(s,0,1),this.innerSetColor(o,"",void 0,void 0,N.Other);const r=O(this.gamut,o).as("hsl").canonicalHSLA();a.ARIAUtils.setValueNow(this.hueElement,r[0])}function f(t){const o=this.hsv.slice(),s=(m(this.alphaSlider,t)-this.hueElement.getBoundingClientRect().left)/this.hueAlphaWidth,r=Math.round(100*s)/100;o[3]=e.NumberUtilities.clamp(r,0,1),this.innerSetColor(o,"",void 0,void 0,N.Other);const n=O(this.gamut,o).as("hsl").canonicalHSLA();a.ARIAUtils.setValueText(this.alphaElement,n[3])}function v(t){const o=this.hsv.slice(),s=function(t,e){const o=t.getBoundingClientRect(),s=o.x+o.width/2,r=o.y+o.width/2,n=function(t,e){const o=e;o.altKey?t=1:o.ctrlKey?t=10:o.shiftKey&&(t=20);return t}(o.width/4,e);switch(e.key){case"ArrowLeft":return{x:o.left-n,y:r};case"ArrowRight":return{x:o.right+n,y:r};case"ArrowDown":return{x:s,y:o.bottom+n};case"ArrowUp":return{x:s,y:o.top-n};default:return{x:e.x,y:e.y}}}(this.colorDragElement,t);this.colorOffset=this.colorElement.getBoundingClientRect(),o[1]=e.NumberUtilities.clamp((s.x-this.colorOffset.left)/this.dragWidth,0,1),o[2]=e.NumberUtilities.clamp(1-(s.y-this.colorOffset.top)/this.dragHeight,0,1),this.innerSetColor(o,"",void 0,void 0,N.Other)}function x(t){const e=new n.Icon.Icon;e.data={iconName:"fold-more",color:"var(--icon-default)",width:"16px",height:"16px"},t.appendChild(e)}g.addEventListener("Click",this.onAddColorMousedown.bind(this)),g.element.addEventListener("keydown",this.onAddColorKeydown.bind(this)),this.addColorToolbar.appendToolbarItem(g),this.colorPickedBound=this.colorPicked.bind(this),this.numPaletteRowsShown=-1,this.contentElement.addEventListener("focusout",(t=>{this.isFormatPickerShown&&t.stopImmediatePropagation()})),this.srgbOverlay=new l.SrgbOverlay.SrgbOverlay,this.loadPalettes(),new _((t=>{t.colors.length?this.addPalette(t):this.selectedColorPalette.get()===t.title&&this.paletteSelected(K)}))}dragStart(t,e){return this.colorOffset=this.colorElement.getBoundingClientRect(),t(e),!0}contrastDetailsBackgroundColorPickedToggled(t){t.data&&this.toggleColorPicker(!1)}contrastPanelExpandedChanged(){this.contrastOverlay&&this.contrastDetails&&(this.contrastOverlay.setVisible(this.contrastDetails.expanded()),this.resizeForSelectedPalette(!0),this.contrastDetails.expanded()?this.hideSrgbOverlay():this.showSrgbOverlay())}updatePalettePanel(){this.palettePanel.removeChildren();this.palettePanel.createChild("div","palette-title").textContent=D(L.colorPalettes);const t=new a.Toolbar.Toolbar("",this.palettePanel);this.closeButton=new a.Toolbar.ToolbarButton(D(L.returnToColorPicker),"cross"),this.closeButton.addEventListener("Click",this.togglePalettePanel.bind(this,!1)),this.closeButton.element.addEventListener("keydown",this.onCloseBtnKeydown.bind(this)),this.closeButton.element.setAttribute("jslog",`${c.close().track({click:!0})}`),t.appendToolbarItem(this.closeButton);for(const t of this.palettes.values())this.palettePanel.appendChild(this.createPreviewPaletteElement(t));this.contentElement.scrollIntoView({block:"end"})}togglePalettePanel(t){this.palettePanelShowing!==t&&(t&&this.updatePalettePanel(),this.palettePanelShowing=t,this.contentElement.classList.toggle("palette-panel-showing",t),this.focusInternal())}onCloseBtnKeydown(t){(e.KeyboardUtilities.isEscKey(t)||e.KeyboardUtilities.isEnterOrSpaceKey(t))&&(this.togglePalettePanel(!1),t.consume(!0))}onSliderKeydown(t,e){switch(e.key){case"ArrowLeft":case"ArrowRight":case"ArrowDown":case"ArrowUp":t(e),e.consume(!0)}}focusInternal(){this.isShowing()&&(this.palettePanelShowing&&this.closeButton?this.closeButton.element.focus({preventScroll:!0}):this.contentElement.focus())}createPaletteColor(t,o,s){const r=document.createElement("div");return r.classList.add("spectrum-palette-color"),r.setAttribute("jslog",`${c.item().track({click:!0,drag:!0,keydown:"ArrowUp|ArrowDown|ArrowLeft|ArrowRight|Escape|Tab"})}`),r.style.background=e.StringUtilities.sprintf("linear-gradient(%s, %s), var(--image-file-checker)",t,t),s&&r.animate([{opacity:0},{opacity:1}],{duration:100,delay:s,fill:"backwards"}),a.Tooltip.Tooltip.install(r,o||t),r}showPalette(t,e,o){this.resizeForSelectedPalette(),this.paletteContainer.removeChildren();for(let o=0;o<t.colors.length;o++){const s=e?100*o/t.colors.length:0,r=this.createPaletteColor(t.colors[o],t.colorNames[o],s);if(a.ARIAUtils.markAsButton(r),a.ARIAUtils.setLabel(r,D(L.colorS,{PH1:t.colors[o]})),r.tabIndex=-1,r.addEventListener("mousedown",this.paletteColorSelected.bind(this,t.colors[o],t.colorNames[o],Boolean(t.matchUserFormat))),r.addEventListener("focus",this.paletteColorSelected.bind(this,t.colors[o],t.colorNames[o],Boolean(t.matchUserFormat))),r.addEventListener("keydown",this.onPaletteColorKeydown.bind(this,o)),t.mutable)R.set(r,!0),U.set(r,t.colors[o]),r.addEventListener("contextmenu",this.showPaletteColorContextMenu.bind(this,o));else if(t===K){r.classList.add("has-material-shades");let e=r.createChild("div","spectrum-palette-color spectrum-palette-color-shadow");e.style.background=t.colors[o],e=r.createChild("div","spectrum-palette-color spectrum-palette-color-shadow"),e.style.background=t.colors[o];const s=D(L.longclickOrLongpressSpaceToShow,{PH1:t.colors[o]});a.Tooltip.Tooltip.install(r,s),a.ARIAUtils.setLabel(r,s),new a.UIUtils.LongClickController(r,this.showLightnessShades.bind(this,r,t.colors[o]))}this.paletteContainer.appendChild(r)}this.paletteContainer.childNodes.length>0&&(this.paletteContainer.childNodes[0].tabIndex=0),this.paletteContainerMutable=t.mutable,t.mutable?(this.paletteContainer.appendChild(this.addColorToolbar.element),this.paletteContainer.appendChild(this.deleteIconToolbar.element)):(this.addColorToolbar.element.remove(),this.deleteIconToolbar.element.remove()),this.togglePalettePanel(!1),this.focusInternal()}showLightnessShades(t,e,o){this.shadesCloseHandler&&this.shadesCloseHandler(),this.shadesContainer.classList.remove("hidden"),this.shadesContainer.removeChildren(),this.shadesContainer.animate([{transform:"scaleY(0)",opacity:"0"},{transform:"scaleY(1)",opacity:"1"}],{duration:200,easing:"cubic-bezier(0.4, 0, 0.2, 1)"});let s=this.paletteContainer.offsetTop+t.offsetTop+(t.parentElement?t.parentElement.offsetTop:0);this.contrastDetails&&(s+=this.contrastDetails.element().offsetHeight),this.shadesContainer.style.top=s+"px",this.shadesContainer.style.left=t.offsetLeft+"px",t.classList.add("spectrum-shades-shown");const r=G.get(e);if(void 0!==r)for(let e=r.length-1;e>=0;e--){const o=this.createPaletteColor(r[e],void 0,200*e/r.length+100);a.ARIAUtils.markAsButton(o),a.ARIAUtils.setLabel(o,D(L.colorS,{PH1:r[e]})),o.tabIndex=-1,o.addEventListener("mousedown",this.paletteColorSelected.bind(this,r[e],r[e],!1)),o.addEventListener("focus",this.paletteColorSelected.bind(this,r[e],r[e],!1)),o.addEventListener("keydown",this.onShadeColorKeydown.bind(this,t)),this.shadesContainer.appendChild(o)}this.shadesContainer.childNodes.length>0&&this.shadesContainer.childNodes[this.shadesContainer.childNodes.length-1].focus(),this.shadesCloseHandler=function(t){this.shadesContainer.classList.add("hidden"),t.classList.remove("spectrum-shades-shown"),this.shadesCloseHandler&&this.shadesContainer.ownerDocument.removeEventListener("mousedown",this.shadesCloseHandler,!0),delete this.shadesCloseHandler}.bind(this,t),this.shadesContainer.ownerDocument.addEventListener("mousedown",this.shadesCloseHandler,!0)}slotIndexForEvent(t){const e=t,o=e.pageX-this.paletteContainer.getBoundingClientRect().left,s=e.pageY-this.paletteContainer.getBoundingClientRect().top,r=Math.min(o/j|0,W-1),n=s/j|0;return Math.min(n*W+r,this.customPaletteSetting.get().colors.length-1)}isDraggingToBin(t){return t.pageX>this.deleteIconToolbar.element.getBoundingClientRect().left}paletteDragStart(t){const e=a.UIUtils.deepElementFromEvent(t);if(!e||!R.get(e))return!1;const o=this.slotIndexForEvent(t);this.dragElement=e;const s=t;return this.dragHotSpotX=s.pageX-o%W*j,this.dragHotSpotY=s.pageY-(o/W|0)*j,!0}paletteDrag(t){const e=t;if(e.pageX<this.paletteContainer.getBoundingClientRect().left||e.pageY<this.paletteContainer.getBoundingClientRect().top)return;if(!this.dragElement||void 0===this.dragHotSpotX||void 0===this.dragHotSpotY)return;const o=this.slotIndexForEvent(t),s=e.pageX-o%W*j,r=e.pageY-(o/W|0)*j,n=this.isDraggingToBin(t);this.deleteIconToolbar.element.classList.add("dragging"),this.deleteIconToolbar.element.classList.toggle("delete-color-toolbar-active",n);const a="translateX("+(s-this.dragHotSpotX)+"px) translateY("+(r-this.dragHotSpotY)+"px)";this.dragElement.style.transform=n?a+" scale(0.8)":a;const i=[...this.paletteContainer.children],l=i.indexOf(this.dragElement),c=new Map;for(const t of i)c.set(t,t.getBoundingClientRect());l!==o&&this.paletteContainer.insertBefore(this.dragElement,i[o>l?o+1:o]);for(const t of i){if(t===this.dragElement)continue;const e=c.get(t),o=t.getBoundingClientRect();!e||e.left===o.left&&e.top===o.top||t.animate([{transform:"translateX("+(e.left-o.left)+"px) translateY("+(e.top-o.top)+"px)"},{transform:"none"}],{duration:100,easing:"cubic-bezier(0, 0, 0.2, 1)"})}}paletteDragEnd(t){if(!this.dragElement)return;this.isDraggingToBin(t)&&this.dragElement.remove(),this.dragElement.style.removeProperty("transform");const e=this.paletteContainer.children,o=[];for(let t=0;t<e.length;++t){const s=U.get(e[t]);s&&o.push(s)}const s=this.customPaletteSetting.get();s.colors=o,this.customPaletteSetting.set(s),this.showPalette(s,!1),this.deleteIconToolbar.element.classList.remove("dragging"),this.deleteIconToolbar.element.classList.remove("delete-color-toolbar-active")}loadPalettes(){this.palettes.set(K.title,K);const e={title:"Custom",colors:[],colorNames:[],mutable:!0,matchUserFormat:void 0};this.customPaletteSetting=t.Settings.Settings.instance().createSetting("custom-color-palette",e);const o=this.customPaletteSetting.get();o.colorNames=o.colorNames||[],this.palettes.set(o.title,o),this.selectedColorPalette=t.Settings.Settings.instance().createSetting("selected-color-palette",$);const s=this.palettes.get(this.selectedColorPalette.get());s&&this.showPalette(s,!0)}addPalette(t){this.palettes.set(t.title,t),this.selectedColorPalette.get()===t.title&&this.showPalette(t,!0)}createPreviewPaletteElement(t){const e=document.createElement("div");e.classList.add("palette-preview"),a.ARIAUtils.markAsButton(e),e.tabIndex=0;let o;for(e.createChild("div","palette-preview-title").textContent=t.title,o=0;o<5&&o<t.colors.length;o++)e.appendChild(this.createPaletteColor(t.colors[o],t.colorNames[o]));for(;o<5;o++)e.createChild("div","spectrum-palette-color empty-color");return self.onInvokeElement(e,(e=>{this.paletteSelected(t),e.consume(!0)})),e}paletteSelected(t){this.selectedColorPalette.set(t.title),this.showPalette(t,!0)}resizeForSelectedPalette(t){const e=this.palettes.get(this.selectedColorPalette.get());if(!e)return;let o=e.colors.length;e===this.customPaletteSetting.get()&&o++;const s=Math.max(1,Math.ceil(o/W));if(this.numPaletteRowsShown===s&&!t)return;this.numPaletteRowsShown=s;let r=236;this.contrastDetails&&(this.contrastDetails.expanded()?r+=78:r+=36),this.element.style.height=r+12+24*s+"px",this.dispatchEventToListeners("SizeChanged")}paletteColorSelected(e,o,s){const r=t.Color.parse(e);r&&this.innerSetColor(r,e,o,s?this.colorFormat:r.format(),N.Other)}onPaletteColorKeydown(t,e){let o;switch(e.key){case"ArrowLeft":o=t-1;break;case"ArrowRight":o=t+1;break;case"ArrowUp":o=t-W;break;case"ArrowDown":o=t+W}void 0!==o&&o>-1&&o<this.paletteContainer.childNodes.length&&this.paletteContainer.childNodes[o].focus()}onShadeColorKeydown(t,o){const s=o.target;e.KeyboardUtilities.isEscKey(o)||"Tab"===o.key?(t.focus(),this.shadesCloseHandler&&this.shadesCloseHandler(),o.consume(!0)):"ArrowUp"===o.key&&s.previousElementSibling?(s.previousElementSibling.focus(),o.consume(!0)):"ArrowDown"===o.key&&s.nextElementSibling&&(s.nextElementSibling.focus(),o.consume(!0))}onAddColorMousedown(){this.addColorToCustomPalette()}onAddColorKeydown(t){e.KeyboardUtilities.isEnterOrSpaceKey(t)&&(this.addColorToCustomPalette(),t.consume(!0))}addColorToCustomPalette(){const t=this.customPaletteSetting.get();t.colors.push(this.colorString()),this.customPaletteSetting.set(t),this.showPalette(t,!1);const e=this.paletteContainer.querySelectorAll(".spectrum-palette-color");e[e.length-1].focus()}showPaletteColorContextMenu(t,e){if(!this.paletteContainerMutable)return;const o=new a.ContextMenu.ContextMenu(e);-1!==t&&(o.defaultSection().appendItem(D(L.removeColor),this.deletePaletteColors.bind(this,t,!1),{jslogContext:"remove-color"}),o.defaultSection().appendItem(D(L.removeAllToTheRight),this.deletePaletteColors.bind(this,t,!0),{jslogContext:"remove-all-to-the-right"})),o.defaultSection().appendItem(D(L.clearPalette),this.deletePaletteColors.bind(this,-1,!0),{jslogContext:"clear-palette"}),o.show()}deletePaletteColors(t,e){const o=this.customPaletteSetting.get();e?o.colors.splice(t+1,o.colors.length-t-1):o.colors.splice(t,1),this.customPaletteSetting.set(o),this.showPalette(o,!1)}setColor(t){this.innerSetColor(t,"",void 0,t.format(),N.Model);const e=t.as("hsl").canonicalHSLA();a.ARIAUtils.setValueNow(this.hueElement,e[0]),a.ARIAUtils.setValueText(this.alphaElement,e[3])}colorSelected(t){this.innerSetColor(t,"",void 0,void 0,N.Other)}get color(){return this.colorInternal?this.colorInternal:O(this.gamut,this.hsv)}innerSetColor(e,o,s,r,n){if(void 0!==o&&(this.colorStringInternal=o),void 0!==r&&(this.colorFormat=function(t){return"rgba"===t?"rgb":"hsla"===t?"hsl":"hwba"===t?"hwb":"hexa"===t?"hex":t}(r),this.gamut=M(this.colorFormat)?"display-p3":"srgb"),Array.isArray(e))this.colorInternal=void 0,this.hsv=e;else if(void 0!==e){this.colorInternal=e;const o=this.hsv?this.hsv[0]:null;this.hsv=function(e,o){switch(e){case"display-p3":{const e=o.as("display-p3");return[...t.Color.rgb2hsv([e.p0,e.p1,e.p2]),e.alpha||1]}case"srgb":return o.as("hsl").hsva()}}(this.gamut,e),null!==o&&e.as("lch").isHuePowerless()&&(this.hsv[0]=o)}this.colorNameInternal=s,this.contrastInfo&&this.contrastInfo.setColor(t.Color.Legacy.fromHSVA(this.hsv),this.colorFormat),this.updateHelperLocations(),this.updateUI(),n!==N.Input&&this.updateInput(),n!==N.Model&&this.dispatchEventToListeners("ColorChanged",this.colorString())}colorName(){return this.colorNameInternal}colorString(){if(this.colorStringInternal)return this.colorStringInternal;const t=this.color;let e=this.colorFormat&&this.colorFormat!==t.format()?t.asString(this.colorFormat):t.getAuthoredText()??t.asString();return e||(e="hex"===this.colorFormat?t.asString("hexa"):"hsl"===this.colorFormat?t.asString("hsla"):"hwb"===this.colorFormat?t.asString("hwba"):t.asString("rgba"),console.assert(Boolean(e)),e||"")}updateHelperLocations(){const t=this.hsv[0],e=this.hsv[1],o=this.hsv[2],s=this.hsv[3];this.dragX=e*this.dragWidth,this.dragY=this.dragHeight-o*this.dragHeight;const r=Math.max(-this.colorDragElementHeight,Math.min(this.dragWidth-this.colorDragElementHeight,this.dragX-this.colorDragElementHeight)),n=Math.max(-this.colorDragElementHeight,Math.min(this.dragHeight-this.colorDragElementHeight,this.dragY-this.colorDragElementHeight));this.colorDragElement.positionAt(r,n);const a=(1-t)*this.hueAlphaWidth-this.slideHelperWidth;this.hueSlider.style.left=a+"px";const i=s*this.hueAlphaWidth-this.slideHelperWidth;this.alphaSlider.style.left=i+"px"}updateInput(){if("hex"===this.colorFormat)this.hexContainer.hidden=!1,this.displayContainer.hidden=!0,this.hexValue.value=this.color.asString(1!==(this.color.alpha??1)?"hexa":"hex");else{this.hexContainer.hidden=!0,this.displayContainer.hidden=!1;const t=p[this.colorFormat],e=t.toValues(this.color);this.textLabels.textContent=t.label;for(let t=0;t<this.textValues.length;++t)a.ARIAUtils.setLabel(this.textValues[t],D(L.sInS,{PH1:this.textLabels.textContent.charAt(t),PH2:this.textLabels.textContent})),this.textValues[t].value=String(e[t])}}hideSrgbOverlay(){this.colorElement.contains(this.srgbOverlay)&&this.colorElement.removeChild(this.srgbOverlay)}showSrgbOverlay(){this.contrastDetails&&this.contrastDetails.expanded()||"display-p3"!==this.gamut||(this.srgbOverlay.render({hue:this.hsv[0],width:this.dragWidth,height:this.dragHeight}),this.colorElement.contains(this.srgbOverlay)||this.colorElement.appendChild(this.srgbOverlay))}updateSrgbOverlay(){"display-p3"===this.gamut?this.showSrgbOverlay():this.hideSrgbOverlay()}updateUI(){this.colorElement.style.backgroundColor=O(this.gamut,[this.hsv[0],1,1,1]).asString(),this.contrastOverlay&&this.contrastOverlay.setDimensions(this.dragWidth,this.dragHeight),this.updateSrgbOverlay(),this.swatch.setColor(this.color,this.colorString()),this.colorDragElement.style.backgroundColor=this.color.asString("lch");const o=t.Color.Legacy.fromHSVA(this.hsv.slice(0,3).concat(1));this.alphaElementBackground.style.backgroundImage=e.StringUtilities.sprintf("linear-gradient(to right, rgba(0,0,0,0), %s)",o.asString("lch")),this.hueElement.classList.toggle("display-p3",M(this.colorFormat))}async showFormatPicker(t){const e=new F(this.color);this.isFormatPickerShown=!0,await e.show(t,(t=>{this.innerSetColor(t,void 0,void 0,t.format(),N.Other),o.userMetrics.colorConvertedFrom(1)})),this.isFormatPickerShown=!1}pasted(e){if(!e.clipboardData)return;const o=e.clipboardData.getData("text"),s=t.Color.parse(o);s&&(this.innerSetColor(s,o,void 0,void 0,N.Other),e.preventDefault())}inputChanged(e){const o=e.currentTarget,s=a.UIUtils.createReplacementString(o.value,e);s&&(o.value=s,o.selectionStart=0,o.selectionEnd=s.length,e.consume(!0));let r=null;if("hex"===this.colorFormat)r=t.Color.parse(this.hexValue.value);else{const t=p[this.colorFormat],e=this.textValues.map((t=>t.value));if(4!==e.length)return;r=t.fromValues(e)}r&&this.innerSetColor(r,void 0,void 0,undefined,N.Input)}wasShown(){this.registerCSSFiles([S]),this.hueAlphaWidth=this.hueElement.offsetWidth,this.slideHelperWidth=this.hueSlider.offsetWidth/2,this.dragWidth=this.colorElement.offsetWidth,this.dragHeight=this.colorElement.offsetHeight,this.colorDragElementHeight=this.colorDragElement.offsetHeight/2,this.innerSetColor(void 0,void 0,void 0,void 0,N.Model),V?this.colorPickerButton.setToggled(!1):this.toggleColorPicker(!0),this.contrastDetails&&this.contrastDetailsBackgroundColorPickedToggledBound&&this.contrastDetails.addEventListener("BackgroundColorPickerWillBeToggled",this.contrastDetailsBackgroundColorPickedToggledBound)}willHide(){this.toggleColorPicker(!1),this.contrastDetails&&this.contrastDetailsBackgroundColorPickedToggledBound&&this.contrastDetails.removeEventListener("BackgroundColorPickerWillBeToggled",this.contrastDetailsBackgroundColorPickedToggledBound)}async toggleColorPicker(e){if(void 0===e&&(e=!this.colorPickerButton.toggled()),this.colorPickerButton.setToggled(e),this.contrastDetails&&e&&this.contrastDetails.backgroundColorPickerEnabled()&&this.contrastDetails.toggleBackgroundColorPicker(!1),V)if(V&&e){const e=new window.EyeDropper;this.eyeDropperAbortController=new AbortController;try{const o=await e.open({signal:this.eyeDropperAbortController.signal}),s=t.Color.parse(o.sRGBHex);this.innerSetColor(s??void 0,"",void 0,void 0,N.Other)}catch(t){"AbortError"!==t.name&&console.error(t)}this.colorPickerButton.setToggled(!1)}else V&&!e&&(this.eyeDropperAbortController?.abort(),this.eyeDropperAbortController=null);else o.InspectorFrontendHost.InspectorFrontendHostInstance.setEyeDropperActive(e),e?o.InspectorFrontendHost.InspectorFrontendHostInstance.events.addEventListener(o.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.colorPickedBound):o.InspectorFrontendHost.InspectorFrontendHostInstance.events.removeEventListener(o.InspectorFrontendHostAPI.Events.EyeDropperPickedColor,this.colorPickedBound)}colorPicked({data:e}){const s=[e.r,e.g,e.b,(e.a/2.55|0)/100],r=t.Color.Legacy.fromRGBA(s);this.innerSetColor(r,"",void 0,void 0,N.Other),o.InspectorFrontendHost.InspectorFrontendHostInstance.bringToFront()}}const N={Input:"Input",Model:"Model",Other:"Other"},j=24,W=8,$="Page colors";class _{callback;frequencyMap;constructor(t){this.callback=t,this.frequencyMap=new Map;const e=[];for(const t of i.TargetManager.TargetManager.instance().models(i.CSSModel.CSSModel))for(const o of t.allStyleSheets())e.push(this.processStylesheet(o));Promise.all(e).catch((t=>{console.error(t)})).then(this.finish.bind(this))}frequencyComparator(t,e){return this.frequencyMap.get(e)-this.frequencyMap.get(t)}finish(){let e=[...this.frequencyMap.keys()];e=e.sort(this.frequencyComparator.bind(this));const o=new Map;for(;o.size<24&&e.length;){const s=e.shift(),r=t.Color.parse(s);r&&o.set(s,r)}this.callback({title:$,colors:[...o.keys()].sort((function(t,e){const s=o.get(t).as("hsl").hsva(),r=o.get(e).as("hsl").hsva();return r[1]<.12&&s[1]<.12?r[2]*r[3]-s[2]*s[3]:r[1]<.12?-1:s[1]<.12?1:r[0]===s[0]?r[1]*r[3]-s[1]*s[3]:(r[0]+.94)%1-(s[0]+.94)%1})),colorNames:[],mutable:!1,matchUserFormat:void 0})}async processStylesheet(t){let e=(await t.requestContent()).content||"";e=e.toLowerCase();const o=e.matchAll(/((?:rgb|hsl|hwb)a?\([^)]+\)|#[0-9a-f]{6}|#[0-9a-f]{3})/g);for(const{0:t,index:s}of o){if(e.indexOf(";",s)<0||e.indexOf(":",s)>-1&&e.indexOf(":",s)<e.indexOf(";",s))continue;const o=1+(this.frequencyMap.get(t)??0);this.frequencyMap.set(t,o)}}}const G=new Map([["#F44336",["#FFEBEE","#FFCDD2","#EF9A9A","#E57373","#EF5350","#F44336","#E53935","#D32F2F","#C62828","#B71C1C"]],["#E91E63",["#FCE4EC","#F8BBD0","#F48FB1","#F06292","#EC407A","#E91E63","#D81B60","#C2185B","#AD1457","#880E4F"]],["#9C27B0",["#F3E5F5","#E1BEE7","#CE93D8","#BA68C8","#AB47BC","#9C27B0","#8E24AA","#7B1FA2","#6A1B9A","#4A148C"]],["#673AB7",["#EDE7F6","#D1C4E9","#B39DDB","#9575CD","#7E57C2","#673AB7","#5E35B1","#512DA8","#4527A0","#311B92"]],["#3F51B5",["#E8EAF6","#C5CAE9","#9FA8DA","#7986CB","#5C6BC0","#3F51B5","#3949AB","#303F9F","#283593","#1A237E"]],["#2196F3",["#E3F2FD","#BBDEFB","#90CAF9","#64B5F6","#42A5F5","#2196F3","#1E88E5","#1976D2","#1565C0","#0D47A1"]],["#03A9F4",["#E1F5FE","#B3E5FC","#81D4FA","#4FC3F7","#29B6F6","#03A9F4","#039BE5","#0288D1","#0277BD","#01579B"]],["#00BCD4",["#E0F7FA","#B2EBF2","#80DEEA","#4DD0E1","#26C6DA","#00BCD4","#00ACC1","#0097A7","#00838F","#006064"]],["#009688",["#E0F2F1","#B2DFDB","#80CBC4","#4DB6AC","#26A69A","#009688","#00897B","#00796B","#00695C","#004D40"]],["#4CAF50",["#E8F5E9","#C8E6C9","#A5D6A7","#81C784","#66BB6A","#4CAF50","#43A047","#388E3C","#2E7D32","#1B5E20"]],["#8BC34A",["#F1F8E9","#DCEDC8","#C5E1A5","#AED581","#9CCC65","#8BC34A","#7CB342","#689F38","#558B2F","#33691E"]],["#CDDC39",["#F9FBE7","#F0F4C3","#E6EE9C","#DCE775","#D4E157","#CDDC39","#C0CA33","#AFB42B","#9E9D24","#827717"]],["#FFEB3B",["#FFFDE7","#FFF9C4","#FFF59D","#FFF176","#FFEE58","#FFEB3B","#FDD835","#FBC02D","#F9A825","#F57F17"]],["#FFC107",["#FFF8E1","#FFECB3","#FFE082","#FFD54F","#FFCA28","#FFC107","#FFB300","#FFA000","#FF8F00","#FF6F00"]],["#FF9800",["#FFF3E0","#FFE0B2","#FFCC80","#FFB74D","#FFA726","#FF9800","#FB8C00","#F57C00","#EF6C00","#E65100"]],["#FF5722",["#FBE9E7","#FFCCBC","#FFAB91","#FF8A65","#FF7043","#FF5722","#F4511E","#E64A19","#D84315","#BF360C"]],["#795548",["#EFEBE9","#D7CCC8","#BCAAA4","#A1887F","#8D6E63","#795548","#6D4C41","#5D4037","#4E342E","#3E2723"]],["#9E9E9E",["#FAFAFA","#F5F5F5","#EEEEEE","#E0E0E0","#BDBDBD","#9E9E9E","#757575","#616161","#424242","#212121"]],["#607D8B",["#ECEFF1","#CFD8DC","#B0BEC5","#90A4AE","#78909C","#607D8B","#546E7A","#455A64","#37474F","#263238"]]]),K={title:"Material",mutable:!1,matchUserFormat:!0,colors:[...G.keys()],colorNames:[]};class Y{colorString;swatchInnerElement;swatchOverlayElement;swatchCopyIcon;constructor(t){const e=t.createChild("span","swatch");e.setAttribute("jslog",`${c.action("copy-color").track({click:!0})}`),this.swatchInnerElement=e.createChild("span","swatch-inner"),this.swatchOverlayElement=e.createChild("span","swatch-overlay"),a.ARIAUtils.markAsButton(this.swatchOverlayElement),a.ARIAUtils.setPressed(this.swatchOverlayElement,!1),this.swatchOverlayElement.tabIndex=0,self.onInvokeElement(this.swatchOverlayElement,this.onCopyText.bind(this)),this.swatchOverlayElement.addEventListener("mouseout",this.onCopyIconMouseout.bind(this)),this.swatchOverlayElement.addEventListener("blur",this.onCopyIconMouseout.bind(this)),this.swatchCopyIcon=n.Icon.create("copy","copy-color-icon"),a.Tooltip.Tooltip.install(this.swatchCopyIcon,D(L.copyColorToClipboard)),this.swatchOverlayElement.appendChild(this.swatchCopyIcon),a.ARIAUtils.setLabel(this.swatchOverlayElement,this.swatchCopyIcon.title)}setColor(t,e){const o=t.as("lch");this.swatchInnerElement.style.backgroundColor=o.asString(),this.swatchInnerElement.classList.toggle("swatch-inner-white",o.l>90),this.colorString=e||null,this.swatchOverlayElement.hidden=!e}onCopyText(t){this.swatchCopyIcon.name="checkmark",o.InspectorFrontendHost.InspectorFrontendHostInstance.copyText(this.colorString),a.ARIAUtils.setPressed(this.swatchOverlayElement,!0),t.consume()}onCopyIconMouseout(){this.swatchCopyIcon.name="copy",a.ARIAUtils.setPressed(this.swatchOverlayElement,!1)}}var X=Object.freeze({__proto__:null,Spectrum:z,ChangeSource:N,PaletteGenerator:_,MaterialPalette:K,Swatch:Y});export{u as ColorFormatSpec,v as ContrastDetails,w as ContrastInfo,k as ContrastOverlay,B as FormatPickerContextMenu,X as Spectrum};
