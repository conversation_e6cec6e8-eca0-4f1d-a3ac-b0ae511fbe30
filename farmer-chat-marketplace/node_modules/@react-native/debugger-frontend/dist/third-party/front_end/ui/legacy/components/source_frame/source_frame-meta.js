import*as t from"../../../../core/common/common.js";import*as e from"../../../../core/i18n/i18n.js";const a={defaultIndentation:"Default indentation:",setIndentationToSpaces:"Set indentation to 2 spaces",Spaces:"2 spaces",setIndentationToFSpaces:"Set indentation to 4 spaces",fSpaces:"4 spaces",setIndentationToESpaces:"Set indentation to 8 spaces",eSpaces:"8 spaces",setIndentationToTabCharacter:"Set indentation to tab character",tabCharacter:"Tab character"},n=e.i18n.registerUIStrings("ui/legacy/components/source_frame/source_frame-meta.ts",a),o=e.i18n.getLazilyComputedLocalizedString.bind(void 0,n);t.Settings.registerSettingExtension({category:"SOURCES",storageType:"Synced",title:o(a.defaultIndentation),settingName:"text-editor-indent",settingType:"enum",defaultValue:"    ",options:[{title:o(a.setIndentationToSpaces),text:o(a.Spaces),value:"  "},{title:o(a.setIndentationToFSpaces),text:o(a.fSpaces),value:"    "},{title:o(a.setIndentationToESpaces),text:o(a.eSpaces),value:"        "},{title:o(a.setIndentationToTabCharacter),text:o(a.tabCharacter),value:"\t"}]});
