import*as e from"../../../core/common/common.js";import*as t from"../../../core/i18n/i18n.js";import*as i from"../../../panels/network/forward/forward.js";import*as r from"../icon_button/icon_button.js";import*as s from"../render_coordinator/render_coordinator.js";import*as o from"../../lit-html/lit-html.js";import*as n from"../../visual_logging/visual_logging.js";const a=new CSSStyleSheet;a.replaceSync(":host{display:inline-block;white-space:nowrap;color:inherit;font-size:inherit;font-family:inherit}:host([hidden]){display:none}button{border:none;background:transparent;margin:0;padding:0;&.link{cursor:pointer;& > span{color:var(--sys-color-primary)}}}devtools-icon{width:16px;height:16px;vertical-align:middle;color:var(--icon-no-request);.link > &{color:var(--icon-link)}}@media (forced-colors: active){devtools-icon{color:ButtonText}}\n/*# sourceURL=requestLinkIcon.css */\n");const l={clickToShowRequestInTheNetwork:"Click to open the network panel and show request for URL: {url}",requestUnavailableInTheNetwork:"Request unavailable in the network panel, try reloading the inspected page",shortenedURL:"Shortened URL"},h=t.i18n.registerUIStrings("ui/components/request_link_icon/RequestLinkIcon.ts",l),c=t.i18n.getLocalizedString.bind(void 0,h),d=s.RenderCoordinator.RenderCoordinator.instance(),u=e=>(/[^/]+$/.exec(e)||/[^/]+\/$/.exec(e)||[""])[0];class p extends HTMLElement{static litTagName=o.literal`devtools-request-link-icon`;#e=this.attachShadow({mode:"open"});#t;#i;#r;#s;#o=!1;#n;#a;#l;#h;#c=e.Revealer.reveal;set data(e){if(this.#t=e.linkToPreflight,this.#i=e.request,e.affectedRequest&&(this.#l={...e.affectedRequest}),this.#r=e.highlightHeader,this.#a=e.networkTab,this.#s=e.requestResolver,this.#o=e.displayURL??!1,this.#n=e.urlToDisplay,this.#h=e.additionalOnClickAction,e.revealOverride&&(this.#c=e.revealOverride),!this.#i&&e.affectedRequest){if(!this.#s)throw new Error("A `RequestResolver` must be provided if an `affectedRequest` is provided.");this.#s.waitFor(e.affectedRequest.requestId).then((e=>(this.#i=e,this.#d()))).catch((()=>{this.#i=null}))}this.#d()}connectedCallback(){this.#e.adoptedStyleSheets=[a]}get data(){return{linkToPreflight:this.#t,request:this.#i,affectedRequest:this.#l,highlightHeader:this.#r,networkTab:this.#a,requestResolver:this.#s,displayURL:this.#o,urlToDisplay:this.#n,additionalOnClickAction:this.#h,revealOverride:this.#c!==e.Revealer.reveal?this.#c:void 0}}handleClick(e){if(0!==e.button)return;const t=this.#t?this.#i?.preflightRequest():this.#i;if(t){if(this.#r){const e=i.UIRequestLocation.UIRequestLocation.header(t,this.#r.section,this.#r.name);this.#c(e)}else{const e=i.UIRequestLocation.UIRequestLocation.tab(t,this.#a??"headers-component");this.#c(e)}this.#h?.(),e.consume()}}#u(){return this.#i?c(l.clickToShowRequestInTheNetwork,{url:this.#i.url()}):c(l.requestUnavailableInTheNetwork)}#p(){return this.#i?this.#i.url():this.#l?.url}#q(){if(!this.#o)return o.nothing;const e=this.#p();if(!e)return o.nothing;if(this.#n)return o.html`<span title=${e}>${this.#n}</span>`;const t=u(e);return o.html`<span aria-label=${c(l.shortenedURL)} title=${e}>${t}</span>`}async#d(){return d.write((()=>{o.render(o.html`
      <button class=${o.Directives.classMap({link:Boolean(this.#i)})}
              title=${this.#u()}
              jslog=${n.link("request").track({click:!0})}
              @click=${this.handleClick}>
        <${r.Icon.Icon.litTagName} name="arrow-up-down-circle"></${r.Icon.Icon.litTagName}>
        ${this.#q()}
      </button>`,this.#e,{host:this})}))}}customElements.define("devtools-request-link-icon",p);var q=Object.freeze({__proto__:null,extractShortPath:u,RequestLinkIcon:p});export{q as RequestLinkIcon};
