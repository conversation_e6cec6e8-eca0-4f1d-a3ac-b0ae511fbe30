import*as e from"../../../core/i18n/i18n.js";import*as i from"../../../third_party/diff/diff.js";import*as t from"../../visual_logging/visual_logging.js";import*as n from"../../lit-html/lit-html.js";import*as r from"../code_highlighter/code_highlighter.js";const o=new CSSStyleSheet;o.replaceSync(".diff-listing{display:grid;grid-template-columns:max-content max-content max-content auto;font-family:var(--source-code-font-family);font-size:var(--source-code-font-size);white-space:pre;line-height:1.2em;user-select:text}.diff-line-number{color:var(--sys-color-token-subtle);padding:0 3px 0 9px;text-align:right;user-select:none}.diff-line-marker{border-right:1px solid var(--sys-color-divider);width:20px;text-align:center}.diff-line-content{padding:0 4px}.diff-line-marker-addition,\n.diff-line-addition{background-color:var(--sys-color-surface-green)}.diff-line-marker-deletion,\n.diff-line-deletion{background-color:var(--sys-color-surface-error)}.diff-line-addition .inner-diff{background-color:color-mix(in sRGB,var(--ref-palette-green70) 40%,transparent)}.diff-line-deletion .inner-diff{background-color:color-mix(in sRGB,var(--ref-palette-error60) 40%,transparent)}.diff-hidden-text{display:inline-block;width:0;overflow:hidden}.diff-line-equal{opacity:50%}.diff-line-spacer{text-align:center;background-color:var(--sys-color-surface5)}\n/*# sourceURL=diffView.css */\n");const s={deletions:"Deletion:",additions:"Addition:",changesDiffViewer:"Changes diff viewer",SkippingDMatchingLines:"( … Skipping {PH1} matching lines … )"},a=e.i18n.registerUIStrings("ui/components/diff_view/DiffView.ts",s),l=e.i18n.getLocalizedString.bind(void 0,a);function d(e){let t=0,n=0;const r=[],o=[],a=[];for(let t=0;t<e.length;++t){const n=e[t];switch(n[0]){case i.Diff.Operation.Equal:a.push(...d(n[1],0===t,t===e.length-1)),r.push(...n[1]),o.push(...n[1]);break;case i.Diff.Operation.Insert:for(const e of n[1])a.push(c(e,"addition"));o.push(...n[1]);break;case i.Diff.Operation.Delete:if(r.push(...n[1]),e[t+1]&&e[t+1][0]===i.Diff.Operation.Insert)t++,a.push(...f(n[1].join("\n"),e[t][1].join("\n"))),o.push(...e[t][1]);else for(const e of n[1])a.push(c(e,"deletion"))}}return{originalLines:r,currentLines:o,rows:a};function d(e,i,r){const o=[];if(!i){for(let i=0;i<3&&i<e.length;i++)o.push(c(e[i],"equal"));e.length>7&&!r&&o.push(c(l(s.SkippingDMatchingLines,{PH1:e.length-6}),"spacer"))}if(!r){const r=Math.max(e.length-3-1,i?0:3);let s=e.length-3-1;i||(s-=3),s>0&&(n+=s,t+=s);for(let i=r;i<e.length;i++)o.push(c(e[i],"equal"))}return o}function f(e,t){const n=i.Diff.DiffWrapper.charDiff(e,t,!0),r=[c("","deletion")],o=[c("","addition")];for(const e of n){const t=e[1],n=e[0],s=n===i.Diff.Operation.Equal?"":"inner-diff",a=t.split("\n");for(let e=0;e<a.length;e++)e>0&&n!==i.Diff.Operation.Insert&&r.push(c("","deletion")),e>0&&n!==i.Diff.Operation.Delete&&o.push(c("","addition")),a[e]&&(n!==i.Diff.Operation.Insert&&r[r.length-1].tokens.push({text:a[e],className:s}),n!==i.Diff.Operation.Delete&&o[o.length-1].tokens.push({text:a[e],className:s}))}return r.concat(o)}function c(e,i){return"addition"===i&&t++,"deletion"===i&&n++,"equal"===i&&(n++,t++),{originalLineNumber:n,currentLineNumber:t,tokens:e?[{text:e,className:"inner-diff"}]:[],type:i}}}function f(e){const i=new Map;for(let t=0,n=0;n<e.length;n++)i.set(n+1,t),t+=e[n].length+1;return i}class c{originalHighlighter;originalMap;currentHighlighter;currentMap;constructor(e,i,t,n){this.originalHighlighter=e,this.originalMap=i,this.currentHighlighter=t,this.currentMap=n}#e(e){return n.html`
      <div class="diff-listing" aria-label=${l(s.changesDiffViewer)}>
        ${e.map((e=>this.#i(e)))}
      </div>`}#i(e){const i="equal"===e.type||"deletion"===e.type?String(e.originalLineNumber):"",r="equal"===e.type||"addition"===e.type?String(e.currentLineNumber):"";let o="",a="diff-line-marker",d=null;return"addition"===e.type?(o="+",a+=" diff-line-addition",d=n.html`<span class="diff-hidden-text">${l(s.additions)}</span>`):"deletion"===e.type&&(o="-",a+=" diff-line-deletion",d=n.html`<span class="diff-hidden-text">${l(s.deletions)}</span>`),n.html`
      <div class="diff-line-number" aria-hidden="true">${i}</div>
      <div class="diff-line-number" aria-hidden="true">${r}</div>
      <div class=${a} aria-hidden="true">${o}</div>
      <div class="diff-line-content diff-line-${e.type}" data-line-number=${r} jslog=${t.link("changes.reveal-source").track({click:!0})}>${d}${this.#t(e)}</div>`}#t(e){if("spacer"===e.type)return e.tokens.map((e=>n.html`${e.text}`));const[i,t]="deletion"===e.type?[this.originalHighlighter,this.originalMap.get(e.originalLineNumber)]:[this.currentHighlighter,this.currentMap.get(e.currentLineNumber)],r=[];let o=t;for(const t of e.tokens){const e=[];i.highlightRange(o,o+t.text.length,((i,t)=>{e.push(t?n.html`<span class=${t}>${i}</span>`:i)})),r.push(t.className?n.html`<span class=${t.className}>${e}</span>`:n.html`${e}`),o+=t.text.length}return r}static async render(e,i,t){const{originalLines:o,currentLines:s,rows:a}=d(e),l=new c(await r.CodeHighlighter.create(o.join("\n"),i),f(o),await r.CodeHighlighter.create(s.join("\n"),i),f(s));n.render(l.#e(a),t,{host:this})}}class h extends HTMLElement{static litTagName=n.literal`devtools-diff-view`;#n=this.attachShadow({mode:"open"});loaded;constructor(e){super(),this.#n.adoptedStyleSheets=[o,r.Style.default],this.loaded=e?c.render(e.diff,e.mimeType,this.#n):Promise.resolve()}set data(e){this.loaded=c.render(e.diff,e.mimeType,this.#n)}}customElements.define("devtools-diff-view",h);var p=Object.freeze({__proto__:null,buildDiffRows:d,DiffView:h});export{p as DiffView};
