import*as e from"../../third_party/puppeteer/puppeteer.js";class n{#e;constructor(e){this.#e=e}send(e){this.#e.sendRawMessage(e)}close(){this.#e.disconnect()}set onmessage(e){this.#e.setOnMessage((n=>{const s=n;if(s.sessionId)return e(JSON.stringify({...s,sessionId:s.sessionId===this.#e.getSessionId()?void 0:s.sessionId}))}))}set onclose(e){const n=this.#e.getOnDisconnect();this.#e.setOnDisconnect((s=>{n&&n(s),e&&e()}))}}class s extends e.Connection{async onMessage(e){const n=JSON.parse(e);n.sessionId&&!this._sessions.has(n.sessionId)||super.onMessage(e)}}var o=Object.freeze({__proto__:null,PuppeteerConnectionHelper:class{static async connectPuppeteerToConnectionViaTab(o){const{connection:t,rootTargetId:i,isPageTargetCallback:c}=o,a=new n(t),r=new s("",a),p=e.Browser._create("chrome",r,[],!1,void 0,void 0,void 0,void 0,(e=>c(e._getTargetInfo())),!1),[,d]=await Promise.all([r._createSession({targetId:i},!0),p]);await d.waitForTarget((e=>"page"===e.type()));return{page:(await d.pages())[0],browser:d,puppeteerConnection:r}}}});export{o as PuppeteerConnection};
