import*as e from"../../models/trace/trace.js";let t=null;class n extends Event{state;updateType;options;static eventName="traceboundsstatechanged";constructor(e,t,i={shouldAnimate:!1}){super(n.eventName,{composed:!0,bubbles:!0}),this.state=e,this.updateType=t,this.options=i}}class i extends EventTarget{static instance(e={forceNew:null}){const n=Boolean(e.forceNew);return t&&!n||(t=new i),t}static removeInstance(){t=null}#e=null;constructor(){super()}resetWithNewBounds(e){return this.#e={entireTraceBounds:e,minimapTraceBounds:e,timelineTraceWindow:e},this.dispatchEvent(new n(this.state(),"RESET")),this}state(){if(null===this.#e)return null;const t=e.Helpers.Timing.traceWindowMilliSeconds(this.#e.entireTraceBounds),n=e.Helpers.Timing.traceWindowMilliSeconds(this.#e.minimapTraceBounds),i=e.Helpers.Timing.traceWindowMilliSeconds(this.#e.timelineTraceWindow);return{micro:this.#e,milli:{entireTraceBounds:t,minimapTraceBounds:n,timelineTraceWindow:i}}}setMiniMapBounds(e){if(!this.#e)return void console.error("TraceBounds.setMiniMapBounds could not set bounds because there is no existing trace window set.");const t=this.#e.minimapTraceBounds;e.min===t.min&&e.max===t.max||e.range<1e3||(this.#e.minimapTraceBounds=e,this.dispatchEvent(new n(this.state(),"MINIMAP_BOUNDS")))}setTimelineVisibleWindow(t,i={shouldAnimate:!1}){if(!this.#e)return void console.error("TraceBounds.setTimelineVisibleWindow could not set bounds because there is no existing trace window set.");const s=this.#e.timelineTraceWindow;t.min===s.min&&t.max===s.max||t.range<1e3||(t.min=e.Types.Timing.MicroSeconds(Math.max(this.#e.minimapTraceBounds.min,t.min)),t.max=e.Types.Timing.MicroSeconds(Math.min(this.#e.minimapTraceBounds.max,t.max)),this.#e.timelineTraceWindow=t,this.dispatchEvent(new n(this.state(),"VISIBLE_WINDOW",{shouldAnimate:i.shouldAnimate})))}}var s=Object.freeze({__proto__:null,StateChangedEvent:n,onChange:function(e){i.instance().addEventListener(n.eventName,e)},removeListener:function(e){i.instance().removeEventListener(n.eventName,e)},BoundsManager:i});export{s as TraceBounds};
