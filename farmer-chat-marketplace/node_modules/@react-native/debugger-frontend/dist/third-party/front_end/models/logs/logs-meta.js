import*as e from"../../core/common/common.js";import*as o from"../../core/i18n/i18n.js";const r={preserveLog:"Preserve log",preserve:"preserve",clear:"clear",reset:"reset",preserveLogOnPageReload:"Preserve log on page reload / navigation",doNotPreserveLogOnPageReload:"Do not preserve log on page reload / navigation",recordNetworkLog:"Record network log"},t=o.i18n.registerUIStrings("models/logs/logs-meta.ts",r),g=o.i18n.getLazilyComputedLocalizedString.bind(void 0,t);e.Settings.registerSettingExtension({category:"NETWORK",title:g(r.preserveLog),settingName:"network-log.preserve-log",settingType:"boolean",defaultValue:!1,tags:[g(r.preserve),g(r.clear),g(r.reset)],options:[{value:!0,title:g(r.preserveLogOnPageReload)},{value:!1,title:g(r.doNotPreserveLogOnPageReload)}]}),e.Settings.registerSettingExtension({category:"NETWORK",title:g(r.recordNetworkLog),settingName:"network-log.record-log",settingType:"boolean",defaultValue:!0,storageType:"Session"});
