import*as e from"../../core/sdk/sdk.js";import*as t from"../../core/common/common.js";import*as s from"../../core/platform/platform.js";import*as r from"../text_utils/text_utils.js";import*as o from"../../core/i18n/i18n.js";class i{custom;constructor(e){if(!e||"object"!=typeof e)throw"First parameter is expected to be an object";this.custom=new Map}static safeDate(e){const t=new Date(e);if(!Number.isNaN(t.getTime()))return t;throw"Invalid date format"}static safeNumber(e){const t=Number(e);if(!Number.isNaN(t))return t;throw"Casting to number results in NaN"}static optionalNumber(e){return void 0!==e?i.safeNumber(e):void 0}static optionalString(e){return void 0!==e?String(e):void 0}customAsString(e){const t=this.custom.get(e);if(t)return String(t)}customAsNumber(e){const t=this.custom.get(e);if(!t)return;const s=Number(t);return Number.isNaN(s)?void 0:s}customAsArray(e){const t=this.custom.get(e);if(t)return Array.isArray(t)?t:void 0}customInitiator(){return this.custom.get("initiator")}}class n extends i{version;creator;browser;pages;entries;comment;constructor(e){if(super(e),this.version=String(e.version),this.creator=new a(e.creator),this.browser=e.browser?new a(e.browser):void 0,this.pages=Array.isArray(e.pages)?e.pages.map((e=>new c(e))):[],!Array.isArray(e.entries))throw"log.entries is expected to be an array";this.entries=e.entries.map((e=>new m(e))),this.comment=i.optionalString(e.comment)}}class a extends i{name;version;comment;constructor(e){super(e),this.name=String(e.name),this.version=String(e.version),this.comment=i.optionalString(e.comment)}}class c extends i{startedDateTime;id;title;pageTimings;comment;constructor(e){super(e),this.startedDateTime=i.safeDate(e.startedDateTime),this.id=String(e.id),this.title=String(e.title),this.pageTimings=new u(e.pageTimings),this.comment=i.optionalString(e.comment)}}class u extends i{onContentLoad;onLoad;comment;constructor(e){super(e),this.onContentLoad=i.optionalNumber(e.onContentLoad),this.onLoad=i.optionalNumber(e.onLoad),this.comment=i.optionalString(e.comment)}}class m extends i{pageref;startedDateTime;time;request;response;cache;timings;serverIPAddress;connection;comment;constructor(e){super(e),this.pageref=i.optionalString(e.pageref),this.startedDateTime=i.safeDate(e.startedDateTime),this.time=i.safeNumber(e.time),this.request=new d(e.request),this.response=new p(e.response),this.cache={},this.timings=new f(e.timings),this.serverIPAddress=i.optionalString(e.serverIPAddress),this.connection=i.optionalString(e.connection),this.comment=i.optionalString(e.comment),this.custom.set("fromCache",i.optionalString(e._fromCache)),this.custom.set("initiator",this.importInitiator(e._initiator)),this.custom.set("priority",i.optionalString(e._priority)),this.custom.set("resourceType",i.optionalString(e._resourceType)),this.custom.set("webSocketMessages",this.importWebSocketMessages(e._webSocketMessages))}importInitiator(e){if("object"==typeof e)return new q(e)}importWebSocketMessages(e){if(!Array.isArray(e))return;const t=[];for(const s of e){if("object"!=typeof s)return;t.push(new w(s))}return t}}class d extends i{method;url;httpVersion;cookies;headers;queryString;postData;headersSize;bodySize;comment;constructor(e){super(e),this.method=String(e.method),this.url=String(e.url),this.httpVersion=String(e.httpVersion),this.cookies=Array.isArray(e.cookies)?e.cookies.map((e=>new l(e))):[],this.headers=Array.isArray(e.headers)?e.headers.map((e=>new h(e))):[],this.queryString=Array.isArray(e.queryString)?e.queryString.map((e=>new g(e))):[],this.postData=e.postData?new S(e.postData):void 0,this.headersSize=i.safeNumber(e.headersSize),this.bodySize=i.safeNumber(e.bodySize),this.comment=i.optionalString(e.comment)}}class p extends i{status;statusText;httpVersion;cookies;headers;content;redirectURL;headersSize;bodySize;comment;constructor(e){super(e),this.status=i.safeNumber(e.status),this.statusText=String(e.statusText),this.httpVersion=String(e.httpVersion),this.cookies=Array.isArray(e.cookies)?e.cookies.map((e=>new l(e))):[],this.headers=Array.isArray(e.headers)?e.headers.map((e=>new h(e))):[],this.content=new b(e.content),this.redirectURL=String(e.redirectURL),this.headersSize=i.safeNumber(e.headersSize),this.bodySize=i.safeNumber(e.bodySize),this.comment=i.optionalString(e.comment),this.custom.set("transferSize",i.optionalNumber(e._transferSize)),this.custom.set("error",i.optionalString(e._error)),this.custom.set("fetchedViaServiceWorker",Boolean(e._fetchedViaServiceWorker)),this.custom.set("responseCacheStorageCacheName",i.optionalString(e._responseCacheStorageCacheName)),this.custom.set("serviceWorkerResponseSource",i.optionalString(e._serviceWorkerResponseSource))}}class l extends i{name;value;path;domain;expires;httpOnly;secure;comment;constructor(e){super(e),this.name=String(e.name),this.value=String(e.value),this.path=i.optionalString(e.path),this.domain=i.optionalString(e.domain),this.expires=e.expires?i.safeDate(e.expires):void 0,this.httpOnly=void 0!==e.httpOnly?Boolean(e.httpOnly):void 0,this.secure=void 0!==e.secure?Boolean(e.secure):void 0,this.comment=i.optionalString(e.comment)}}class h extends i{name;value;comment;constructor(e){super(e),this.name=String(e.name),this.value=String(e.value),this.comment=i.optionalString(e.comment)}}class g extends i{name;value;comment;constructor(e){super(e),this.name=String(e.name),this.value=String(e.value),this.comment=i.optionalString(e.comment)}}class S extends i{mimeType;params;text;comment;constructor(e){super(e),this.mimeType=String(e.mimeType),this.params=Array.isArray(e.params)?e.params.map((e=>new y(e))):[],this.text=String(e.text),this.comment=i.optionalString(e.comment)}}class y extends i{name;value;fileName;contentType;comment;constructor(e){super(e),this.name=String(e.name),this.value=i.optionalString(e.value),this.fileName=i.optionalString(e.fileName),this.contentType=i.optionalString(e.contentType),this.comment=i.optionalString(e.comment)}}class b extends i{size;compression;mimeType;text;encoding;comment;constructor(e){super(e),this.size=i.safeNumber(e.size),this.compression=i.optionalNumber(e.compression),this.mimeType=String(e.mimeType),this.text=i.optionalString(e.text),this.encoding=i.optionalString(e.encoding),this.comment=i.optionalString(e.comment)}}class f extends i{blocked;dns;connect;send;wait;receive;ssl;comment;constructor(e){super(e),this.blocked=i.optionalNumber(e.blocked),this.dns=i.optionalNumber(e.dns),this.connect=i.optionalNumber(e.connect),this.send=i.safeNumber(e.send),this.wait=i.safeNumber(e.wait),this.receive=i.safeNumber(e.receive),this.ssl=i.optionalNumber(e.ssl),this.comment=i.optionalString(e.comment),this.custom.set("blocked_queueing",i.optionalNumber(e._blocked_queueing)),this.custom.set("blocked_proxy",i.optionalNumber(e._blocked_proxy)),this.custom.set("workerStart",i.optionalNumber(e._workerStart)),this.custom.set("workerReady",i.optionalNumber(e._workerReady)),this.custom.set("workerFetchStart",i.optionalNumber(e._workerFetchStart)),this.custom.set("workerRespondWithSettled",i.optionalNumber(e._workerRespondWithSettled))}}class q extends i{type;url;lineNumber;requestId;stack;constructor(e){super(e),this.type=i.optionalString(e.type)??"other",this.url=i.optionalString(e.url),this.lineNumber=i.optionalNumber(e.lineNumber),this.requestId=i.optionalString(e.requestId),e.stack&&(this.stack=new k(e.stack))}}class k extends i{description;callFrames;parent;parentId;constructor(e){super(e),this.callFrames=Array.isArray(e.callFrames)?e.callFrames.map((e=>e?new T(e):null)).filter(Boolean):[],e.parent&&(this.parent=new k(e.parent)),this.description=i.optionalString(e.description);const t=e.parentId;t&&(this.parentId={id:i.optionalString(t.id)??"",debuggerId:i.optionalString(t.debuggerId)})}}class T extends i{functionName;scriptId;url="";lineNumber=-1;columnNumber=-1;constructor(e){super(e),this.functionName=i.optionalString(e.functionName)??"",this.scriptId=i.optionalString(e.scriptId)??"",this.url=i.optionalString(e.url)??"",this.lineNumber=i.optionalNumber(e.lineNumber)??-1,this.columnNumber=i.optionalNumber(e.columnNumber)??-1}}class w extends i{time;opcode;data;type;constructor(e){super(e),this.time=i.optionalNumber(e.time),this.opcode=i.optionalNumber(e.opcode),this.data=i.optionalString(e.data),this.type=i.optionalString(e.type)}}var v=Object.freeze({__proto__:null,HARRoot:class extends i{log;constructor(e){super(e),this.log=new n(e.log)}},HARLog:n,HARPage:c,HAREntry:m,HARParam:y,HARTimings:f,HARInitiator:q,HARStack:k,HARCallFrame:T});class N{static requestsFromHARLog(t){const s=new Map;for(const e of t.pages)s.set(e.id,e);t.entries.sort(((e,t)=>e.startedDateTime.valueOf()-t.startedDateTime.valueOf()));const r=new Map,o=[];for(const i of t.entries){const t=i.pageref;let n=t?r.get(t):void 0;const a=n?n.mainRequest.url():i.request.url;let c=null;const u=i.customInitiator();u&&(c={type:u.type,url:u.url,lineNumber:u.lineNumber,requestId:u.requestId,stack:u.stack});const m=e.NetworkRequest.NetworkRequest.createWithoutBackendRequest("har-"+o.length,i.request.url,a,c),d=t?s.get(t):void 0;!n&&t&&d&&(n=N.buildPageLoad(d,m),r.set(t,n)),N.fillRequestFromHAREntry(m,i,n),n&&n.bindRequest(m),o.push(m)}return o}static buildPageLoad(t,s){const r=new e.PageLoad.PageLoad(s);return r.startTime=t.startedDateTime.valueOf(),r.contentLoadTime=1e3*Number(t.pageTimings.onContentLoad),r.loadTime=1e3*Number(t.pageTimings.onLoad),r}static fillRequestFromHAREntry(t,o,i){o.request.postData?t.setRequestFormData(!0,o.request.postData.text):t.setRequestFormData(!1,null),t.connectionId=o.connection||"",t.requestMethod=o.request.method,t.setRequestHeaders(o.request.headers),o.response.content.mimeType&&"x-unknown"!==o.response.content.mimeType&&(t.mimeType=o.response.content.mimeType),t.responseHeaders=o.response.headers,t.statusCode=o.response.status,t.statusText=o.response.statusText;let n=o.response.httpVersion.toLowerCase();"http/2.0"===n&&(n="h2"),t.protocol=n.replace(/^http\/2\.0?\+quic/,"http/2+quic");const a=o.startedDateTime.getTime()/1e3;t.setIssueTime(a,a);const c=o.response.content.size>0?o.response.content.size:0,u=o.response.headersSize>0?o.response.headersSize:0,m=o.response.bodySize>0?o.response.bodySize:0;t.resourceSize=c||u+m;let d=o.response.customAsNumber("transferSize");void 0===d&&(d=o.response.headersSize+o.response.bodySize),t.setTransferSize(d>=0?d:0);const p=o.customAsString("fromCache");"memory"===p?t.setFromMemoryCache():"disk"===p&&t.setFromDiskCache();const l=o.response.content.text,h="base64"===o.response.content.encoding,{mimeType:g,charset:S}=s.MimeType.parseContentType(o.response.content.mimeType);t.setContentDataProvider((async()=>new r.ContentData.ContentData(l??"",h,g??"",S??void 0))),N.setupTiming(t,a,o.time,o.timings),t.setRemoteAddress(o.serverIPAddress||"",80),t.setResourceType(N.getResourceType(t,o,i));const y=o.customAsString("priority");y&&Protocol.Network.ResourcePriority.hasOwnProperty(y)&&t.setPriority(y);const b=o.customAsArray("webSocketMessages");if(b)for(const s of b){if(void 0===s.time)continue;if(!Object.values(e.NetworkRequest.WebSocketFrameType).includes(s.type))continue;if(void 0===s.opcode)continue;if(void 0===s.data)continue;const r=s.type===e.NetworkRequest.WebSocketFrameType.Send;t.addFrame({time:s.time,text:s.data,opCode:s.opcode,mask:r,type:s.type})}t.fetchedViaServiceWorker=Boolean(o.response.custom.get("fetchedViaServiceWorker"));const f=o.response.customAsString("serviceWorkerResponseSource");if(f){new Set(["cache-storage","fallback-code","http-cache","network"]).has(f)&&t.setServiceWorkerResponseSource(f)}const q=o.response.customAsString("responseCacheStorageCacheName");q&&t.setResponseCacheStorageCacheName(q),t.finished=!0}static getResourceType(e,s,r){const o=s.customAsString("resourceType");if(o){const e=t.ResourceType.ResourceType.fromName(o);if(e)return e}if(r&&r.mainRequest===e)return t.ResourceType.resourceTypes.Document;const i=t.ResourceType.ResourceType.fromMimeType(s.response.content.mimeType);if(i!==t.ResourceType.resourceTypes.Other)return i;const n=t.ResourceType.ResourceType.fromURL(s.request.url);return n||t.ResourceType.resourceTypes.Other}static setupTiming(e,t,s,r){function o(e){return void 0===e||e<0?-1:(i+=e,i)}let i=r.blocked&&r.blocked>=0?r.blocked:0;const n=r.customAsNumber("blocked_proxy")||-1,a=r.customAsNumber("blocked_queueing")||-1;i>0&&a>0&&(i-=a);const c=r.ssl&&r.ssl>=0?r.ssl:0;r.connect&&r.connect>0&&(r.connect-=c);const u={proxyStart:n>0?i-n:-1,proxyEnd:n>0?i:-1,requestTime:t+(a>0?a:0)/1e3,dnsStart:r.dns&&r.dns>=0?i:-1,dnsEnd:o(r.dns),connectStart:r.connect&&r.connect>=0?i:-1,connectEnd:o(r.connect)+c,sslStart:r.ssl&&r.ssl>=0?i:-1,sslEnd:o(r.ssl),workerStart:r.customAsNumber("workerStart")||-1,workerReady:r.customAsNumber("workerReady")||-1,workerFetchStart:r.customAsNumber("workerFetchStart")||-1,workerRespondWithSettled:r.customAsNumber("workerRespondWithSettled")||-1,sendStart:r.send>=0?i:-1,sendEnd:o(r.send),pushStart:0,pushEnd:0,receiveHeadersStart:r.wait&&r.wait>=0?i:-1,receiveHeadersEnd:o(r.wait)};o(r.receive),e.timing=u,e.endTime=t+Math.max(s,i)/1e3}}var R=Object.freeze({__proto__:null,Importer:N});class x{static pseudoWallTime(e,t){return new Date(1e3*e.pseudoWallTime(t))}static async build(e){const t=new x,s=[];for(const t of e)s.push(_.build(t));const r=await Promise.all(s);return{version:"1.2",creator:t.creator(),pages:t.buildPages(e),entries:r}}creator(){const e=/AppleWebKit\/([^ ]+)/.exec(window.navigator.userAgent);return{name:"WebInspector",version:e?e[1]:"n/a"}}buildPages(t){const s=new Set,r=[];for(let o=0;o<t.length;++o){const i=t[o],n=e.PageLoad.PageLoad.forRequest(i);n&&!s.has(n.id)&&(s.add(n.id),r.push(this.convertPage(n,i)))}return r}convertPage(e,t){return{startedDateTime:x.pseudoWallTime(t,e.startTime).toJSON(),id:"page_"+e.id,title:e.url,pageTimings:{onContentLoad:this.pageEventTime(e,e.contentLoadTime),onLoad:this.pageEventTime(e,e.loadTime)}}}pageEventTime(e,t){const s=e.startTime;return-1===t||-1===s?-1:_.toMilliseconds(t-s)}}class _{request;constructor(e){this.request=e}static toMilliseconds(e){return-1===e?-1:1e3*e}static async build(s){const r=new _(s);let o=r.request.remoteAddress();const i=o.lastIndexOf(":");-1!==i&&(o=o.substr(0,i));const n=r.buildTimings();let a=0;for(const e of[n.blocked,n.dns,n.connect,n.send,n.wait,n.receive])a+=Math.max(e,0);const c=r.request.initiator();let u=null;c&&(u={type:c.type},void 0!==c.url&&(u.url=c.url),void 0!==c.requestId&&(u.requestId=c.requestId),void 0!==c.lineNumber&&(u.lineNumber=c.lineNumber),c.stack&&(u.stack=c.stack));const m={_fromCache:void 0,_initiator:u,_priority:r.request.priority(),_resourceType:r.request.resourceType().name(),_webSocketMessages:void 0,cache:{},connection:void 0,pageref:void 0,request:await r.buildRequest(),response:r.buildResponse(),serverIPAddress:o.replace(/\[\]/g,""),startedDateTime:x.pseudoWallTime(r.request,r.request.issueTime()).toJSON(),time:a,timings:n};r.request.cached()?m._fromCache=r.request.cachedInMemory()?"memory":"disk":delete m._fromCache,"0"!==r.request.connectionId?m.connection=r.request.connectionId:delete m.connection;const d=e.PageLoad.PageLoad.forRequest(r.request);if(d?m.pageref="page_"+d.id:delete m.pageref,r.request.resourceType()===t.ResourceType.resourceTypes.WebSocket){const e=[];for(const t of r.request.frames())e.push({type:t.type,time:t.time,opcode:t.opCode,data:t.text});m._webSocketMessages=e}else delete m._webSocketMessages;return m}async buildRequest(){const e=this.request.requestHeadersText(),t={method:this.request.requestMethod,url:this.buildRequestURL(this.request.url()),httpVersion:this.request.requestHttpVersion(),headers:this.request.requestHeaders(),queryString:this.buildParameters(this.request.queryParameters||[]),cookies:this.buildCookies(this.request.includedRequestCookies().map((e=>e.cookie))),headersSize:e?e.length:-1,bodySize:await this.requestBodySize(),postData:void 0},s=await this.buildPostData();return s?t.postData=s:delete t.postData,t}buildResponse(){const e=this.request.responseHeadersText;return{status:this.request.statusCode,statusText:this.request.statusText,httpVersion:this.request.responseHttpVersion(),headers:this.request.responseHeaders,cookies:this.buildCookies(this.request.responseCookies),content:this.buildContent(),redirectURL:this.request.responseHeaderValue("Location")||"",headersSize:e?e.length:-1,bodySize:this.responseBodySize,_transferSize:this.request.transferSize,_error:this.request.localizedFailDescription,_fetchedViaServiceWorker:this.request.fetchedViaServiceWorker,_responseCacheStorageCacheName:this.request.getResponseCacheStorageCacheName(),_serviceWorkerResponseSource:this.request.serviceWorkerResponseSource()}}buildContent(){const e={size:this.request.resourceSize,mimeType:this.request.mimeType||"x-unknown",compression:void 0},t=this.responseCompression;return"number"==typeof t?e.compression=t:delete e.compression,e}buildTimings(){const e=this.request.timing,t=this.request.issueTime(),s=this.request.startTime,r={blocked:-1,dns:-1,ssl:-1,connect:-1,send:0,wait:0,receive:0,_blocked_queueing:-1,_blocked_proxy:void 0},o=t<s?s-t:-1;r.blocked=_.toMilliseconds(o),r._blocked_queueing=_.toMilliseconds(o);let i=0;if(e){const t=d([e.dnsStart,e.connectStart,e.sendStart]);t!==1/0&&(r.blocked+=t),-1!==e.proxyEnd&&(r._blocked_proxy=e.proxyEnd-e.proxyStart),r._blocked_proxy&&r._blocked_proxy>r.blocked&&(r.blocked=r._blocked_proxy);const s=e.dnsEnd>=0?t:0,o=e.dnsEnd>=0?e.dnsEnd:-1;r.dns=o-s;const n=e.sslEnd>0?e.sslStart:0,a=e.sslEnd>0?e.sslEnd:-1;r.ssl=a-n;const c=e.connectEnd>=0?d([o,t]):0,u=e.connectEnd>=0?e.connectEnd:-1;r.connect=u-c;const m=e.sendEnd>=0?Math.max(u,o,t):0,p=e.sendEnd>=0?e.sendEnd:0;r.send=p-m,r.send<0&&(r.send=0),i=Math.max(p,u,a,o,t,0),r._workerStart=e.workerStart,r._workerReady=e.workerReady,r._workerFetchStart=e.workerFetchStart,r._workerRespondWithSettled=e.workerRespondWithSettled}else if(-1===this.request.responseReceivedTime)return r.blocked=_.toMilliseconds(this.request.endTime-t),r;const n=e?e.requestTime:s,a=i,c=_.toMilliseconds(this.request.responseReceivedTime-n);r.wait=c-a;const u=c,m=_.toMilliseconds(this.request.endTime-n);return r.receive=Math.max(m-u,0),r;function d(e){return e.reduce(((e,t)=>t>=0&&t<e?t:e),1/0)}}async buildPostData(){const e=await this.request.requestFormData();if(!e)return null;const t={mimeType:this.request.requestContentType()||"",text:e,params:void 0},s=await this.request.formParameters();return s?t.params=this.buildParameters(s):delete t.params,t}buildParameters(e){return e.slice()}buildRequestURL(e){return t.ParsedURL.ParsedURL.split(e,"#",2)[0]}buildCookies(e){return e.map(this.buildCookie.bind(this))}buildCookie(e){const t={name:e.name(),value:e.value(),path:e.path(),domain:e.domain(),expires:e.expiresDate(x.pseudoWallTime(this.request,this.request.startTime)),httpOnly:e.httpOnly(),secure:e.secure(),sameSite:void 0,partitionKey:void 0};return e.sameSite()?t.sameSite=e.sameSite():delete t.sameSite,e.partitionKey()?t.partitionKey=e.partitionKey():delete t.partitionKey,t}async requestBodySize(){const e=await this.request.requestFormData();return e?(new TextEncoder).encode(e).length:0}get responseBodySize(){return this.request.cached()||304===this.request.statusCode?0:this.request.responseHeadersText?this.request.transferSize-this.request.responseHeadersText.length:-1}get responseCompression(){if(!this.request.cached()&&304!==this.request.statusCode&&206!==this.request.statusCode&&this.request.responseHeadersText)return this.request.resourceSize-this.responseBodySize}}var C=Object.freeze({__proto__:null,Log:x,Entry:_});const A={collectingContent:"Collecting content…",writingFile:"Writing file…"},z=o.i18n.registerUIStrings("models/har/Writer.ts",A),D=o.i18n.getLocalizedString.bind(void 0,z);class P{static async write(e,s,r){const o=new t.Progress.CompositeProgress(r),i=await P.harStringForRequests(s,o);r.isCanceled()||await P.writeToStream(e,o,i)}static async harStringForRequests(e,t){const o=t.createSubProgress();o.setTitle(D(A.collectingContent)),o.setTotalWork(e.length),e.sort(((e,t)=>e.issueTime()-t.issueTime()));const i=await x.build(e),n=[];for(let t=0;t<e.length;t++){const s=e[t].requestContentData();n.push(s.then(a.bind(null,i.entries[t])))}return await Promise.all(n),o.done(),o.isCanceled()?"":JSON.stringify({log:i},null,W);function a(e,t){o.incrementWorked();const i=r.ContentData.ContentData.asDeferredContent(t);let n=i.isEncoded;if(null!==i.content){let t=i.content;t&&!n&&function(e){for(let s=0;s<e.length;s++)if(!((t=e.charCodeAt(s))<55296||t>=57344&&t<64976||t>65007&&t<=1114111&&65534!=(65534&t)))return!0;var t;return!1}(t)&&(t=s.StringUtilities.toBase64(t),n=!0),e.response.content.text=t}n&&(e.response.content.encoding="base64")}}static async writeToStream(e,t,s){const r=t.createSubProgress();r.setTitle(D(A.writingFile)),r.setTotalWork(s.length);for(let t=0;t<s.length&&!r.isCanceled();t+=I){const o=s.substr(t,I);await e.write(o),r.incrementWorked(o.length)}r.done()}}const W=2,I=1e5;var L=Object.freeze({__proto__:null,Writer:P,jsonIndent:W,chunkSize:I});export{v as HARFormat,R as Importer,C as Log,L as Writer};
