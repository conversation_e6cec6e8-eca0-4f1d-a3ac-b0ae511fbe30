import React from 'react';
import { Ionicons } from '@expo/vector-icons';

interface IconProps {
  name: string;
  size?: number;
  color?: string;
}

// Icon mapping from lucide names to Ionicons names
const iconMap: Record<string, string> = {
  // Basic icons
  'Plus': 'add',
  'ShoppingBag': 'bag',
  'MessageCircle': 'chatbubble',
  'BarChart': 'bar-chart',
  'TrendingUp': 'trending-up',

  // Navigation icons
  'Home': 'home',
  'Search': 'search',
  'User': 'person',
  'Settings': 'settings',
  'Menu': 'menu',
  'ArrowLeft': 'arrow-back',
  'ArrowRight': 'arrow-forward',
  'ChevronLeft': 'chevron-back',
  'ChevronRight': 'chevron-forward',
  'ChevronDown': 'chevron-down',
  'ChevronUp': 'chevron-up',

  // Action icons
  'Edit': 'create',
  'Delete': 'trash',
  'Save': 'checkmark',
  'Cancel': 'close',
  'Send': 'send',
  'Share': 'share',
  'Download': 'download',
  'Upload': 'cloud-upload',
  'Copy': 'copy',
  'Cut': 'cut',

  // Content icons
  'Heart': 'heart',
  'Star': 'star',
  'Eye': 'eye',
  'EyeOff': 'eye-off',
  'ThumbsUp': 'thumbs-up',
  'ThumbsDown': 'thumbs-down',
  'Flag': 'flag',
  'Bookmark': 'bookmark',

  // Communication icons
  'Mail': 'mail',
  'Phone': 'call',
  'MessageSquare': 'chatbubble',
  'Bell': 'notifications',
  'BellOff': 'notifications-off',

  // Media icons
  'Play': 'play',
  'Pause': 'pause',
  'Stop': 'stop',
  'Volume2': 'volume-high',
  'VolumeX': 'volume-mute',
  'Camera': 'camera',
  'Image': 'image',
  'Video': 'videocam',

  // File icons
  'File': 'document',
  'FileText': 'document-text',
  'Folder': 'folder',
  'FolderOpen': 'folder-open',

  // Status icons
  'Check': 'checkmark',
  'X': 'close',
  'AlertCircle': 'alert-circle',
  'Info': 'information-circle',
  'HelpCircle': 'help-circle',
  'CheckCircle': 'checkmark-circle',
  'XCircle': 'close-circle',

  // Commerce icons
  'ShoppingCart': 'cart',
  'CreditCard': 'card',
  'DollarSign': 'cash',
  'Package': 'cube',
  'Truck': 'car',

  // Time icons
  'Clock': 'time',
  'Calendar': 'calendar',
  'CalendarDays': 'calendar',

  // Location icons
  'MapPin': 'location',
  'Navigation': 'navigate',
  'Compass': 'compass',

  // Weather icons
  'Sun': 'sunny',
  'Moon': 'moon',
  'Cloud': 'cloud',
  'CloudRain': 'rainy',

  // Tech icons
  'Wifi': 'wifi',
  'WifiOff': 'wifi-off',
  'Battery': 'battery-full',
  'Bluetooth': 'bluetooth',
  'Smartphone': 'phone-portrait',
  'Laptop': 'laptop',

  // Default fallback
  'default': 'ellipse'
};

export const Icon: React.FC<IconProps> = ({ name, size = 24, color = '#000' }) => {
  const ioniconsName = iconMap[name] || iconMap['default'];

  return (
    <Ionicons
      name={ioniconsName as any}
      size={size}
      color={color}
    />
  );
};

// Export individual icon components for backward compatibility
export const Plus = (props: Omit<IconProps, 'name'>) => <Icon name="Plus" {...props} />;
export const ShoppingBag = (props: Omit<IconProps, 'name'>) => <Icon name="ShoppingBag" {...props} />;
export const MessageCircle = (props: Omit<IconProps, 'name'>) => <Icon name="MessageCircle" {...props} />;
export const BarChart = (props: Omit<IconProps, 'name'>) => <Icon name="BarChart" {...props} />;
export const TrendingUp = (props: Omit<IconProps, 'name'>) => <Icon name="TrendingUp" {...props} />;
export const Home = (props: Omit<IconProps, 'name'>) => <Icon name="Home" {...props} />;
export const Search = (props: Omit<IconProps, 'name'>) => <Icon name="Search" {...props} />;
export const User = (props: Omit<IconProps, 'name'>) => <Icon name="User" {...props} />;
export const Settings = (props: Omit<IconProps, 'name'>) => <Icon name="Settings" {...props} />;
export const Menu = (props: Omit<IconProps, 'name'>) => <Icon name="Menu" {...props} />;
export const ArrowLeft = (props: Omit<IconProps, 'name'>) => <Icon name="ArrowLeft" {...props} />;
export const ArrowRight = (props: Omit<IconProps, 'name'>) => <Icon name="ArrowRight" {...props} />;
export const ChevronLeft = (props: Omit<IconProps, 'name'>) => <Icon name="ChevronLeft" {...props} />;
export const ChevronRight = (props: Omit<IconProps, 'name'>) => <Icon name="ChevronRight" {...props} />;
export const ChevronDown = (props: Omit<IconProps, 'name'>) => <Icon name="ChevronDown" {...props} />;
export const ChevronUp = (props: Omit<IconProps, 'name'>) => <Icon name="ChevronUp" {...props} />;
export const Edit = (props: Omit<IconProps, 'name'>) => <Icon name="Edit" {...props} />;
export const Delete = (props: Omit<IconProps, 'name'>) => <Icon name="Delete" {...props} />;
export const Save = (props: Omit<IconProps, 'name'>) => <Icon name="Save" {...props} />;
export const Cancel = (props: Omit<IconProps, 'name'>) => <Icon name="Cancel" {...props} />;
export const Send = (props: Omit<IconProps, 'name'>) => <Icon name="Send" {...props} />;
export const Share = (props: Omit<IconProps, 'name'>) => <Icon name="Share" {...props} />;
export const Heart = (props: Omit<IconProps, 'name'>) => <Icon name="Heart" {...props} />;
export const Star = (props: Omit<IconProps, 'name'>) => <Icon name="Star" {...props} />;
export const Eye = (props: Omit<IconProps, 'name'>) => <Icon name="Eye" {...props} />;
export const EyeOff = (props: Omit<IconProps, 'name'>) => <Icon name="EyeOff" {...props} />;
export const Mail = (props: Omit<IconProps, 'name'>) => <Icon name="Mail" {...props} />;
export const Phone = (props: Omit<IconProps, 'name'>) => <Icon name="Phone" {...props} />;
export const Bell = (props: Omit<IconProps, 'name'>) => <Icon name="Bell" {...props} />;
export const Check = (props: Omit<IconProps, 'name'>) => <Icon name="Check" {...props} />;
export const X = (props: Omit<IconProps, 'name'>) => <Icon name="X" {...props} />;
export const AlertCircle = (props: Omit<IconProps, 'name'>) => <Icon name="AlertCircle" {...props} />;
export const Info = (props: Omit<IconProps, 'name'>) => <Icon name="Info" {...props} />;
export const ShoppingCart = (props: Omit<IconProps, 'name'>) => <Icon name="ShoppingCart" {...props} />;
export const Clock = (props: Omit<IconProps, 'name'>) => <Icon name="Clock" {...props} />;
export const Calendar = (props: Omit<IconProps, 'name'>) => <Icon name="Calendar" {...props} />;
export const MapPin = (props: Omit<IconProps, 'name'>) => <Icon name="MapPin" {...props} />;

export default Icon;